package listener

import com.ctrip.dcs.application.command.ReDispatchSubmitExeCmd
import com.ctrip.dcs.application.listener.ReDispatchSubmitListener
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.entity.LogContentDTO
import com.ctrip.dcs.domain.dsporder.gateway.PurchaseOrderDispatchCaseGateway
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.infrastructure.adapter.redis.IDispatchModifyInfoCacheService
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory
import com.ctrip.igt.framework.common.jackson.JacksonUtil
import org.springframework.beans.factory.annotation.Autowired
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

import java.sql.Timestamp

class ReDispatchSubmitListenerTest extends Specification {

    def dspOrderRepository = Mock(DspOrderRepository)
    def purchaseOrderDispatchCaseGateway = Mock(PurchaseOrderDispatchCaseGateway)

    def cacheService = Mock(IDispatchModifyInfoCacheService)
    def recordRepository = Mock(DspOrderConfirmRecordRepository)
    def queryDspOrderService = Mock(QueryDspOrderService)
    def workBenchLogMessageFactory = Mock(WorkBenchLogMessageFactory)
    def workBenchLogGateway = Mock(WorkBenchLogGateway)
    def reDispatchSubmitExeCmd = Mock(ReDispatchSubmitExeCmd)

    def  messageProducer = Mock(MessageProviderService)

    def listener = new ReDispatchSubmitListener(
            dspOrderRepository: dspOrderRepository,
            purchaseOrderDispatchCaseGateway: purchaseOrderDispatchCaseGateway,
            cacheService:cacheService,
            recordRepository:recordRepository,
            queryDspOrderService:queryDspOrderService,
            workBenchLogMessageFactory:workBenchLogMessageFactory,
            workBenchLogGateway:workBenchLogGateway,
            reDispatchSubmitExeCmd:reDispatchSubmitExeCmd,
            messageProducer:messageProducer

    )

    def "test execute suc"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("userOrderId", "2222")
        map.put("roleId", "1")
        map.put("reasonDetailId", "15")

        def msg = new BaseMessage(attrs: map)
        def order = new DspOrderDO(userOrderId: "1111", supplyOrderId: "33331", orderStatus: 240, estimatedUseTime: new Timestamp(System.currentTimeMillis()),supplierId: 30804)
        dspOrderRepository.find(_) >> order
        def dspOrderConfirmRecordVO = new DspOrderConfirmRecordVO(driverInfo:new DspOrderConfirmRecordVO.DriverRecord(driverId: 1L))
        recordRepository.findByDspOrderId(_,_) >> dspOrderConfirmRecordVO


        when: "执行校验方法"
        listener.onMessage(msg)

        then: "验证校验结果"
        order.orderStatus == 240


    }

    def "test execute null"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("userOrderId", "2222")
        map.put("roleId", "1")
        map.put("reasonDetailId", "13")

        def msg = new BaseMessage(attrs: map)
        def order = new DspOrderDO(userOrderId: "1111", supplyOrderId: "33331", orderStatus: 240, estimatedUseTime: new Timestamp(System.currentTimeMillis()))
        dspOrderRepository.find(_) >> null


        when: "执行校验方法"
        listener.onMessage(msg)

        then: "验证校验结果"
        order.orderStatus == 240


    }


    def "test execute suc1"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("driverId", "1111")
        map.put("userOrderId", "2222")
        map.put("sysExceptBookTimeBj", "2023-12-20 10:00:00")
        map.put("freezeTotalHours", 15)
        map.put("log", "")

        def msg = new BaseMessage(attrs: map)
        def order = new DspOrderDO(userOrderId: "1111", supplyOrderId: "33331", orderStatus: 240, estimatedUseTime: new Timestamp(System.currentTimeMillis()),supplierId: 30804)
        dspOrderRepository.find(_) >> order
        def dspOrderConfirmRecordVO = new DspOrderConfirmRecordVO(driverInfo:new DspOrderConfirmRecordVO.DriverRecord(driverId: 1L))
        recordRepository.findByDspOrderId(_,_) >> dspOrderConfirmRecordVO

        queryDspOrderService.selectSectionWaitingServiceOrder(_,_,_,_)>> null


        when: "执行校验方法"
        listener.onCarFaultFreezeMessage(msg)

        then: "验证校验结果"
        map.isEmpty() != true

    }

    def "test execute suc2"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("driverId", "1111")
        map.put("userOrderId", "2222")
        map.put("sysExceptBookTimeBj", "2023-12-20 10:00:00")
        map.put("freezeTotalHours", 15)
        map.put("log", JacksonUtil.serialize(new LogContentDTO()))

        def msg = new BaseMessage(attrs: map)
        def order = new DspOrderDO(userOrderId: "1111", supplyOrderId: "33331", orderStatus: 240, estimatedUseTime: new Timestamp(System.currentTimeMillis()),supplierId: 30804)
        dspOrderRepository.find(_) >> order
        def dspOrderConfirmRecordVO = new DspOrderConfirmRecordVO(driverInfo:new DspOrderConfirmRecordVO.DriverRecord(driverId: 1L))
        recordRepository.findByDspOrderId(_,_) >> dspOrderConfirmRecordVO

        queryDspOrderService.selectSectionWaitingServiceOrder(_,_,_,_)>> [new DspOrderVO(userOrderId: "111")]


        when: "执行校验方法"
        listener.onCarFaultFreezeMessage(msg)

        then: "验证校验结果"
        map.isEmpty() != true

    }




}
