package listener

import com.ctrip.dcs.application.command.CancelDspOrderExeCmd
import com.ctrip.dcs.application.listener.DriverOrderCancelListener
import com.ctrip.dcs.application.processor.PushSettlementProcessor
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.gateway.DspDrvOrderLimitTakenRecordGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderConfirmRecordDao
import com.ctrip.igt.framework.common.jackson.JacksonUtil
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

import java.sql.Timestamp

class DriverOrderCancelListenerTest extends Specification {

    def dspOrderRepository = Mock(DspOrderRepository)
    def cancelDspOrderExeCmd = Mock(CancelDspOrderExeCmd)
    def dspDrvOrderLimitTakenRecordGateway = Mock(DspDrvOrderLimitTakenRecordGateway)
    def pushSettlementProcessor = Mock(PushSettlementProcessor)
    def dspOrderConfirmRecordDao = Mock(DspOrderConfirmRecordDao)

    def listener = new DriverOrderCancelListener(
            dspOrderRepository: dspOrderRepository,
            cancelDspOrderExeCmd: cancelDspOrderExeCmd,
            dspDrvOrderLimitTakenRecordGateway:dspDrvOrderLimitTakenRecordGateway,
            pushSettlementProcessor: pushSettlementProcessor,
            dspOrderConfirmRecordDao: dspOrderConfirmRecordDao
    )

    def "test execute suc"() {

        given: "Mock数据"
        Map<String, Object> param = new HashMap<>()
        param.put("causeCode" ,100010)
        param.put("cancelReason" ,"取消场景")

        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("cancelRecord", JacksonUtil.serialize(param))
        def msg = new BaseMessage(attrs: map)
        def order = new DspOrderDO(userOrderId: "1111", orderStatus: 600, estimatedUseTime: new Timestamp(System.currentTimeMillis()))
        dspOrderRepository.find(_) >> order


        when: "执行校验方法"
        listener.onMessage(msg)

        then: "验证校验结果"
        order.orderStatus == 600
        1 * cancelDspOrderExeCmd.execute(_)


    }


}
