package listener.binlog

import com.ctrip.arch.canal.DataChange
import com.ctrip.dcs.application.listener.binlog.DispatchDBBinlogListener
import com.ctrip.dcs.application.listener.binlog.DspOrderDealImpl
import com.ctrip.dcs.application.listener.binlog.DspOrderDetailDealImpl
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.platform.dal.dao.helper.JsonUtils
import org.assertj.core.util.Lists
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

class DspOrderDealImplTest extends Specification {

    def dspOrderRepository = Mock(DspOrderRepository)

    def listener = new DspOrderDealImpl(
            dspOrderRepository: dspOrderRepository
    )
    def "dispatchDBBinlogMessage"() {
        given:
        when:
        listener.clearCache(new DataChange())
        then: "验证校验结果"
        1 * dspOrderRepository.clearCache(_)

    }


    def "dispatchDBBinlogMessage1"() {
        given:
        when:
        listener.clearCache(Lists.newArrayList("1"))
        then: "验证校验结果"
        1 * dspOrderRepository.clearCache(_)

    }
}
