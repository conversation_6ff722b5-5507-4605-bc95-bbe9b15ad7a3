package listener.binlog

import com.ctrip.arch.canal.DataChange
import com.ctrip.dcs.application.listener.binlog.DspOrderWayPointDealImpl
import com.ctrip.dcs.application.listener.binlog.OrderLocaleDealImpl
import com.ctrip.dcs.domain.dsporder.entity.OrderLocaleDO
import com.ctrip.dcs.domain.dsporder.repository.DspOrderWayPointRepository
import com.ctrip.dcs.domain.dsporder.repository.OrderLocaleRepository
import org.assertj.core.util.Lists
import spock.lang.Specification

class OrderLocaleDealImplTest extends Specification {

    def orderLocaleRepository = Mock(OrderLocaleRepository)

    def listener = new OrderLocaleDealImpl(
            orderLocaleRepository: orderLocaleRepository
    )

    def "dispatchDBBinlogMessage"() {
        given:
        when:
        listener.clearCache(new DataChange())
        then: "验证校验结果"
        1 * orderLocaleRepository.clearCache(_,_)

    }

    def "dispatchDBBinlogMessage1"() {
        given:
         orderLocaleRepository.findByDspOrderId(_ as String) >> [new OrderLocaleDO()]

        when:
        listener.clearCache(Lists.newArrayList("1"))
        then: "验证校验结果"
        1 * orderLocaleRepository.clearCache(_,_)

    }
}
