package listener.binlog

import com.ctrip.arch.canal.DataChange
import com.ctrip.dcs.application.listener.binlog.DspOrderDetailDealImpl
import com.ctrip.dcs.application.listener.binlog.DspOrderExtendAttributeDealImpl
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderExtendAttributeRepository
import org.assertj.core.util.Lists
import spock.lang.Specification

class DspOrderExtendAttributeDealImplTest extends Specification {

    def dspOrderExtendAttributeRepository = Mock(DspOrderExtendAttributeRepository)

    def listener = new DspOrderExtendAttributeDealImpl     (
            dspOrderExtendAttributeRepository: dspOrderExtendAttributeRepository
    )
    def "dispatchDBBinlogMessage"() {
        given:
        when:
        listener.clearCache(new DataChange())
        then: "验证校验结果"
        1 * dspOrderExtendAttributeRepository.clearCache(_)

    }

    def "dispatchDBBinlogMessage1"() {
        given:
        when:
        listener.clearCache(Lists.newArrayList("1"))
        then: "验证校验结果"
        1 * dspOrderExtendAttributeRepository.clearCache(_)

    }
}
