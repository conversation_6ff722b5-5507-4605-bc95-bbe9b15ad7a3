package listener.binlog

import com.ctrip.arch.canal.DataChange
import com.ctrip.dcs.application.listener.binlog.DspOrderFeeDealImpl
import com.ctrip.dcs.application.listener.binlog.DspOrderWayPointDealImpl
import com.ctrip.dcs.domain.dsporder.repository.DspOrderFeeRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderWayPointRepository
import org.assertj.core.util.Lists
import spock.lang.Specification

class DspOrderWayPointDealImplTest extends Specification {

    def dspOrderWayPointRepository = Mock(DspOrderWayPointRepository)

    def listener = new DspOrderWayPointDealImpl    (
            dspOrderWayPointRepository: dspOrderWayPointRepository
    )
    def "dispatchDBBinlogMessage"() {
        given:
        when:
        listener.clearCache(new DataChange())
        then: "验证校验结果"
        1 * dspOrderWayPointRepository.clearCache(_)

    }

    def "dispatchDBBinlogMessage1"() {
        given:
        when:
        listener.clearCache(Lists.newArrayList("1"))
        then: "验证校验结果"
        1 * dspOrderWayPointRepository.clearCache(_)

    }
}
