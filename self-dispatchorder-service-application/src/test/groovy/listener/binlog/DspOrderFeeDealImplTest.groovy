package listener.binlog

import com.ctrip.arch.canal.DataChange
import com.ctrip.dcs.application.listener.binlog.DspOrderExtendAttributeDealImpl
import com.ctrip.dcs.application.listener.binlog.DspOrderFeeDealImpl
import com.ctrip.dcs.domain.dsporder.repository.DspOrderExtendAttributeRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderFeeRepository
import org.assertj.core.util.Lists
import spock.lang.Specification

class DspOrderFeeDealImplTest extends Specification {

    def dspOrderFeeRepository = Mock(DspOrderFeeRepository)

    def listener = new DspOrderFeeDealImpl    (
            dspOrderFeeRepository: dspOrderFeeRepository
    )

    def "dispatchDBBinlogMessage"() {
        given:
        when:
        listener.clearCache(new DataChange())
        then: "验证校验结果"
        1 * dspOrderFeeRepository.clearCache(_)

    }

    def "dispatchDBBinlogMessage1"() {
        given:
        when:
        listener.clearCache(Lists.newArrayList("1"))
        then: "验证校验结果"
        1 * dspOrderFeeRepository.clearCache(_)

    }
}
