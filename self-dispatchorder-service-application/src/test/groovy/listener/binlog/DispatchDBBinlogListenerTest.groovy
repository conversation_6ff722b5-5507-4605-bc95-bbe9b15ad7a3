package listener.binlog

import com.ctrip.arch.canal.DataChange
import com.ctrip.dcs.application.listener.binlog.DispatchDBBinlogListener
import com.ctrip.dcs.application.listener.binlog.DspOrderDetailDealImpl
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository
import com.ctrip.platform.dal.dao.helper.JsonUtils
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

class DispatchDBBinlogListenerTest extends Specification {

    def map = Mock(Map)
    def dspOrderDetailRepository = Mock(DspOrderDetailRepository)

    def listener = new DispatchDBBinlogListener(
            map: map
    )

    def "dispatchDBBinlogMessage"() {
        given:
        def source = new DspOrderDetailDealImpl(dspOrderDetailRepository: dspOrderDetailRepository)
        map.get(_) >> source

        when:
        Map<String, Object> map = new HashMap<>()
        map.put("dataChange", JsonUtils.toJson(new DataChange()))
        def msg = new BaseMessage(attrs: map)
        listener.dispatchDBBinlogMessage(msg)
        then: "验证校验结果"
        msg.getStringProperty("dataChange") != null

    }
}
