package listener.binlog

import com.ctrip.arch.canal.DataChange
import com.ctrip.dcs.application.listener.binlog.DspOrderDealImpl
import com.ctrip.dcs.application.listener.binlog.DspOrderDetailDealImpl
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import org.assertj.core.util.Lists
import spock.lang.Specification

class DspOrderDetailDealImplTest extends Specification {

    def dspOrderDetailRepository = Mock(DspOrderDetailRepository)

    def listener = new DspOrderDetailDealImpl    (
            dspOrderDetailRepository: dspOrderDetailRepository
    )
    def "dispatchDBBinlogMessage"() {
        given:
        when:
        listener.clearCache(new DataChange())
        then: "验证校验结果"
        1 * dspOrderDetailRepository.clearCache(_)

    }


    def "dispatchDBBinlogMessage1"() {
        given:
        when:
        listener.clearCache(Lists.newArrayList("1"))
        then: "验证校验结果"
        1 * dspOrderDetailRepository.clearCache(_)

    }
}
