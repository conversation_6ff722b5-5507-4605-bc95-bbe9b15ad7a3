package listener.binlog

import com.ctrip.arch.canal.DataChange
import com.ctrip.dcs.application.listener.binlog.DspOrderFeeDealImpl
import com.ctrip.dcs.application.listener.binlog.DspOrderRewardDealImpl
import com.ctrip.dcs.domain.dsporder.repository.DspOrderFeeRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRewardRepository
import org.assertj.core.util.Lists
import spock.lang.Specification

class DspOrderRewardDealImplTest extends Specification {

    def dspOrderRewardRepository = Mock(DspOrderRewardRepository)

    def listener = new   DspOrderRewardDealImpl    (
            dspOrderRewardRepository: dspOrderRewardRepository
    )

    def "dispatchDBBinlogMessage"() {
        given:
        when:
        listener.clearCache(new DataChange())
        then: "验证校验结果"
        1 * dspOrderRewardRepository.cleanCacheForDspOrderReward(_)

    }

    def "dispatchDBBinlogMessage1"() {
        given:
        when:
        listener.clearCache(Lists.newArrayList("1"))
        then: "验证校验结果"
        1 * dspOrderRewardRepository.cleanCacheForDspOrderReward(_)

    }
}
