package listener

import com.ctrip.dcs.application.command.CreateOrderWayPointTagExeCmd
import com.ctrip.dcs.application.command.CreateScheduleExeCmd
import com.ctrip.dcs.application.listener.DspOrderBookPostListener
import com.ctrip.dcs.domain.dsporder.gateway.OrderLocaleGateway
import com.ctrip.dcs.domain.dsporder.logic.PlatformPriceStrategyLogic
import com.ctrip.dcs.domain.dsporder.logic.PremiumOrderLogic
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

class DspOrderBookPostListenerTest extends Specification {

    def createScheduleExeCmd = Mock(CreateScheduleExeCmd)
    def premiumOrderLogic = Mock(PremiumOrderLogic)
    def orderLocaleGateway = Mock(OrderLocaleGateway)
    def platformPriceStrategyLogic = Mock(PlatformPriceStrategyLogic)
    def createOrderWayPointTagExeCmd = Mock(CreateOrderWayPointTagExeCmd)

    def listener = new DspOrderBookPostListener(
            createScheduleExeCmd: createScheduleExeCmd,
            premiumOrderLogic: premiumOrderLogic,
            orderLocaleGateway: orderLocaleGateway,
            platformPriceStrategyLogic: platformPriceStrategyLogic,
            createOrderWayPointTagExeCmd: createOrderWayPointTagExeCmd

    )

    def "test execute suc"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("userOrderId", "2222")
        def msg = new BaseMessage(attrs: map)

        when: "执行校验方法"
        listener.onOrderBookMessage1(msg)

        then: "验证校验结果"
        1 * platformPriceStrategyLogic.savePriceStrategy(_)
    }

    def "test execute suc1"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("userOrderId", "2222")
        def msg = new BaseMessage(attrs: map)

        when: "执行校验方法"
        listener.onOrderBookMessage2(msg)

        then: "验证校验结果"
        1 * orderLocaleGateway.saveOrderLocale(_)
    }

    def "test execute suc2"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("userOrderId", "2222")
        def msg = new BaseMessage(attrs: map)

        when: "执行校验方法"
        listener.onOrderBookMessage3(msg)

        then: "验证校验结果"
        1 * premiumOrderLogic.premiumOrderProcess(_)
    }

    def "test execute suc3"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("userOrderId", "2222")
        map.put("assignType", 0)
        def msg = new BaseMessage(attrs: map)

        when: "执行校验方法"
        listener.onCreateSchedule(msg)

        then: "验证校验结果"
        1 * createScheduleExeCmd.execute(_)
    }

    def "test on OrderWayPointTag"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("userOrderId", "2222")
        map.put("assignType", 0)
        def msg = new BaseMessage(attrs: map)

        when: "执行校验方法"
        listener.onOrderWayPointTag(msg)

        then: "验证校验结果"
        1 * createOrderWayPointTagExeCmd.execute(_)
        map.get("userOrderId") == "2222"

    }
}
