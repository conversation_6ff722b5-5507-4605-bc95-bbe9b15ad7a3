package listener

import com.ctrip.dcs.application.command.CalculateDriverMileageProfitExeCmd
import com.ctrip.dcs.application.listener.DspOrderDoneListener
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.igt.framework.common.jackson.JacksonUtil
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

class DspOrderDoneListenerTest extends Specification {

    def dspOrderConfirmRecordRepository = Mock(DspOrderConfirmRecordRepository)
    def calculateDriverMileageProfitExeCmd = Mock(CalculateDriverMileageProfitExeCmd)
    def queryDriverService = Mock(QueryDriverService)
    def queryDspOrderService = Mock(QueryDspOrderService)

    def listener = new DspOrderDoneListener(
            dspOrderConfirmRecordRepository: dspOrderConfirmRecordRepository,
            calculateDriverMileageProfitExeCmd: calculateDriverMileageProfitExeCmd,
            queryDriverService: queryDriverService,
            queryDspOrderService: queryDspOrderService
    )


    def "test execute suc"() {

        given: "Mock数据"
        Map<String, Object> param = new HashMap<>()
        param.put("causeCode", 100010)
        param.put("cancelReason", "取消场景")

        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("cancelRecord", JacksonUtil.serialize(param))
        def msg = new BaseMessage(attrs: map)
        queryDspOrderService.queryBase(_) >> getDspOrderVO1()
        dspOrderConfirmRecordRepository.find(_) >> getDspOrderConfirmRecordVO()
        queryDriverService.queryDriver(_ as Long, _ as ParentCategoryEnum, _ as Long) >> getDriverVO()


        when: "执行校验方法"
        listener.onMessage(msg)

        then: "验证校验结果"
        1 * calculateDriverMileageProfitExeCmd.execute(_);


    }

    DspOrderConfirmRecordVO getDspOrderConfirmRecordVO() {
        def driverRecord = new DspOrderConfirmRecordVO.DriverRecord(driverId: 1L, transportGroupId: 2)
        def reocrd = new DspOrderConfirmRecordVO(driverInfo: driverRecord, duid: "47067200989216864-1702697426785-1-1-8002-3-4-0-0-0")
        return reocrd
    }

    DspOrderVO getDspOrderVO1() {
        def dspOrderVO = new DspOrderVO(userOrderId: "1111", dspOrderId: "222", productType: 0, countryId: 1, categoryCode: CategoryCodeEnum.FROM_AIRPORT,estimatedUseTime: DateUtil.parseDateStr2Date("2024-10-01 12:00:00"))
        return dspOrderVO
    }

    DriverVO getDriverVO() {
        def supplier = new SupplierVO(1105877L, [1105877L])
        def car = new CarVO(carId: 1L, carLicense: "京xxxx", carColorId: 1L, carColor: "red", carBrandId: 1L, carBrandName: "carBrandName", carTypeId: 119L, carTypeName: "carTypeName", carSeriesId: 1L, carSeriesName: "carSeriesName", isEnergy: 0)

        def drv = new DriverVO(10004, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [new TransportGroupVO(249, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1105877, 0, 0,"informPhone", "informEmail", null, null, null)], car, supplier, 0, new PeriodsVO(["10:00~23:00"]), Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId",1, 0, 1l, null, YesOrNo.NO, false,0,1, "", "","","","",null)
        return drv
    }


}
