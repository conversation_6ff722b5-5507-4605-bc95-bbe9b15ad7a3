package listener

import com.ctrip.dcs.application.event.IConflictRedispatchEventHandler
import com.ctrip.dcs.application.listener.ConflictRedispatchListener
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

class ConflictRedispatchListenerTest extends Specification {
    def handler = Mock(IConflictRedispatchEventHandler)
    def listener = new ConflictRedispatchListener(handler: handler)

    def "redispatch"(){
        given:
        when:
        handler.redispatch(_,_,_,_,_) >> true
        Map<String, Object> map = new HashMap<>()
        def msg = new BaseMessage(attrs: map)
        listener.redispatch(msg)
        then:
        msg != null
    }
}
