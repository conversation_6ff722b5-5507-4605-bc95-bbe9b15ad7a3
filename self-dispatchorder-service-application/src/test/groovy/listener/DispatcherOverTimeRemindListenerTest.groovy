package listener


import com.ctrip.dcs.application.listener.DispatcherOverTimeRemindListener
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.IvrCallService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.service.VbkAppPushService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.igt.framework.common.jackson.JacksonUtil
import qunar.tc.qmq.base.BaseMessage
import qunar.tc.qmq.client.consumer.ConsumeMessage
import spock.lang.Specification

import java.sql.Timestamp

class DispatcherOverTimeRemindListenerTest extends Specification {

    def queryDspOrderService = Mock(QueryDspOrderService)
    def ivrCallService = Mock(IvrCallService)
    def timeOutNoDispatchService = Mock(VbkAppPushService)


    def listener = new DispatcherOverTimeRemindListener(
            queryDspOrderService: queryDspOrderService,
            ivrCallService: ivrCallService,
            timeOutNoDispatchService: timeOutNoDispatchService,

    )

    def "test execute suc1"() {

        given: "Mock数据"

        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("pushRounds", 1)
        map.put("supplierId", 32804)
        map.put("lastConfirmCarTime", "2023-12-25 10:00:01")
        map.put("pushPhase", 1)
        map.put("userOrderId", "222")
        def msg = new BaseMessage(attrs: map)
        queryDspOrderService.query(_) >> getDspOrderVO2()


        when: "执行校验方法"
        listener.timeoutNoDispatchPush(msg)

        then: "验证校验结果"
        1 * timeOutNoDispatchService.timeOutNoDispatchAppVoicePush(_)


    }

    def "test execute suc"() {

        given: "Mock数据"

        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("triggerType", 1)
        map.put("checkTimes", 1)
        map.put("lastConfirmCarTime", "2023-12-25 10:00:01")
        map.put("orderStatus", 240)
        map.put("userOrderId", "222")
        def msg = new BaseMessage(attrs: map)
        queryDspOrderService.query(_) >> getDspOrderVO2()

        when: "执行校验方法"
        listener.onMessage(msg)

        then: "验证校验结果"
        msg.getStringProperty("dspOrderId") == "1111"


    }

    def "test execute suc3"() {

        given: "Mock数据"

        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("triggerType", 20)
        map.put("checkTimes", 1)
        map.put("lastConfirmCarTime", "2023-12-25 10:00:01")
        map.put("orderStatus", 240)
        map.put("userOrderId", "222")
        def msg = new BaseMessage(attrs: map)

        queryDspOrderService.query(_) >> getDspOrderVO2()

        when: "执行校验方法"
        listener.onMessage(msg)

        then: "验证校验结果"
        msg.getStringProperty("dspOrderId") == "1111"


    }

    def "test execute suc4"() {

        given: "Mock数据"

        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("triggerType", 1)
        map.put("checkTimes", 1)
        map.put("lastConfirmCarTime", "2023-12-25 10:00:01")
        map.put("orderStatus", 240)
        map.put("userOrderId", "222")
        def msg = new BaseMessage(attrs: map)

        queryDspOrderService.query(_) >> getDspOrderVO1()

        when: "执行校验方法"
        listener.onMessage(msg)

        then: "验证校验结果"
        msg.getStringProperty("dspOrderId") == "1111"


    }

    DspOrderVO getDspOrderVO1() {
        def dspOrderVO = new DspOrderVO(userOrderId: "1111", dspOrderId: "222", productType: 0, countryId: 1, categoryCode: CategoryCodeEnum.DAY_RENTAL,estimatedUseTime: new Date())
        return dspOrderVO
    }


    DspOrderVO getDspOrderVO2() {
        def dspOrderVO = new DspOrderVO(userOrderId: "1111", dspOrderId: "222", productType: 0, countryId: 1, categoryCode: CategoryCodeEnum.DAY_RENTAL,estimatedUseTime: DateUtil.parse("2023-12-25 10:00:01",DateUtil.DATETIME_FORMAT))
        return dspOrderVO
    }



}
