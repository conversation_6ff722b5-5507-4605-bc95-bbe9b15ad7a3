package listener

import com.ctrip.dcs.application.listener.DriverOrderFinishListener
import com.ctrip.dcs.application.processor.PushSettlementProcessor
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.domain.schedule.repository.DriverAttendanceRepository
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

import java.sql.Timestamp

class DriverOrderFinishListenerTest extends Specification {

    def dspOrderRepository = Mock(DspOrderRepository)
    def dspOrderConfirmRecordRepository = Mock(DspOrderConfirmRecordRepository)
    def messageProviderService = Mock(com.ctrip.dcs.domain.common.service.MessageProviderService)
    def driverAttendanceRepository = Mock(DriverAttendanceRepository)
    def pushSettlementProcessor = Mock(PushSettlementProcessor)

    def listener = new DriverOrderFinishListener(
            dspOrderRepository: dspOrderRepository,
            dspOrderConfirmRecordRepository: dspOrderConfirmRecordRepository,
            messageProducer: messageProviderService,
            driverAttendanceRepository: driverAttendanceRepository,
            pushSettlementProcessor: pushSettlementProcessor
    )

    def "test execute suc"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("drvOrderId", "2222")
        def msg = new BaseMessage(attrs: map)
        def order = new DspOrderDO(userOrderId: "1111", orderStatus: 600, estimatedUseTime: new Timestamp(System.currentTimeMillis()))
        def driverInfo = new DspOrderConfirmRecordVO.DriverRecord(transportGroupId: 3L)
        def confirm = new DspOrderConfirmRecordVO(driverInfo: driverInfo)
        dspOrderRepository.find(_) >> order
        dspOrderConfirmRecordRepository.findByDspOrderId(_, _) >> confirm


        when: "执行校验方法"
        listener.onMessage(msg)

        then: "验证校验结果"
        order.orderStatus == 700
        1 * dspOrderRepository.update(_)

    }


    def "test onMessageDriverAttendance suc"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("sysExpectBookTimeBJ", System.currentTimeMillis())
        map.put("driverId", "2222")
        def msg = new BaseMessage(attrs: map)



        when: "执行校验方法"
        listener.onMessageDriverAttendance(msg)

        then: "验证校验结果"
        1 * driverAttendanceRepository.save(_,_)

    }


    def "test onMessageNonCtripOrder suc"() {

        given: "Mock数据"

        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", dspOrderId)
        map.put("qmq_times", times)
        map.put("orderStatus", driverOrderStatus)
        def msg = new BaseMessage(attrs: map)

        def order = new DspOrderDO(userOrderId: "1111", orderStatus: 600, estimatedUseTime: new Timestamp(System.currentTimeMillis()))
        def driverInfo = new DspOrderConfirmRecordVO.DriverRecord(transportGroupId: 3L)
        def confirm = new DspOrderConfirmRecordVO(driverInfo: driverInfo)

        dspOrderRepository.find(_) >> order
        order.setOrderStatus(dspOrderStatus)
        if (orderFlag) {
            order = null;
        }
        dspOrderConfirmRecordRepository.findByDspOrderId(_, _) >> confirm
        if (confirmFlag) {
            confirm = null;
        }
        when: "执行校验方法"
        listener.onMessageNonCtripOrder(msg)
        then: "验证校验结果"

        where:
        dspOrderId | times | driverOrderStatus | dspOrderStatus | orderFlag | confirmFlag || code
        null       | null  | null              | null           | null      | null        || null
        "1"        | 100   | null              | null           | null      | null        || null
        "1"        | 1     | 700               | null           | null      | null        || null
        "1"        | 1     | 240               | null           | true      | false       || null
        "1"        | 1     | 240               | null           | false     | true        || null
        "1"        | 1     | 240               | null           | true      | true        || null
        "1"        | 1     | 240               | null           | false     | false       || null
        "1"        | 1     | 240               | 700            | false     | false       || null
        "1"        | 1     | 240               | 900            | false     | false       || null
        "1"        | 1     | 240               | 240            | false     | false       || null
    }


}
