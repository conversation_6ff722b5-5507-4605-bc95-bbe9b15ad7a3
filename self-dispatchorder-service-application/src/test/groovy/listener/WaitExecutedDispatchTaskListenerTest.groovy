package listener

import com.ctrip.dcs.application.command.ExecuteTaskExeCmd
import com.ctrip.dcs.application.listener.WaitExecutedDispatchTaskListener
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

class WaitExecutedDispatchTaskListenerTest extends Specification {

    def executeTaskExeCmd = Mock(ExecuteTaskExeCmd)


    def listener = new WaitExecutedDispatchTaskListener(
            executeTaskExeCmd: executeTaskExeCmd

    )

    def "test execute"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("taskId", 1L)
        map.put("dspOrderId", "aba")
        map.put("scheduleId", 2L)
        map.put("scheduleTaskId", "cccc")
        def msg = new BaseMessage(attrs: map)

        when: "执行校验方法"
        listener.onMessage(msg)

        then: "验证校验结果"
        msg.getStringProperty("dspOrderId") == "aba"
    }
}
