package listener

import com.ctrip.dcs.application.command.CalculateDriverMileageProfitExeCmd
import com.ctrip.dcs.application.command.dispatchergrab.CancelDispatcherGrabOrderExeCmd
import com.ctrip.dcs.application.listener.DspOrderCancelListener
import com.ctrip.dcs.application.service.ShutdownScheduleService
import com.ctrip.dcs.domain.common.service.*
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderFeeDO
import com.ctrip.dcs.domain.dsporder.entity.QueryGeoDetailResDTO
import com.ctrip.dcs.domain.dsporder.gateway.DspDrvOrderLimitTakenRecordGateway
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway
import com.ctrip.dcs.domain.dsporder.gateway.PlatformGeoServiceGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderFeeRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

import java.sql.Timestamp

class DspOrderCancelListenerTest extends Specification {
    def dspOrderRepository = Mock(DspOrderRepository)
    def queryTransportGroupService = Mock(QueryTransportGroupService)
    def sendSmsService = Mock(SendSmsService)
    def sendEmailService = Mock(SendEmailService)
    def businessTemplateInfoConfig = Mock(BusinessTemplateInfoConfig)
    def platformGeoServiceGateway = Mock(PlatformGeoServiceGateway)
    def dspOrderConfirmRecordRepository = Mock(DspOrderConfirmRecordRepository)
    def decryptPhoneService = Mock(DecryptPhoneService)
    def ivrCallService = Mock(IvrCallService)
    def grabOrderDetailRepository = Mock(GrabOrderDetailRepository)
    def vbkAppPushService = Mock(VbkAppPushService)
    def dspOrderFeeRepository = Mock(DspOrderFeeRepository)
    def cancelDispatcherGrabOrderExeCmd = Mock(CancelDispatcherGrabOrderExeCmd)
    def grabCentreRepository = Mock(GrabCentreRepository)
    def calculateDriverMileageProfitExeCmd = Mock(CalculateDriverMileageProfitExeCmd)
    def queryDriverService = Mock(QueryDriverService)
    def queryDspOrderService = Mock(QueryDspOrderService)
    def shutdownScheduleService = Mock(ShutdownScheduleService)
    def igtOrderQueryServiceGateway = Mock(IGTOrderQueryServiceGateway)
    def dspDrvOrderLimitTakenRecordGateway = Mock(DspDrvOrderLimitTakenRecordGateway)

    def listener = new DspOrderCancelListener(dspOrderRepository: dspOrderRepository
            , queryTransportGroupService: queryTransportGroupService
            , sendSmsService: sendSmsService
            , sendEmailService: sendEmailService
            , businessTemplateInfoConfig: businessTemplateInfoConfig
            , platformGeoServiceGateway: platformGeoServiceGateway
            , dspOrderConfirmRecordRepository: dspOrderConfirmRecordRepository
            , decryptPhoneService: decryptPhoneService
            ,ivrCallService:ivrCallService
            ,vbkAppPushService:vbkAppPushService
            ,dspOrderFeeRepository: dspOrderFeeRepository
            ,cancelDispatcherGrabOrderExeCmd: cancelDispatcherGrabOrderExeCmd
            ,grabCentreRepository: grabCentreRepository
            ,calculateDriverMileageProfitExeCmd:calculateDriverMileageProfitExeCmd
            ,queryDriverService:queryDriverService
            ,queryDspOrderService:queryDspOrderService
            ,grabOrderDetailRepository: grabOrderDetailRepository
            ,shutdownScheduleService: shutdownScheduleService
            ,igtOrderQueryServiceGateway:igtOrderQueryServiceGateway
            ,dspDrvOrderLimitTakenRecordGateway:dspDrvOrderLimitTakenRecordGateway
    )


    def "test jnt execute suc"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("drvOrderId", "2222")
        def msg = new BaseMessage(attrs: map)
        def order = new DspOrderDO(userOrderId: "1111", categoryCode: "airport_dropoff", cityId: 1, orderStatus: 600, estimatedUseTime: new Timestamp(System.currentTimeMillis()), bizAreaType: 33)
        def driverInfo = new DspOrderConfirmRecordVO.DriverRecord(driverId: 5, transportGroupId: 3L)
        def confirm = new DspOrderConfirmRecordVO(driverInfo: driverInfo)
        dspOrderRepository.find(_) >> order
        def group = new TransportGroupVO(transportGroupId: 2L, transportGroupName: "运力组名称", informSwitch: 1, informEmail: "<EMAIL>")
        queryTransportGroupService.queryTransportGroup(_) >> group
        def resDTO = new QueryGeoDetailResDTO(chineseName: "中文", englishName: "yingwen")
        platformGeoServiceGateway.queryGeoDetail(_) >> resDTO
        Map<String, String> serviceTypeMap = new HashMap<>()
        map.put("airport_dropoff", "接机")
        businessTemplateInfoConfig.getServiceTypeDesMap() >> serviceTypeMap
        businessTemplateInfoConfig.getValueByKey(_) >> ""
        dspOrderConfirmRecordRepository.findByDspOrderId(_, _) >> confirm
        dspOrderFeeRepository.find(_) >> new DspOrderFeeDO(cancelFineRate: rate as BigDecimal)


        when: "执行校验方法"
        listener.onMessage(msg)

        then: "验证校验结果"
        map.isEmpty() != res

        where:
        rate || res
        0.00  || true
        0.1 || true

    }

    def "test charter execute suc"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("drvOrderId", "2222")
        def msg = new BaseMessage(attrs: map)
        def order = new DspOrderDO(userOrderId: "1111", categoryCode: "day_rental", cityId: 1, orderStatus: 600, estimatedUseTime: new Timestamp(System.currentTimeMillis()), bizAreaType: 32)
        def driverInfo = new DspOrderConfirmRecordVO.DriverRecord(driverId: 5, transportGroupId: 3L)
        def confirm = new DspOrderConfirmRecordVO(driverInfo: driverInfo)
        dspOrderRepository.find(_) >> order
        def group = new TransportGroupVO(transportGroupId: 2L, transportGroupName: "运力组名称", informSwitch: 1, informPhone: "***********")
        queryTransportGroupService.queryTransportGroup(_) >> group
        def resDTO = new QueryGeoDetailResDTO(chineseName: "中文", englishName: "yingwen")
        platformGeoServiceGateway.queryGeoDetail(_) >> resDTO
        Map<String, String> serviceTypeMap = new HashMap<>()
        map.put("day_rental", "包车")
        businessTemplateInfoConfig.getServiceTypeDesMap() >> serviceTypeMap
        dspOrderConfirmRecordRepository.findByDspOrderId(_, _) >> confirm
        dspOrderFeeRepository.find(_) >> new DspOrderFeeDO(cancelFineRate: 0.00 as BigDecimal)


        when: "执行校验方法"
        listener.onMessage(msg)

        then: "验证校验结果"
        map.isEmpty() != true

    }

    def "test_onOriModifyCancelDelay"() {

        given: "Mock数据"
        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("userOrderId", "2222")
        def msg = new BaseMessage(attrs: map)

        def order1 = new DspOrderDO(dspOrderId: "1122", userOrderId: "2222", categoryCode: "airport_dropoff", cityId: 1, orderStatus: 600, estimatedUseTime: new Timestamp(System.currentTimeMillis()), bizAreaType: 33, originalDspOrderId: "1111",originalSupplierConfirm: 1)
        def order2 = new DspOrderDO(dspOrderId: "1122", userOrderId: "2222", categoryCode: "airport_dropoff", cityId: 1, orderStatus: 600, estimatedUseTime: new Timestamp(System.currentTimeMillis()), bizAreaType: 33, originalDspOrderId: "1111")
        def order3 = new DspOrderDO(dspOrderId:"1234", userOrderId: "2222", categoryCode: "airport_dropoff", cityId: 1, orderStatus: 600, estimatedUseTime: new Timestamp(System.currentTimeMillis()), bizAreaType: 33)
        dspOrderRepository.queryValidDspOrders(_) >> [] >> [order1] >> [order2] >> [order3]

        when: "执行校验方法"
        listener.onOriModifyCancelDelay(msg)
        listener.onOriModifyCancelDelay(msg)
        listener.onOriModifyCancelDelay(msg)
        listener.onOriModifyCancelDelay(msg)

        then: "验证校验结果"
        3 * dspOrderRepository.find(_)
    }


}
