package listener


import com.ctrip.dcs.application.listener.UseTimeChangeListener
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.IvrCallService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.dsporder.gateway.DspDrvOrderLimitTakenRecordGateway
import com.ctrip.dcs.infrastructure.gateway.remind.DispatcherIgtOrderIvrRemindImpl
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

class UseTimeChangeListenerTest extends Specification {

    def ivrCallService = Mock(IvrCallService)
    def queryDspOrderService = Mock(QueryDspOrderService)
    def dspDrvOrderLimitTakenRecordGateway = Mock(DspDrvOrderLimitTakenRecordGateway)
    def dispatcherIgtOrderIvrRemind = Mock(DispatcherIgtOrderIvrRemindImpl)


    def listener = new UseTimeChangeListener(
            ivrCallService: ivrCallService,
            queryDspOrderService: queryDspOrderService,
            dspDrvOrderLimitTakenRecordGateway:dspDrvOrderLimitTakenRecordGateway,
            dispatcherIgtOrderIvrRemind:dispatcherIgtOrderIvrRemind

    )

    def "test execute suc"() {

        given: "Mock数据"

        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("oldLastConfirmCarTime", "2023-12-29 10:00:01")
        map.put("lastConfirmTime", "2023-12-30 10:00:01")

        def msg = new BaseMessage(attrs: map)

        queryDspOrderService.query(_)>> null


        when: "执行校验方法"
        listener.onMessage(msg)

        then: "验证校验结果"
        msg.getStringProperty("dspOrderId") == "1111"


    }

    def "test execute suc1"() {

        given: "Mock数据"

        Map<String, Object> map = new HashMap<>()
        map.put("dspOrderId", "1111")
        map.put("oldLastConfirmCarTime", "2023-12-29 10:00:01")
        map.put("lastConfirmTime", "2023-12-30 10:00:01")

        def msg = new BaseMessage(attrs: map)

        queryDspOrderService.query(_)>> getDspOrderVO1()


        when: "执行校验方法"
        listener.onMessage(msg)

        then: "验证校验结果"
        msg.getStringProperty("dspOrderId") == "1111"


    }

    DspOrderVO getDspOrderVO1() {
        def dspOrderVO = new DspOrderVO(userOrderId: "1111", dspOrderId: "222", productType: 0, countryId: 1, categoryCode: CategoryCodeEnum.DAY_RENTAL,estimatedUseTime: new Date())
        return dspOrderVO
    }



}
