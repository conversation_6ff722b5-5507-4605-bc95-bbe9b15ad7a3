package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.command.UpdateOrderSettleToDriverBeforeTakenCmd
import com.ctrip.dcs.application.command.UpdateOrderSettlementBeforeTakenCmd
import com.ctrip.dcs.infrastructure.adapter.soa.SelfOrderQueryServiceProxy
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateOrderDetailSettlementRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateOrderDetailSettlementResponseType
import com.ctrip.dcs.self.order.query.api.QueryOrderListResponseType
import com.ctrip.dcs.self.order.query.dto.BaseDetail
import com.ctrip.dcs.self.order.query.dto.OrderDetail
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class UpdateOrderDetailSettlementExecutorTest extends Specification {
    @Mock
    UpdateOrderSettlementBeforeTakenCmd updateOrderSettlementBeforeTakenCmd
    @Mock
    SelfOrderQueryServiceProxy selfOrderQueryServiceProxy
    @Mock
    UpdateOrderDetailSettlementRequestType requestType
    @Mock
    OrderDetail orderDetail
    @Mock
    QueryOrderListResponseType responseType
    @Mock
    BaseDetail baseDetail
    @Mock
    UpdateOrderSettleToDriverBeforeTakenCmd updateOrderSettleToDriverBeforeTakenCmd;
    @InjectMocks
    UpdateOrderDetailSettlementExecutor updateOrderDetailSettlementExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute empty"() {
        given:
        when(selfOrderQueryServiceProxy.queryOrderList(any())).thenReturn(responseType)

        when:
        UpdateOrderDetailSettlementResponseType result = updateOrderDetailSettlementExecutor.execute(requestType)

        then:
        result != null
    }

    def "test execute"() {
        given:
        when(selfOrderQueryServiceProxy.queryOrderList(any())).thenReturn(responseType)
        when(responseType.getOrderList()).thenReturn([orderDetail])
        when(orderDetail.getBaseDetail()).thenReturn(baseDetail)
        when(baseDetail.getCarTypeId()).thenReturn(1L)
        when(baseDetail.getSupplierId()).thenReturn(1)
        when(baseDetail.getOrderVersion()).thenReturn(4)

        when:
        UpdateOrderDetailSettlementResponseType result = updateOrderDetailSettlementExecutor.execute(requestType)

        then:
        result != null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme