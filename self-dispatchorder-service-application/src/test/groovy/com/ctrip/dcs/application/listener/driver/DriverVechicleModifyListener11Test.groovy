package com.ctrip.dcs.application.listener.driver


import com.ctrip.dcs.application.event.IDriverVehicleChangeEventHandler
import com.ctrip.dcs.application.event.IDriverVehicleInfoModifyEventHandler
import com.ctrip.dcs.application.event.IDriverVehicleTypeModifyEventHandler
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.igt.framework.common.jackson.JacksonSerializer
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverVechicleModifyListener11Test extends Specification {
    @Mock
    Logger logger
    @Mock
    IDriverVehicleChangeEventHandler driverVehicleChangeEventHandler;
    @Mock
    IDriverVehicleInfoModifyEventHandler driverVehicleInfoModifyEventHandler;
    @Mock
    IDriverVehicleTypeModifyEventHandler driverVehicleTypeModifyEventHandler;
    @InjectMocks
    DriverVehicleModifyListener driverVehicleModifyListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test handle 1"() {
        given:
        Message message = new BaseMessage()
        message.addTag("tag_drivervehicle_change")
        message.setProperty("drvId", "0")
        message.setProperty("vehicleId", "118")

        when:
        driverVehicleModifyListener.handle(message)

        then:
        message.getTags().size() == 1
    }


    def "test handle 3"() {
        given:
        Message message = new BaseMessage()
        message.addTag("tag_drivervehicletype_modify")
        message.setProperty("drvId", "0")

        when:
        driverVehicleModifyListener.handle(message)

        then:
        message.getTags().size() == 1
    }

    def "test handle 4"() {
        given:
        Message message = new BaseMessage()
        message.addTag("tag_drivervehicle_modify")
        message.setProperty("drvId", "0")

        when:
        driverVehicleModifyListener.handle(message)

        then:
        message.getTags().size() == 1
    }

    def "handleDrvGuideTransportChgMessage"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("driverId", driverId)
        message.setProperty("vehicleId", vehicleId)
        message.setProperty("operateFrom", "2")
        message.setProperty("driverProductLineList", driverProductLineList)
        when(driverVehicleInfoModifyEventHandler.handle(any())).thenThrow(new BizException("executeed"))

        when:
        def result = ""
        try {
            driverVehicleModifyListener.handleDrvGuideTransportChgMessage(message)
        } catch (Exception e) {
            result = e.getMessage()
        }

        then:
        result == expected

        where:
        driverId || vehicleId || driverProductLineList || expected
        0L       || 1L        || "V,G"                 || ""
        1L       || 1L        || ""                    || ""
        1L       || 1L        || "G"                   || ""
        1L       || 1L        || "V,G"                 || "executeed"
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme