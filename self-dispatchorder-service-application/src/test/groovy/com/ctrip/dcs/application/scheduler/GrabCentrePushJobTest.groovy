package com.ctrip.dcs.application.scheduler

import com.ctrip.dcs.application.command.SubmitBroadcastGrabExeCmd
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO
import com.ctrip.dcs.domain.schedule.process.impl.GrabCentreProcess
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository
import qunar.tc.schedule.MockParameter
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class GrabCentrePushJobTest extends Specification {
    @Mock
    GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository
    @Mock
    MessageProviderService messageProviderService
    @Mock
    GrabCentreProcess grabCentreProcess
    @Mock
    ConfigService broadcastGrabConfig
    @Mock
    SubmitBroadcastGrabExeCmd submitBroadcastGrabExeCmd
    @InjectMocks
    GrabCentrePushJob grabCentrePushJob

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    def "test execute"() {
        given:
        when(grabDspOrderDriverIndexRepository.queryCountGrabPushTime(any(Date.class), any(Date.class))).thenReturn(1L)
        when(grabDspOrderDriverIndexRepository.queryGrabPushTime(any(Date.class), any(Date.class), anyInt(), anyInt())).thenReturn([new GrabDspOrderDriverIndexDO(dspOrderId: "dspOrderId", driverId: 1l, cityId: 1l, isBroadcast: 0, duid: "47067200989216864-1702697426785-1-1-8002-3-4-0-0-0", broadcastPushTimeBj: new Date(), grabPushTimeBj: new Date())])
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)
        when(grabCentreProcess.notPushDriverByOrder(anyLong(), anyString())).thenReturn(true)
        when(grabCentreProcess.notPushDriverByDuration(anyLong())).thenReturn(true)
        when(submitBroadcastGrabExeCmd.isGrayscaleCity(anyLong())).thenReturn(true)

        when:
        def result = grabCentrePushJob.execute(new MockParameter())

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme