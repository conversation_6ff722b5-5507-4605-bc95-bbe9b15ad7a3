package com.ctrip.dcs.application.command

import cn.hutool.core.lang.Pair
import com.ctrip.dcs.application.command.dto.VBKGrabTaskDTO
import com.ctrip.dcs.application.command.dto.VBKGrabTaskSettlement
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabDriverDO
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabDriverRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO
import com.ctrip.dcs.infrastructure.common.util.DateZoneConvertUtil
import com.ctrip.dcs.infrastructure.service.DistributedLockServiceImpl
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.platform.dal.dao.helper.JsonUtils
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class UpdateVBKGrabTaskExeCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DistributedLockService distributedLockService
    @Mock
    VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository
    @Mock
    VBKDriverGrabDriverRepository vbkDriverGrabDriverRepository
    @Mock
    DateZoneConvertUtil dateZoneConvertUtil
    @Mock
    ConfigService commonConfConfig
    @Mock
    MessageProviderService messageProducer
    @Mock
    DistributedLockService.DistributedLock lock
    @InjectMocks
    UpdateVBKGrabTaskExeCmd updateVBKGrabTaskExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test send VBK Grab Task Operate Event"() {
        when:
        def r = updateVBKGrabTaskExeCmd.sendVBKGrabTaskOperateEvent(new VBKGrabTaskDTO(taskId: "taskId", cityId: 0, driverIdList: ["String"], initial: new VBKGrabTaskSettlement(type: 0, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), rewards: new VBKGrabTaskSettlement(type: 0, value: 0 as BigDecimal, rate: 0 as BigDecimal, currency: "currency"), grabEndTime: "grabEndTime", makeUpEffectTime: "makeUpEffectTime", payForDriver: 0, supplierId: 1l, operatorName: "operatorName"), new VBKDriverGrabTaskDO(initialType: 0, initialCurrency: "initialCurrency", rewardsType: 0, rewardsCurrency: "rewardsCurrency", grabLimitHours: 0 as BigDecimal, grabRewardHours: 0 as BigDecimal, initialValue: 0 as BigDecimal, initialRate: 0 as BigDecimal, rewardsValue: 0 as BigDecimal, rewardsRate: 0 as BigDecimal), 0l)

        then:
        r == null
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme