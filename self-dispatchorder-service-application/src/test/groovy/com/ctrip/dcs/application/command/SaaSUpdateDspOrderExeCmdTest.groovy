package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.SaaSUpdateDspOrderCommand
import com.ctrip.dcs.application.provider.converter.SaaSUpdateDspOrderConverter
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDetailDO
import com.ctrip.dcs.domain.dsporder.entity.FromPoiDTO
import com.ctrip.dcs.domain.dsporder.entity.ToPoiDTO
import com.ctrip.dcs.domain.dsporder.gateway.DateZoneConvertGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderOperateRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.dcs.infrastructure.common.util.LocalDateUtils
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasUpdateDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateOrderRemarkRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSExtendInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSFeeInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSPoiInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaasUpdateDspOrderInfo
import com.ctrip.igt.framework.common.exception.BizException
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

class SaaSUpdateDspOrderExeCmdTest extends Specification {

    def dspOrderRepository = Mock(DspOrderRepository)
    def dspOrderDetailRepository = Mock(DspOrderDetailRepository)
    def dspOrderOperateRepository = Mock(DspOrderOperateRepository)
    def driverOrderGateway = Mock(DriverOrderGateway)
    def dateZoneConvertGateway = Mock(DateZoneConvertGateway)
    def distributedLockService = Mock(DistributedLockService)
    def selfOrderQueryGateway = Mock(SelfOrderQueryGateway)
    def orderQueryService = Mock(QueryDspOrderService)

    def executor = new SaaSUpdateDspOrderExeCmd(
            dspOrderRepository: dspOrderRepository,
            dspOrderOperateRepository: dspOrderOperateRepository,
            distributedLockService: distributedLockService,
            dspOrderDetailRepository: dspOrderDetailRepository,
            driverOrderGateway: driverOrderGateway,
            dateZoneConvertGateway: dateZoneConvertGateway,
            selfOrderQueryGateway: selfOrderQueryGateway,
            orderQueryService: orderQueryService
    )

    @Unroll
    def "test execute"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        dspOrderRepository.find(_) >> oldDspOrderDO
        dspOrderDetailRepository.find(_) >> oldSDspOrderDetailDO
        dspOrderRepository.find(_) >> newDspOrderDO
        dspOrderDetailRepository.find(_) >> newSDspOrderDetailDO
        selfOrderQueryGateway.queryDspOrder(_) >> dspOrderVO

        when: "执行校验方法"
        executor.execute(req)

        then: "验证校验结果"
        true

        where:
        req       |  lock | oldDspOrderDO       | oldSDspOrderDetailDO     | newDspOrderDO           | newSDspOrderDetailDO | flowSwitch | dspOrderVO
        getReq()  |  true | getOldDspOrderDO()  | getOldDspOrderDetailDO() | getNewDspOrderDO()      | getNewDspOrderDetailDO() |  true  | getDspOrderVO()
        getReq()  |  true | getOldDspOrderDOTrip()  | getOldDspOrderDetailDO() | getNewDspOrderDO()      | getNewDspOrderDetailDO() | true |  getDspOrderVO()
        getReq2()  |  true | getOldDspOrderDOTrip()  | getOldDspOrderDetailDO() | getNewDspOrderDO()      | getNewDspOrderDetailDO() | true |  getDspOrderVO()

    }

    @Unroll
    def "test exception"() {

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        dspOrderRepository.find(_) >> oldDspOrderDO
        dspOrderDetailRepository.find(_) >> oldSDspOrderDetailDO
        dspOrderRepository.find(_) >> newDspOrderDO
        dspOrderDetailRepository.find(_) >> newSDspOrderDetailDO
        selfOrderQueryGateway.queryDspOrder(_) >> dspOrderVO
        when: "执行校验方法"
        executor.execute(req)

        then: "验证校验结果"

        def ex = thrown(BizException)
        ex.code == res

        where:
        req       |  lock | oldDspOrderDO       | oldSDspOrderDetailDO     | newDspOrderDO           | newSDspOrderDetailDO    | flowSwitch | dspOrderVO || res
        getReq()  |  false| getOldDspOrderDO()  | getOldDspOrderDetailDO() | getNewDspOrderDO()      | getNewDspOrderDetailDO() |  true  | getDspOrderVO() || "10033"
        getReq1() |  false| getOldDspOrderDO()  | getOldDspOrderDetailDO() | getNewDspOrderDO()      | getNewDspOrderDetailDO() | true |  getDspOrderVO()|| "10033"


    }

    @Unroll
    def "test execute newProcess is success"() {
        given:
        SaaSUpdateDspOrderCommand cmd = new SaaSUpdateDspOrderCommand();
        cmd.setNewProcess(1)
        cmd.setSupplierId(30804L)
        cmd.setDspOrderDO(new DspOrderDO(dspOrderId: "344444", fromPoiDTO: new FromPoiDTO(), toPoiDTO: new ToPoiDTO(), userOrderId: "22222"))
        cmd.setDspOrderDetailDO(dspOrderDetail);
        def lock = Mock(DistributedLockService.DistributedLock);
        distributedLockService.getLock(_) >> lock;
        lock.tryLock() >> true
        dspOrderRepository.find(_) >> dspOrderDO;
        dspOrderDetailRepository.find(_) >> dspOrderDetail;

        when:
        executor.execute(cmd)

        then:
        Objects.equals(cmd.getSupplierId(), 30804L);

        where:
        dspOrderDO                                                              | dspOrderDetail         || expect
        new DspOrderDO(supplierId: 30804, orderSourceCode: 2, orderStatus: 220, userOrderId: "22222") | new DspOrderDetailDO() || null
        new DspOrderDO(supplierId: 30804, orderSourceCode: 2, orderStatus: 230, userOrderId: "33333") | new DspOrderDetailDO() || null
    }


    @Unroll
    def "test execute newProcess is failed"() {
        given:
        SaaSUpdateDspOrderCommand cmd = new SaaSUpdateDspOrderCommand();
        cmd.setNewProcess(1)
        cmd.setSupplierId(30804L)
        cmd.setDspOrderDO(new DspOrderDO(dspOrderId: "344444", fromPoiDTO: new FromPoiDTO(), toPoiDTO: new ToPoiDTO()))
        cmd.setDspOrderDetailDO(dspOrderDetail);
        def lock = Mock(DistributedLockService.DistributedLock);
        distributedLockService.getLock(_) >> lock;
        lock.tryLock() >> true
        dspOrderRepository.find(_) >> dspOrderDO;
        dspOrderDetailRepository.find(_) >> dspOrderDetail;
        driverOrderGateway.update(_, _, _, _, _) >> {throw new BizException("")}
        when:
        executor.execute(cmd)

        then:
        def ex = thrown(BizException);
        Assert.assertTrue(ex != null)

        where:
        dspOrderDO                                                              | dspOrderDetail || expect
        new DspOrderDO(supplierId: 2666)                                        | null           || null
        new DspOrderDO(supplierId: 30804, orderSourceCode: 1)                   | null           || null
        new DspOrderDO(supplierId: 30804, orderSourceCode: 2, orderStatus: 900) | null           || null
        new DspOrderDO(supplierId: 30804, orderSourceCode: 2, orderStatus: 230) | null           || null
    }

    def "test updateOrderRemark"() {
        given:
        def lock = Mock(DistributedLockService.DistributedLock);
        distributedLockService.getLock(_) >> lock;
        lock.tryLock() >> true;
        def dspOrderDO = Mock(DspOrderDO);
        dspOrderDO.getOrderStatus() >> orderStatus;
        dspOrderRepository.find(_) >>  dspOrderDO;
        dspOrderDetailRepository.find(_) >> Mock(DspOrderDetailDO);

        UpdateOrderRemarkRequestType requestType = new UpdateOrderRemarkRequestType();
        requestType.setOrderSourceCode(1)

        when:
        executor.updateOrderRemark(requestType);

        then:
        Assert.assertTrue(Objects.equals(requestType.getOrderSourceCode(),1))

        where:
        flowSwitch    | orderStatus || expect
        Boolean.TRUE  | 220         || null
        Boolean.TRUE  | 240         || null
        Boolean.FALSE | 240         || null
    }


    SaaSUpdateDspOrderCommand getReq1() {
        def cmd = getReq()
        cmd.setDspOrderDetailDO(null)
        return cmd
    }

    SaaSUpdateDspOrderCommand getReq2() {
        SaasUpdateDspOrderInfo saasUpdateDspOrderInfo = buildSaasUpdateDspOrderInfo()
        SaaSFeeInfo saaSFeeInfo = new SaaSFeeInfo()
        saaSFeeInfo.setSupplierCurrency(null)
        saaSFeeInfo.setUserCurrency(null)
        saaSFeeInfo.setCostAmount(null)
        saasUpdateDspOrderInfo.setFeeInfo(saaSFeeInfo)
        saasUpdateDspOrderInfo.setXproductInfo(null)
        def saasUpdateDspOrderRequestType = new SaasUpdateDspOrderRequestType(null, null, saasUpdateDspOrderInfo, 222L);
        def cmd = SaaSUpdateDspOrderConverter.converter(saasUpdateDspOrderRequestType)
        return cmd
    }

    SaaSUpdateDspOrderCommand getReq() {
        SaasUpdateDspOrderInfo saasUpdateDspOrderInfo = buildSaasUpdateDspOrderInfo()
        def saasUpdateDspOrderRequestType = new SaasUpdateDspOrderRequestType(null, null, saasUpdateDspOrderInfo, 222L);
        def cmd = SaaSUpdateDspOrderConverter.converter(saasUpdateDspOrderRequestType)
        return cmd
    }


    SaasUpdateDspOrderInfo buildSaasUpdateDspOrderInfo(){
        SaasUpdateDspOrderInfo saasUpdateDspOrderInfo = new SaasUpdateDspOrderInfo()
        saasUpdateDspOrderInfo.setDspOrderId("222")
        saasUpdateDspOrderInfo.setVbkOrderId("111")
        saasUpdateDspOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.BOOKING.getCode())
        saasUpdateDspOrderInfo.setEstimatedUseTime(LocalDateUtils.toString(new Date(), "yyyy-MM-dd HH:mm:ss"))

        SaaSPoiInfo fromPoi = new SaaSPoiInfo()
        fromPoi.setAddress("beijing")
        fromPoi.setCityId("111")
        saasUpdateDspOrderInfo.setFromPoi(fromPoi)

        SaaSPoiInfo toPoi = new SaaSPoiInfo()
        toPoi.setAddress("beijing")
        toPoi.setCityId("111")
        saasUpdateDspOrderInfo.setToPoi(toPoi)

        SaaSExtendInfo extendInfo = new SaaSExtendInfo()
        extendInfo.setDriverRemark("司机可见备注")
        saasUpdateDspOrderInfo.setExtendInfo(extendInfo)

        return saasUpdateDspOrderInfo
    }


    DspOrderDO getOldDspOrderDO() {
        def order = new DspOrderDO()
        order.setDspOrderId("16211561979363356")
        order.setOrderSourceCode(OrderSourceCodeEnum.BOOKING.getCode())
        order.setSupplierId(222)
        return order
    }

    DspOrderDO getOldDspOrderDOTrip() {
        def order = new DspOrderDO()
        order.setDspOrderId("16211561979363356")
        order.setOrderSourceCode(OrderSourceCodeEnum.TRIP.getCode())
        order.setSupplierId(222)
        return order
    }

    DspOrderDetailDO getOldDspOrderDetailDO() {
        def orderDetail = new DspOrderDetailDO()
        orderDetail.setDspOrderId("16211561979363356")
        return orderDetail
    }

    DspOrderDO getNewDspOrderDO() {
        def order = new DspOrderDO()
        order.setDspOrderId("16211561979363356")
        return order
    }

    DspOrderDetailDO getNewDspOrderDetailDO() {
        def orderDetail = new DspOrderDetailDO()
        orderDetail.setDspOrderId("16211561979363356")
        return orderDetail
    }

    DspOrderVO getDspOrderVO() {
        DspOrderVO dspOrderVO = new DspOrderVO()
        dspOrderVO.setSupplierId(222)
        dspOrderVO.setOrderSourceCode(OrderSourceCodeEnum.TRIP.code)
        dspOrderVO.setOrderStatus(240)
        return dspOrderVO
    }

}
