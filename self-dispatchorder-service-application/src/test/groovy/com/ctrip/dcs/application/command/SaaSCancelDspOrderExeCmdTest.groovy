package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.CancelDspOrderCommand
import com.ctrip.dcs.application.provider.converter.SaaSCancelDspOrderConverter
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.gateway.DateZoneConvertGateway
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderOperateRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCancelDspOrderRequestType
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.Specification
import spock.lang.Unroll

class SaaSCancelDspOrderExeCmdTest extends Specification {

    def dspOrderRepository = Mock(DspOrderRepository)
    def dspOrderOperateRepository = Mock(DspOrderOperateRepository)
    def driverOrderGateway = Mock(DriverOrderGateway)
    def dateZoneConvertGateway = Mock(DateZoneConvertGateway)
    def distributedLockService = Mock(DistributedLockService)
    def businessTemplateInfoConfig = Mock(BusinessTemplateInfoConfig)
    def vbkOperationRecordGateway = Mock(VBKOperationRecordGateway)

    def executor = new SaaSCancelDspOrderExeCmd(
            dspOrderRepository: dspOrderRepository,
            dspOrderOperateRepository: dspOrderOperateRepository,
            driverOrderGateway: driverOrderGateway,
            dateZoneConvertGateway: dateZoneConvertGateway,
            distributedLockService: distributedLockService,
            businessTemplateInfoConfig:businessTemplateInfoConfig,
            vbkOperationRecordGateway: vbkOperationRecordGateway
    )

    @Unroll
    def "test execute"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        dspOrderRepository.find(_) >> dspOrderDO
        businessTemplateInfoConfig.getOperateUserNameDesc(_) >> "supplier%s";

        when: "执行校验方法"
        executor.execute(req)

        then: "验证校验结果"
        true

        where:
        req       |  lock | dspOrderDO
        getReq()  |  true | getDspOrderDO()

    }

    @Unroll
    def "test exception"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        dspOrderRepository.find(_) >> dspOrderDO

        when: "执行校验方法"
        executor.execute(req)

        then: "验证校验结果"

        def ex = thrown(BizException)
        ex.code == res

        where:
        req       |  lock | dspOrderDO          || res
        getReq()  |  true | getDspOrderDOException()  || "09015324"
        getReq()  |  false| getDspOrderDO()  || "10033"
        getReq1() |  true| getDspOrderDO()  || "09015325"

    }

    CancelDspOrderCommand getReq1() {
        def cmd = getReq()
        cmd.setSupplierId(222L)
        return cmd
    }

    CancelDspOrderCommand getReq() {
        def saasCancelDspOrderRequestType = new SaasCancelDspOrderRequestType(null, null, 1L, "1", "2", 3, "取消原因","",0);
        def cmd = SaaSCancelDspOrderConverter.converter(saasCancelDspOrderRequestType)
        return cmd
    }

    DspOrderDO getDspOrderDO() {
        def order = new DspOrderDO()
        order.setDspOrderId("16211561979363356")
        order.setOrderSourceCode(OrderSourceCodeEnum.BOOKING.getCode())
        order.setSupplierId(1)
        return order
    }

    DspOrderDO getDspOrderDOException() {
        def order = new DspOrderDO()
        order.setDspOrderId("16211561979363356")
        order.setOrderSourceCode(OrderSourceCodeEnum.TRIP.getCode())
        order.setSupplierId(222)
        return order
    }
}
