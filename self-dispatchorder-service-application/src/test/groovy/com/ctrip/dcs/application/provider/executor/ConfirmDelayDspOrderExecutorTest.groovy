package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.command.ConfirmDelayDspOrderExeCmd
import com.ctrip.dcs.application.command.validator.ValidateException
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.exception.OrderStatusException
import com.ctrip.dcs.self.dispatchorder.interfaces.ConfirmDelayDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.ConfirmDelayDspOrderResponseType
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ConfirmDelayDspOrderExecutorTest extends Specification {
    @Mock
    Logger logger
    @Mock
    ConfirmDelayDspOrderExeCmd cmd
    @InjectMocks
    ConfirmDelayDspOrderExecutor confirmDelayDspOrderExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        ConfirmDelayDspOrderRequestType request = new ConfirmDelayDspOrderRequestType()
        request.setDspOrderId("dspOrderId")
        request.setDuid("duid")
        request.setDriverId(1L)
        request.setTransportGroupId(1L)

        when:
        ConfirmDelayDspOrderResponseType result = confirmDelayDspOrderExecutor.execute(request)

        then:
        result.getResponseResult().getReturnCode() == "200"
    }

    def "test execute exception"() {
        given:
        ConfirmDelayDspOrderRequestType request = new ConfirmDelayDspOrderRequestType()
        request.setDspOrderId("dspOrderId")
        request.setDuid("duid")
        request.setDriverId(1L)
        request.setTransportGroupId(1L)
        when(cmd.execute(any())).thenThrow(new ValidateException(ErrorCode.BUILD_CHECK_CHAIN_ERROR)).thenThrow(new OrderStatusException(ErrorCode.BUILD_CHECK_SOURCE_ERROR)).thenThrow(new RuntimeException("test exception")).thenThrow(ErrorCode.CREATE_DRIVER_ORDER_ERROR.getBizException())
        when:
        ConfirmDelayDspOrderResponseType result1 = confirmDelayDspOrderExecutor.execute(request)
        ConfirmDelayDspOrderResponseType result2 = confirmDelayDspOrderExecutor.execute(request)
        ConfirmDelayDspOrderResponseType result3 = confirmDelayDspOrderExecutor.execute(request)
        ConfirmDelayDspOrderResponseType result4 = confirmDelayDspOrderExecutor.execute(request)

        then:
        result1.getResponseResult().getReturnCode() == ErrorCode.BUILD_CHECK_CHAIN_ERROR.getCode()
        result2.getResponseResult().getReturnCode() == ErrorCode.BUILD_CHECK_SOURCE_ERROR.getCode()
        result3.getResponseResult().getReturnCode() == ErrorCode.CONFIRM_DELAY_DSP_ORDER_ERROR.getCode()
        result4.getResponseResult().getReturnCode() == ErrorCode.CREATE_DRIVER_ORDER_ERROR.getCode()


    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme