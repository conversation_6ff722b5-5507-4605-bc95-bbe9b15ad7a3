package com.ctrip.dcs.application.listener.grab

import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository
import com.ctrip.igt.framework.common.clogging.Logger
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class VBKGrabTaskTaskFinishListenerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository
    @Mock
    VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository
    @Mock
    MessageProviderService messageProducer
    @Mock
    BaseMessage message
    @InjectMocks
    VBKGrabTaskTaskFinishListener vBKGrabTaskTaskFinishListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test consume Message"() {
        given:
        when(vbkDriverGrabTaskRepository.queryByGrabStatusPage(anyInt(), anyInt(), anyInt())).thenReturn([new VBKDriverGrabTaskDO(vbkGrabTaskId: "1")])
        when(vbkDriverGrabTaskRepository.countByGrabTaskStatus(anyInt())).thenReturn(10)
        when(vbkDriverGrabOrderRepository.countByTaskIdAndStatus(anyString(), anyInt())).thenReturn(0)
        when(message.getIntProperty("pageSize")).thenReturn(10)

        when:
        def result = vBKGrabTaskTaskFinishListener.consumeMessage(message)

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme