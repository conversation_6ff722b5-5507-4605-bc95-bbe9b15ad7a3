package com.ctrip.dcs.application.provider.executor.dispatchergrab

import com.ctrip.dcs.application.command.dispatchergrab.CancelDispatcherGrabOrderExeCmd
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDispatcherGrabOrdersRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDispatcherGrabOrdersResponseType
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class CancelDispatcherGrabOrdersExecutorTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CancelDispatcherGrabOrderExeCmd cmd
    @Mock
    CancelDispatcherGrabOrdersRequestType request
    @InjectMocks
    CancelDispatcherGrabOrdersExecutor cancelDispatcherGrabOrdersExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute success"() {
        given:
        when(request.getUserOrderId()).thenReturn("1")
        when:
        CancelDispatcherGrabOrdersResponseType result = cancelDispatcherGrabOrdersExecutor.execute(request)

        then:
        result.getResponseResult().isSuccess()
    }

    def "test execute fail"() {
        given:
        when(request.getUserOrderId()).thenReturn(orderId)
        when(cmd.execute(any())).thenThrow(error)
        when:
        CancelDispatcherGrabOrdersResponseType result = cancelDispatcherGrabOrdersExecutor.execute(request)

        then:
        result.getResponseResult().getReturnCode() == code

        where:
        orderId | error                                                    || code
        ""      | ErrorCode.ERROR_PARAMS.getBizException()                 || ErrorCode.ERROR_PARAMS.getCode()
        "1"     | ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getBizException() || ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getCode()
        "1"     | new RuntimeException()                                   || "500"

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme