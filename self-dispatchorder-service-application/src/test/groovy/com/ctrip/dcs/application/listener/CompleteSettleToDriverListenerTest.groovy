package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.command.CreateScheduleExeCmd
import com.ctrip.dcs.application.command.UpdateOrderSettlementBeforeTakenCmd
import com.ctrip.dcs.application.command.api.UpdateOrderSettlementBeforeTakenCommand
import com.ctrip.dcs.domain.common.constants.ConfigKey
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.SendEmailService
import com.ctrip.dcs.domain.dsporder.value.OrderSettlePriceVO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderPushRuleDO
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig
import com.ctrip.dcs.infrastructure.handler.DispatcherGrabOrderPushRuleHandler
import com.ctrip.igt.framework.common.clogging.Logger
import org.springframework.beans.factory.annotation.Autowired
import qunar.tc.qmq.NeedRetryException
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class CompleteSettleToDriverListenerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    UpdateOrderSettlementBeforeTakenCmd cmd
    @Mock
    BaseMessage message
    @Mock
    DispatcherGrabOrderPushRuleHandler dispatcherGrabOrderPushRuleHandler;
    @Mock
    private CreateScheduleExeCmd createScheduleExeCmd;
    @Mock
    private SendEmailService sendEmailService;
    @Mock
    private BusinessTemplateInfoConfig businessTemplateInfoConfig;
    @InjectMocks
    CompleteSettleToDriverListener completeSettleToDriverListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message"() {
        given:
        when(cmd.execute(any())).thenThrow(new RuntimeException("test"))

        when:
        completeSettleToDriverListener.onMessage(message)

        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.message == "CompleteSettleToDriverListenerError"

    }

    def "test createSchedule"() {
        given:
        UpdateOrderSettlementBeforeTakenCommand command = new UpdateOrderSettlementBeforeTakenCommand(dspOrderId: "1", supplierId: supplierId, cityId: cityId, categoryCode: categoryCode, carTypeId: carTypeId, settleToDriver: 0)
        OrderSettlePriceVO price = new OrderSettlePriceVO(preDriverGuideCurrency: preDriverGuideCurrency, preDriverGuideAmount: preDriverGuideAmount)
        when(dispatcherGrabOrderPushRuleHandler.queryGrabOrderPushRule(1L, 1L, CategoryCodeEnum.FROM_AIRPORT.getType(), 117L)).thenReturn(new GrabDspOrderPushRuleDO())
        when(businessTemplateInfoConfig.getValueByKey(ConfigKey.COMMON_EMAIL_SENDER)).thenReturn("<EMAIL>")
        when(businessTemplateInfoConfig.getValueByKey(ConfigKey.COMMON_NAME_SENDER)).thenReturn("携程专车")
        when(businessTemplateInfoConfig.getValueByKey(ConfigKey.GRAB_PUSH_RULE_TITLE)).thenReturn("【境外抢单司机结算规则未配置】")
        when(businessTemplateInfoConfig.getValueByKey(ConfigKey.GRAB_PUSH_RULE_CONTEND)).thenReturn("<entry><content><html><![CDATA[【供应商ID%s】&【城市%s】未配置司机结算规则，请尽快配置]]></html></content></entry>")
        when(businessTemplateInfoConfig.getValueByKey(ConfigKey.GRAB_PUSH_RULE_RECEIVER_EMAIL)).thenReturn("<EMAIL>")
        when:
        def result = completeSettleToDriverListener.createSchedule(command, price)

        then:
        result == res

        where:
        supplierId | cityId | categoryCode                            | carTypeId | preDriverGuideCurrency | preDriverGuideAmount || res
        null       | null   | null                                    | null      | null                   | null                 || null
        1L         | null   | null                                    | null      | null                   | null                 || null
        1L         | 1      | null                                    | null      | null                   | null                 || null
        1L         | 1      | CategoryCodeEnum.FROM_AIRPORT.getType() | null      | null                   | null                 || null
        1L         | 1      | CategoryCodeEnum.FROM_AIRPORT.getType() | 117L      | null                   | null                 || null
        1L         | 1      | CategoryCodeEnum.FROM_AIRPORT.getType() | 117L      | "CNY"                  | BigDecimal.ONE       || null

    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme