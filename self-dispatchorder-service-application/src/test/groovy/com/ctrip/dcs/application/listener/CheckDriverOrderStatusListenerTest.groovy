package com.ctrip.dcs.application.listener

import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DriverOrderDao
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDao
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.GrabDspOrderDriverIndexDao
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.GrabDspOrderSnapshotDao
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DrvOrderPO
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderPO
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabDspOrderDriverIndexPO
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabDspOrderSnapshotPO
import org.springframework.beans.factory.annotation.Autowired
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class CheckDriverOrderStatusListenerTest extends Specification {

    DspOrderDao dspOrderDao = Mock(DspOrderDao)

    DriverOrderDao driverOrderDao = Mock(DriverOrderDao)

    GrabDspOrderSnapshotDao grabDspOrderSnapshotDao = Mock(GrabDspOrderSnapshotDao)

    GrabDspOrderDriverIndexDao grabDspOrderDriverIndexDao = Mock(GrabDspOrderDriverIndexDao)

    CheckDriverOrderStatusListener listener = new CheckDriverOrderStatusListener(dspOrderDao: dspOrderDao, driverOrderDao: driverOrderDao, grabDspOrderSnapshotDao: grabDspOrderSnapshotDao, grabDspOrderDriverIndexDao: grabDspOrderDriverIndexDao)

    def "test onMessage"() {

        given:
        Message message = new BaseMessage()
        message.setProperty("dspOrderId", "1")
        DspOrderPO dspOrder = new DspOrderPO(orderStatus: dspOrderStatus, driverOrderId: "1")
        DrvOrderPO drvOrder1 = new DrvOrderPO(orderStatus: drvOrderStatus1, drvOrderId: "1")
        DrvOrderPO drvOrder2 = new DrvOrderPO(orderStatus: drvOrderStatus2, drvOrderId: "2")
        dspOrderDao.findByDspOrderId(_ as String) >> dspOrder
        driverOrderDao.queryByDspOrderId(_ as String) >> [drvOrder1, drvOrder2]

        when:
        def flag = listener.onMessage(message)

        then:
        flag == result

        where:
        dspOrderStatus | drvOrderStatus1 | drvOrderStatus2 || result
        240            | 240             | 900             || null
        240            | 900             | 900             || null
        240            | 900             | 240             || null
        240            | 500             | 900             || null
        900            | 240             | 240             || null
        900            | 900             | 900             || null
    }

    def "test isDriverOrderNoCancel"() {

        given:
        DspOrderPO dspOrder = new DspOrderPO(orderStatus: dspOrderStatus)
        DrvOrderPO drvOrder = new DrvOrderPO(orderStatus: drvOrderStatus)

        when:
        boolean  flag = listener.isDriverOrderNoCancel(dspOrder, [drvOrder])

        then:
        flag == result

        where:
        dspOrderStatus | drvOrderStatus || result
        900            | 900            || false
        240            | 240            || false
        900            | 240            || true
    }

    def "test isDriverOrderMultiConfirmed"() {

        given:
        DspOrderPO dspOrder = new DspOrderPO(orderStatus: dspOrderStatus)
        DrvOrderPO drvOrder = new DrvOrderPO(orderStatus: drvOrderStatus)

        when:
        boolean  flag = listener.isDriverOrderMultiConfirmed(dspOrder, [drvOrder])

        then:
        flag == result

        where:
        dspOrderStatus | drvOrderStatus || result
        240            | 240            || false
        240            | 900            || true
        900            | 240            || false
    }

    def "test isNotEqualDriverOrderStatus"() {

        given:
        DspOrderPO dspOrder = new DspOrderPO(orderStatus: dspOrderStatus, driverOrderId: "1")
        DrvOrderPO drvOrder = new DrvOrderPO(orderStatus: drvOrderStatus, drvOrderId: "1")

        when:
        boolean  flag = listener.isNotEqualDriverOrderStatus(dspOrder, [drvOrder])

        then:
        flag == result

        where:
        dspOrderStatus | drvOrderStatus || result
        240            | 240            || false
        240            | 900            || true
        900            | 240            || false
    }

    def "test isNotEqualGrabOrderSnapshotStatus"() {

        given:
        DspOrderPO dspOrder = new DspOrderPO(orderStatus: dspOrderStatus, driverOrderId: "1")
        GrabDspOrderSnapshotPO snapshot = new GrabDspOrderSnapshotPO(grabType: grabType, grabStatus: grabStatus)
        GrabDspOrderDriverIndexPO index = new GrabDspOrderDriverIndexPO(isValid: isValid)

        when:
        boolean  flag = listener.isNotEqualGrabOrderSnapshotStatus(dspOrder, snapshot, [index])

        then:
        flag == result

        where:
        dspOrderStatus | grabType | grabStatus | isValid || result
        200            | 1        | 10         | 0       || false
        240            | 1        | 10         | 0       || true
        240            | 1        | 30         | 0       || true
        240            | 1        | 30         | 2       || false
        220            | 2        | 20         | 1       || false
        240            | 2        | 20         | 1       || true
        240            | 2        | 30         | 1       || true
        240            | 2        | 30         | 2       || false

    }
}
