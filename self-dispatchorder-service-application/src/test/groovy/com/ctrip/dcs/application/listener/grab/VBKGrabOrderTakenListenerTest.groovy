package com.ctrip.dcs.application.listener.grab

import com.ctrip.dcs.application.command.CancelVBKGrabTaskExeCmd
import com.ctrip.dcs.application.command.UpdateOrderSettleToDriverBeforeTakenCmd
import com.ctrip.dcs.application.command.UpdateOrderSettlementBeforeTakenCmd
import com.ctrip.dcs.application.command.api.UpdateOrderSettlementBeforeTakenCommand
import com.ctrip.dcs.application.service.DriverConfirmedService
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.igt.framework.common.clogging.Logger
import org.junit.Assert
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class VBKGrabOrderTakenListenerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    SelfOrderQueryGateway selfOrderQueryGateway
    @Mock
    UpdateOrderSettleToDriverBeforeTakenCmd updateOrderSettleToDriverBeforeTakenCmd
    @Mock
    DistributedLockService distributedLockService
    @Mock
    CancelVBKGrabTaskExeCmd cancelVBKGrabTaskExeCmd
    @Mock
    VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository
    @Mock
    MessageProviderService messageProducer
    @Mock
    VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository
    @Mock
    BaseMessage message
    @Mock
    DspOrderVO dspOrder
    @Mock
    DriverConfirmedService driverConfirmedService
    @InjectMocks
    VBKGrabOrderTakenListener vBKGrabOrderTakenListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test driver Confirm"() {
        given:
        when(message.times()).thenReturn(1)
        when(message.getStringProperty("dspOrderId")).thenReturn("")

        when:
        def result = vBKGrabOrderTakenListener.driverConfirm(message)

        then:
        result == null
    }

    def "test driver Car Confirm"() {
        given:
        when(message.times()).thenReturn(1)
        when(message.getStringProperty("dspOrderId")).thenReturn("")
        when:
        def result = vBKGrabOrderTakenListener.driverCarConfirm(message)

        then:
        result == null
    }

    def "test saas Driver Car Confirm"() {
        given:
        when(message.times()).thenReturn(1)
        when(message.getStringProperty("dspOrderId")).thenReturn("")
        when:
        def result = vBKGrabOrderTakenListener.saasDriverCarConfirm(message)

        then:
        result == null
    }

    def "saasDriverCarConfirmDrvLevel"() {
        given:
        when(message.times()).thenReturn(1)
        when(message.getStringProperty("dspOrderId")).thenReturn("")
        when:
        def result = vBKGrabOrderTakenListener.saasDriverCarConfirmDrvLevel(message)

        then:
        result == null
    }

    def "test old Driver Car Confirm"() {
        given:
        when(message.times()).thenReturn(1)
        when(message.getStringProperty("supplyOrderIds")).thenReturn(",")

        when:
        def result = vBKGrabOrderTakenListener.oldDriverCarConfirm(message)

        then:
        result == null
    }

    def "test old Driver Confirm"() {
        given:
        when(message.times()).thenReturn(1)
        when(message.getStringProperty("supplyOrderId")).thenReturn("")
        when:
        def result = vBKGrabOrderTakenListener.oldDriverConfirm(message)

        then:
        result == null
    }
//
    def "test old Driver Confirm Settle To Driver"() {
        given:
        when(message.times()).thenReturn(1)
        when(message.getStringProperty("supplyOrderId")).thenReturn("")
        when(message.getStringProperty("msg")).thenReturn("dispatcher_confirmed")
        when(selfOrderQueryGateway.queryDspOrder(Mockito.anyString())).thenReturn(dspOrder)
        when(dspOrder.getCategoryCode()).thenReturn(CategoryCodeEnum.FROM_AIRPORT)

        when:
        def result = vBKGrabOrderTakenListener.oldDriverConfirmSettleToDriver(message)

        then:
        result == null
    }

    def "test Driver Confirm"() {
        given:
        when(message.times()).thenReturn(1)
        when(message.getStringProperty("supplyOrderId")).thenReturn("")
        when(message.getStringProperty("msg")).thenReturn("dispatcher_confirmed")
        when(selfOrderQueryGateway.queryDspOrderForVBKGrab(Mockito.anyString(), Mockito.anyBoolean())).thenReturn(dspOrder)
        when(dspOrder.getCategoryCode()).thenReturn(CategoryCodeEnum.FROM_AIRPORT)

        when:
        def result = vBKGrabOrderTakenListener.driverConfirm("1")

        then:
        result == null
    }


    def "test getGrabTaskStatus"() {
        given:
        DspOrderVO dpsVO = new DspOrderVO()
        dpsVO.setOrderConfirmRecordDuid(orderConfirmRecordDuid)

        when:
        def result = vBKGrabOrderTakenListener.getGrabTaskStatus(dpsVO, 2)

        then:
        result == res

        where:
        orderConfirmRecordDuid                                                          || res
//        "881837828056045519-v2.0-2020082753-90-1:1:1-************-1-1-302-0"            || 2
//        "881837828056045519-v2.0-2020082753-90-1:1:1-************-1-60-302-0"           || 4
//        "84744441661259843-1084596434892521732-2084596434892521734-1-11012-32-60-0-0-0" || 4
//        "84744441661259843-1084596434892521732-2084596434892521734-1-11012-32-1-0-0-0"  || 2
        "null-null-null-null-null-null-null-0-0-0"                                      || 2


    }


    def "test updateOrderDetailSettlementInfo"() {
        given:
        DspOrderVO dpsVO = new DspOrderVO()
        dpsVO.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT)
        dpsVO.setOrderConfirmRecordDuid(orderConfirmRecordDuid)
        when:
        def result = vBKGrabOrderTakenListener.updateOrderDetailSettlementInfo(dpsVO, 2, 2)

        then:
        result == res

        where:
        orderConfirmRecordDuid                                               || res
        "881837828056045519-v2.0-2020082753-90-1:1:1-************-1-1-302-0" || null
    }

    def "test driverConfirmDrvLevel"() {
        given:
        DspOrderVO dspOrderVO = new DspOrderVO()
        dspOrderVO.setDriverId(1L)
        dspOrderVO.setConfirmRecordId(1L)
        when(selfOrderQueryGateway.queryDspOrderForVBKGrab(Mockito.anyString(), Mockito.anyBoolean())).thenReturn(dspOrderVO)
        when:
        vBKGrabOrderTakenListener.driverConfirmDrvLevel("1111")

        then:
        Assert.assertTrue(dspOrderVO.getDriverId() == 1)
    }


//
//    def "test deal Message Logic"() {
//        given:
//        when(distributedLockService.getLock(anyString())).thenReturn(new DistributedLockServiceImpl.RedisDistributedLock(null))
//        when(vbkDriverGrabTaskRepository.queryTaskIdByVBKGrabTaskIds(any(), anyInt())).thenReturn(["String"])
//        when(vbkDriverGrabOrderRepository.queryByDspOrderId(anyString(), anyInt())).thenReturn([new VBKDriverGrabOrderDO(vbkGrabTaskId: "vbkGrabTaskId", supplierId: 1l, dspOrderId: "dspOrderId")])
//        when(vbkDriverGrabOrderRepository.countByTaskIdAndStatus(anyString(), anyInt())).thenReturn(0)
//        when(vbkDriverGrabOrderRepository.finishBatchByDspOrderIds(anyInt(), anyInt(), any(), anyString())).thenReturn(0)
//
//        when:
//        vBKGrabOrderTakenListener.dealMessageLogic("supplyOrderId", 0, 0, "driverOrderId")
//
//        then:
//        false//todo - validate something
//    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme