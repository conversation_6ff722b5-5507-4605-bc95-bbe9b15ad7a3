package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.GrabDspOrderDriverIndexDao
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabDspOrderDriverIndexPO
import com.ctrip.dcs.self.dispatchorder.interfaces.GrabDspOrderDriverIndexDTO
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabDspOrderDriverIndexRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabDspOrderDriverIndexResponseType
import com.ctrip.igt.CommonAllianceDTO
import com.ctrip.igt.CommonGPSDTO
import com.ctrip.igt.CommonMiniProgramDTO
import com.ctrip.igt.CommonUBTDTO
import com.ctrip.igt.RequestHeader
import com.ctrip.igt.ResponseResult
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.igt.framework.infrastructure.exception.ServiceValidationException
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import com.ctriposs.baiji.rpc.mobile.common.types.ExtensionFieldType
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class UpdateGrabDspOrderDriverIndexExecutorTest extends Specification {
    @Mock
    GrabDspOrderDriverIndexDao grabDspOrderDriverIndexDao
    @InjectMocks
    UpdateGrabDspOrderDriverIndexExecutor updateGrabDspOrderDriverIndexExecutor

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    def "test execute"() {
        given:
        GrabDspOrderDriverIndexPO po = new GrabDspOrderDriverIndexPO(isValid: 0, isBroadcast: 0)
        when(grabDspOrderDriverIndexDao.query(anyString(), any(List<Long>.class))).thenReturn([new GrabDspOrderDriverIndexPO(isValid: 0, isBroadcast: 0)])
        UpdateGrabDspOrderDriverIndexRequestType requestType = new UpdateGrabDspOrderDriverIndexRequestType(indexes: [new GrabDspOrderDriverIndexDTO(driverId: 1, dspOrderId: 1, isValid: 1, isBroadcast: 1)])
        when:
        UpdateGrabDspOrderDriverIndexResponseType result = updateGrabDspOrderDriverIndexExecutor.execute(requestType)

        then:
        result.getResponseResult().getReturnCode() == "200"
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme