package com.ctrip.dcs.application.command.converter

import com.ctrip.dcs.application.command.dto.DispatcherGrabOrderDTO
import com.ctrip.dcs.domain.common.enums.DispatcherGrabOrderStatusEnum
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO
import spock.lang.*

/**
 * <AUTHOR>
 */
class DispatcherGrabOrderDOConverterTest extends Specification {
    DispatcherGrabOrderDOConverter dispatcherGrabOrderDOConverter = new DispatcherGrabOrderDOConverter()

    def "test to Dispatcher Grab Order PO"() {
        given:
        DispatcherGrabOrderDTO dto = new DispatcherGrabOrderDTO()
        dto.dspOrderId = "dspOrderId"
        dto.userOrderId = "userOrderId"
        dto.supplierId = 1l
        dto.transportGroupId = 1l
        dto.duid = "duid"
        dto.subSkuId = 2
        dto.lastConfirmTime = new Date()

        when:
        List<DispatcherGrabOrderDO> result = dispatcherGrabOrderDOConverter.toDispatcherGrabOrderPO([dto])

        then:
        result.size() == 1
        with(result.get(0)) {
            userOrderId == "userOrderId"
            dspOrderId == "dspOrderId"
            supplierId == 1l
            transportGroupId == 1l
            subSku == 2
            duid == "duid"
        }
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme