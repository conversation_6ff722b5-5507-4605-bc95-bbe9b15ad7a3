package com.ctrip.dcs.application.provider.converter

import com.ctrip.dcs.application.command.api.CreateIvrRecordCommand
import com.ctrip.dcs.domain.dsporder.entity.IvrRecordDO
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateIvrRecordRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.IvrRecordInfo
import spock.lang.*

class CreateIvrRecordConverterTest extends Specification {

    def "test converter"() {
        when:
        CreateIvrRecordRequestType requestType = new CreateIvrRecordRequestType();
        IvrRecordInfo data = new IvrRecordInfo();
        requestType.setData(data);
        CreateIvrRecordCommand result = CreateIvrRecordConverter.converter(requestType)

        then:
        result != null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme