package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.query.AvailableDriversExeQry
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryAvailableDriversRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryAvailableDriversResponseType
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import java.sql.Driver

import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class QueryAvailableDriversExecutorTest extends Specification {
    @Mock
    AvailableDriversExeQry availableDriversExeQry
    @Mock
    AbstractValidator validator
    @Mock
    SortModel sortModel
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverVO driverVO
    @InjectMocks
    QueryAvailableDriversExecutor queryAvailableDriversExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(sortModel.getScore()).thenReturn(1D)
        when(sortModel.getModel()).thenReturn(dspModelVO)
        when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(driverVO.getDriverId()).thenReturn(1L)
        when(dspOrderVO.getDspOrderId()).thenReturn("dspOrderId")
        when(availableDriversExeQry.query(any())).thenReturn([sortModel])
        QueryAvailableDriversRequestType request = new QueryAvailableDriversRequestType()
        request.setDspOrderId("dspOrderId")
        request.setDuid("duid")
        when:
        QueryAvailableDriversResponseType result = queryAvailableDriversExecutor.execute(request)

        then:
        result.getResponseResult().getReturnCode() == "200"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme