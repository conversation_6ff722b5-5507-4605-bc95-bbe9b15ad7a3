package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.command.SelectGrabOrderExeCmd
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class SelectGrabOrderListenerTest extends Specification {
    @Mock
    SelectGrabOrderExeCmd selectGrabOrderExeCmd
    @InjectMocks
    SelectGrabOrderListener selectGrabOrderListener

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    def "test on Message"() {
        when:
        def result = selectGrabOrderListener.onMessage(new BaseMessage())

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme