package com.ctrip.dcs.application.provider.executor


import com.ctrip.dcs.self.dispatchorder.interfaces.QueryDriverOrCarInServiceOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryDriverOrCarInServiceOrderResponseType
import org.mockito.InjectMocks
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class QueryDriverOrCarInServiceOrderCountExecutorTest extends Specification {
    @InjectMocks
    QueryDriverOrCarInServiceOrderCountExecutor queryDriverOrCarInServiceOrderCountExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
         when:
        QueryDriverOrCarInServiceOrderRequestType requestType = new QueryDriverOrCarInServiceOrderRequestType();
        QueryDriverOrCarInServiceOrderResponseType result = queryDriverOrCarInServiceOrderCountExecutor.execute(requestType)

        then:
        result.getOrderNum() == 0
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme