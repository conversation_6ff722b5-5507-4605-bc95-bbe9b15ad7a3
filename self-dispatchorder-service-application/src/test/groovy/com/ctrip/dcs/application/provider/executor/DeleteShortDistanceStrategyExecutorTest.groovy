package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.command.ShortDistanceStrategyExeCmd
import com.ctrip.dcs.self.dispatchorder.interfaces.DeleteShortDistanceStrategyRequestType
import spock.lang.Specification

class DeleteShortDistanceStrategyExecutorTest extends Specification {
    def strategyExeCmd = Mock(ShortDistanceStrategyExeCmd)
    def executor = new DeleteShortDistanceStrategyExecutor(
            strategyExeCmd: strategyExeCmd
    )

    def "test executor"() {
        given:
        strategyExeCmd.delete(_) >> result

        when:
        def response = executor.execute(new DeleteShortDistanceStrategyRequestType())

        then:
        response.responseResult.returnCode == returnCode

        where:
        returnCode | result
        "200"      | true
        "500"      | false
    }


    def "test execute exp"() {
        given:
        strategyExeCmd.delete(_) >> { throw new Exception("error") }

        when:
        def result = executor.execute(new DeleteShortDistanceStrategyRequestType())

        then:
        result.responseResult.returnCode == "500"
    }
}
