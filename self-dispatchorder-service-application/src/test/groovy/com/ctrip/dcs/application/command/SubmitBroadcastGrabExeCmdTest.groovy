package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.SubmitGrabOrderCommand
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotStatusEnum
import com.ctrip.dcs.domain.common.enums.GrabOrderType
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.IdempotentCheckService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO
import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import javax.annotation.Resource

import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class SubmitBroadcastGrabExeCmdTest extends Specification {
    @Mock
    MessageProviderService messageProvider
    @Mock
    IdempotentCheckService idempotentCheckService
    @Mock
    GrabDspOrderSnapshotRepository grabDspOrderSnapshotRepository
    @Mock
    GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository
    @Mock
    ConfigService broadcastGrabConfig
    @Mock
    SubmitBroadcastExeCmd submitBroadcastExeCmd
    @Mock
    SubmitGrabCentreExeCmd submitGrabCentreExeCmd
    @Mock
    QueryDriverService queryDriverService;
    @Mock
    SysSwitchConfigGateway sysSwitchConfigGateway;
    @InjectMocks
    SubmitBroadcastGrabExeCmd submitBroadcastGrabExeCmd

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    def "test execute"() {
        given:
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)
        when(grabDspOrderSnapshotRepository.query(anyString())).thenReturn(new GrabDspOrderSnapshotDO(cityId: 1l, tipsDelaySecond: 0, grabStatus: GrabDspOrderSnapshotStatusEnum.INIT))
        when(grabDspOrderDriverIndexRepository.query(anyString(), any(List<Long>.class))).thenReturn([new GrabDspOrderDriverIndexDO(dspOrderId: "dspOrderId", duid: "duid")])
        when(broadcastGrabConfig.getString(anyString(), anyString())).thenReturn(city)
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)
        when(broadcastGrabConfig.getMap(anyString())).thenReturn(["getMapResponse": "getMapResponse"])

        when:
        def result = submitBroadcastGrabExeCmd.execute(new SubmitGrabOrderCommand(1l, "orderId", "duid", grabOrderType, "nonPremiumOrderId", ""))

        then:
        result == res

        where:
        city | grabOrderType             || res
        ""   | GrabOrderType.BROADCAST   || null
        ""   | GrabOrderType.GRAB_CENTRE || null
        "1"  | GrabOrderType.GRAB_CENTRE || null
    }
//
    def "test execute 2"() {
        given:
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)
        when(grabDspOrderDriverIndexRepository.query(anyString(), any(List<Long>.class))).thenReturn([new GrabDspOrderDriverIndexDO(dspOrderId: "dspOrderId", duid: "8984431463809168-108984431463809169-208984431463809170-1-11-50-5-0-0-0", isValid: 1)])
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)
        when(broadcastGrabConfig.getMap(anyString())).thenReturn(["getMapResponse": "getMapResponse"])

        when:
        def result = submitBroadcastGrabExeCmd.execute(new SubmitGrabOrderCommand(1l, "orderId", "8984431463809168-108984431463809169-208984431463809170-1-11-50-5-0-0-0", GrabOrderType.BROADCAST, "nonPremiumOrderId", ""), new GrabDspOrderSnapshotDO(tipsDelaySecond: 0, grabStatus: GrabDspOrderSnapshotStatusEnum.GRAB))

        then:
        result == null
    }

    def "test is Grayscale City"() {
        given:
        when(broadcastGrabConfig.getString(anyString(), anyString())).thenReturn(config)

        when:
        boolean result = submitBroadcastGrabExeCmd.isGrayscaleCity(1l)

        then:
        result == flag

        where:
        config || flag
        ""     || false
        "2"    || false
        "1"    || true
        "1,2"  || true
        "all"  || true
    }

    def "test validate"() {
        when:
        boolean result = submitBroadcastGrabExeCmd.validate(snapshot, indexes)

        then:
        result == flag
        where:
        snapshot                                                                      | indexes                                     || flag
        null                                                                          | []                                          || false
        new GrabDspOrderSnapshotDO(grabStatus: GrabDspOrderSnapshotStatusEnum.CANCEL) | []                                          || false
        new GrabDspOrderSnapshotDO(grabStatus: GrabDspOrderSnapshotStatusEnum.CANCEL) | [new GrabDspOrderDriverIndexDO()]           || false
        new GrabDspOrderSnapshotDO(grabStatus: GrabDspOrderSnapshotStatusEnum.GRAB)   | [new GrabDspOrderDriverIndexDO(isValid: 0)] || false
        new GrabDspOrderSnapshotDO(grabStatus: GrabDspOrderSnapshotStatusEnum.GRAB)   | [new GrabDspOrderDriverIndexDO(isValid: 1)] || true
    }

    def "test send Grab Fail Event"() {
        given:
        when(broadcastGrabConfig.getMap(anyString())).thenReturn(["getMapResponse": "getMapResponse"])

        when:
        def result = submitBroadcastGrabExeCmd.sendGrabFailEvent(new SubmitGrabOrderCommand(1l, "orderId", "duid", GrabOrderType.BROADCAST, "nonPremiumOrderId", ""), new GrabDspOrderSnapshotDO())

        then:
        result == null
    }

    def "test is Select"() {
        given:
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)

        when:
        boolean result = submitBroadcastGrabExeCmd.isSelect("dspOrderId", 0)

        then:
        result == true
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme