package com.ctrip.dcs.application.listener

import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.gateway.PhoneBridgeServiceGateway
import com.ctrip.dcs.domain.dsporder.gateway.PurchaseSupplyOrderGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import qunar.tc.qmq.NeedRetryException
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class BindCarAndTakenListenerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    PurchaseSupplyOrderGateway purchaseSupplyOrderGateway
    @Mock
    DspOrderRepository dspOrderRepository
    @Mock
    DspOrderConfirmRecordRepository confirmRecordRepository
    @Mock
    PhoneBridgeServiceGateway phoneBridgeRepository
    @Mock
    DspOrderDO dspOrderDO
    @Mock
    DspOrderConfirmRecordVO dspOrderConfirmRecordVO
    @Mock
    BaseMessage message
    @InjectMocks
    BindCarAndTakenListener bindCarAndTakenListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test sync Purchase Error"() {
        given:
        when(dspOrderRepository.find(anyString())).thenReturn(dspOrderDO)
        when(confirmRecordRepository.find(anyLong())).thenReturn(dspOrderConfirmRecordVO)
        when(message.getStringProperty(anyString())).thenReturn("1")
        when(purchaseSupplyOrderGateway.confirm(any())).thenThrow(new RuntimeException("test"))

        when:
        bindCarAndTakenListener.syncPurchase(message)

        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "BindCarAndTakenListenerError"
    }

    def "test sync Purchase"() {
        given:
        when(dspOrderRepository.find(anyString())).thenReturn(dspOrderDO)
        when(confirmRecordRepository.find(anyLong())).thenReturn(dspOrderConfirmRecordVO)
        when(message.getStringProperty(anyString())).thenReturn("1")

        when:
        def result = bindCarAndTakenListener.syncPurchase(message)

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme