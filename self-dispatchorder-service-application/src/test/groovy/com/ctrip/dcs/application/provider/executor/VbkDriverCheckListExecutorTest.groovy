package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.command.VbkDriverCheckListExeCmd
import com.ctrip.dcs.application.command.dto.DriverCheckDTO
import com.ctrip.dcs.application.command.dto.DriverCheckListResDTO
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkDriverCheckListRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkDriverCheckListResponseType
import com.ctrip.igt.framework.common.clogging.Logger
import com.google.common.collect.Lists
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class VbkDriverCheckListExecutorTest extends Specification {

    @Mock
    Logger logger

    @Mock
    VbkDriverCheckListExeCmd vbkDriverCheckListExeCmd

    @InjectMocks
    VbkDriverCheckListExecutor vbkDriverCheckListExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        List<DriverCheckDTO> list = Lists.newArrayList()
        def driverCheckDTO = new DriverCheckDTO()
        driverCheckDTO.setDriverId(123)
        list.add(driverCheckDTO)
        def checkListResDTO = new DriverCheckListResDTO(0, 0)
        checkListResDTO.setList(list)
        when(vbkDriverCheckListExeCmd.execute(any())).thenReturn(checkListResDTO)

        when:
        VbkDriverCheckListResponseType result = vbkDriverCheckListExecutor.execute(new VbkDriverCheckListRequestType())

        then:
        result != null
    }
}