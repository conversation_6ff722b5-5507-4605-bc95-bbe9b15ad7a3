package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.processor.PushSettlementProcessor
import com.ctrip.dcs.application.service.DriverCarConfirmedService
import com.ctrip.dcs.application.service.DriverConfirmedService
import com.ctrip.dcs.application.service.SpContractInfoService
import com.ctrip.dcs.domain.dsporder.gateway.DspDrvOrderLimitTakenRecordGateway
import org.junit.Assert
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification
import spock.lang.Unroll

class DriverAndCarConfirmListenerTest extends Specification {
    def testObj = new DriverAndCarConfirmListener()
    def driverCarConfirmedService = Mock(DriverCarConfirmedService)
    def spContractInfoService = Mock(SpContractInfoService)
    def driverConfirmedService = Mock(DriverConfirmedService)
    def dspDrvOrderLimitTakenRecordGateway = Mock(DspDrvOrderLimitTakenRecordGateway)
    def pushSettlementProcessor = Mock(PushSettlementProcessor)

    def setup() {

        testObj.driverCarConfirmedService = driverCarConfirmedService
        testObj.spContractInfoService = spContractInfoService
        testObj.driverConfirmedService = driverConfirmedService
        testObj.dspDrvOrderLimitTakenRecordGateway=dspDrvOrderLimitTakenRecordGateway
        testObj.pushSettlementProcessor = pushSettlementProcessor
    }


    @Unroll
    def "contractUpdateDrvLevelTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverConfirmedService.contractUpdateDrvLevel(_, _) >> Boolean.TRUE
        BaseMessage message = new BaseMessage()
        message.setProperty("driverId", 1L)
        message.setProperty("confirmRecordId", 1L)
        message.setSubject("1111")

        when:
        testObj.contractUpdateDrvLevel(message)

        then: "验证返回结果里属性值是否符合预期"
        Assert.assertTrue(message.getSubject().equals("1111"))

    }

    @Unroll
    def "contractUpdateDrvLevel1Test"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverConfirmedService.contractUpdateDrvLevel(_, _) >> Boolean.TRUE
        BaseMessage message = new BaseMessage()
        message.setProperty("driverId1", 1L)
        message.setProperty("confirmRecordId", 1L)
        message.setSubject("1111")

        when:
        testObj.contractUpdateDrvLevel(message)

        then: "验证返回结果里属性值是否符合预期"
        Assert.assertTrue(message.getSubject().equals("1111"))

    }

    @Unroll
    def "pushSettlementTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        BaseMessage message = new BaseMessage()
        message.setProperty("driverOrderId", 1)
        message.setProperty("dspOrderId", 1)
        message.setProperty("confirmRecordId", 1L)

        when:
        testObj.onPushSettlementMessage(message)

        then: "验证返回结果里属性值是否符合预期"
        Assert.assertTrue(message.getStringProperty("driverOrderId") == "1")
    }

}

