package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.service.IGrabOrderPushRuleService
import com.ctrip.dcs.self.dispatchorder.interfaces.DeleteGrabOrderPushRuleRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.DeleteGrabOrderPushRuleResponseType
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DeleteGrabOrderPushRuleExecutorTest extends Specification {

    private IGrabOrderPushRuleService grabOrderPushRuleService = Mock(IGrabOrderPushRuleService)

    DeleteGrabOrderPushRuleExecutor executor = new DeleteGrabOrderPushRuleExecutor(grabOrderPushRuleService: grabOrderPushRuleService)

    def "Execute"() {
        given:
        DeleteGrabOrderPushRuleRequestType request = new DeleteGrabOrderPushRuleRequestType()
        request.setRuleId(1L)
        request.setOperateUser("a")
        request.setSupplierId(2L)
        grabOrderPushRuleService.deleteById(1L, "a", 2L) >> del
        when:
        DeleteGrabOrderPushRuleResponseType response = executor.execute(request)
        then:
        response.getResponseResult().getReturnCode() == code
        where:
        del   || code
        true  || "200"
        false || "500"
    }
}
