package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.service.IGrabOrderPushRuleService
import com.ctrip.dcs.application.service.dto.GrabOrderPushRuleDTO
import com.ctrip.dcs.application.service.dto.QueryGrabOrderPushRuleParamDTO
import com.ctrip.dcs.application.service.dto.QueryGrabOrderPushRuleResultDTO
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleResponseType
import com.ctrip.igt.PaginatorDTO
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class QueryGrabOrderPushRuleExecutorTest extends Specification {

    private IGrabOrderPushRuleService grabOrderPushRuleService = Mock(IGrabOrderPushRuleService);

    QueryGrabOrderPushRuleExecutor executor = new QueryGrabOrderPushRuleExecutor(grabOrderPushRuleService: grabOrderPushRuleService)

    def "Execute"() {
        given:
        QueryGrabOrderPushRuleRequestType request = new QueryGrabOrderPushRuleRequestType()
        request.setSupplierId(1L)
        request.setCityId(1)
        request.setVehicleGroupId(117)
        request.setPaginator(new PaginatorDTO(1, 10))
        GrabOrderPushRuleDTO rule = new GrabOrderPushRuleDTO()
        rule.setId(1L)
        rule.setSupplierId(2L)
        rule.setCityId(1L)
        rule.setCityIds([1L])
        rule.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        rule.setSupplierName("supplierName")
        rule.setVehicleGroupIdList([118L])
        rule.setRuleType(1)
        rule.setImmediatePushTime(1)
        rule.setFixedPushTime(null)
        rule.setStartBookTime("08:00")
        rule.setEndBookTime("08:00")
        rule.setBookTime("08:00")
        rule.setOperateUser("")
        rule.setDeleteFlag(1)

        QueryGrabOrderPushRuleResultDTO resultDTO = new QueryGrabOrderPushRuleResultDTO()
        resultDTO.setRuleDTOList([rule])
        grabOrderPushRuleService.query(_) >> resultDTO
        when:
        QueryGrabOrderPushRuleResponseType response = executor.execute(request)
        then:
        with(response.getRules().getFirst()) {
            id == 1L
            supplierId == 2L
            cityId == 1L
            categoryCode == CategoryCodeEnum.FROM_AIRPORT.getType()
            supplierName == "supplierName"
            vehicleGroupIdList == [118L]
            ruleType == 1
            immediatePushTime == 1
            fixedPushTime == null
            startBookTime == "08:00"
            endBookTime == "08:00"
            bookTime == "08:00"
        }
    }
}
