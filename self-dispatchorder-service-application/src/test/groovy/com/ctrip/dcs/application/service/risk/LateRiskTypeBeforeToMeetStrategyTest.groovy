package com.ctrip.dcs.application.service.risk

import com.ctrip.dcs.domain.common.enums.LateRiskTypeEnum
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.dsporder.gateway.LateRiskRemindGateway
import com.ctrip.igt.framework.common.clogging.Logger
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2025/1/1 10:49
 */
class LateRiskTypeBeforeToMeetStrategyTest extends Specification {
    def testObj = new LateRiskTypeBeforeToMeetStrategy()
    def logger = Mock(Logger)
    def lateRiskRemindGateway = Mock(LateRiskRemindGateway)

    def setup() {
        testObj.lateRiskRemindGateway = lateRiskRemindGateway
        testObj.logger = logger
    }

    @Unroll
    def "test getType"() {
        given:
        when:
        def result = testObj.getType()
        then:
        Assert.assertTrue(Objects.equals(result, LateRiskTypeEnum.BEFORE_TO_MEET.getType()))
    }

    @Unroll
    def "test sendEmail"() {
        given:
        when:
        testObj.sendEmail(dspOrder, transportGroupVO)
        then:
        Assert.assertTrue(Objects.nonNull(transportGroupVO))
        where:
        transportGroupVO                      | dspOrder                                 || expectedResult
        new TransportGroupVO(informEmail: "") | new DspOrderVO(dspOrderId: "dspOrderId") || null
        new TransportGroupVO(informEmail: "informEmail") | new DspOrderVO(dspOrderId: "dspOrderId") || null
    }

    @Unroll
    def "test sendPcStationLetter"() {
        given:
        when:
        testObj.sendPcStationLetter(dspOrder, transportGroup)
        then:
        Assert.assertTrue(Objects.nonNull(dspOrder))
        where:
        dspOrder         | transportGroup         || expectedResult
        new DspOrderVO() | new TransportGroupVO() || null
    }

    @Unroll
    def "test sendMobileSupplierRemind"() {
        given:
        when:
        testObj.sendMobileSupplierRemind(dspOrder, transportGroup)
        then:
        Assert.assertTrue(Objects.nonNull(transportGroup))
        where:
        dspOrder         | transportGroup         || expectedResult
        new DspOrderVO() | new TransportGroupVO() || null
    }

    @Unroll
    def "test sendLateRiskIVR"() {
        given:
        when:
        testObj.sendLateRiskIVR(dspOrderVO, transportGroupDetail)
        then:
        Assert.assertTrue(Objects.nonNull(transportGroupDetail))
        where:
        dspOrderVO       | transportGroupDetail   || expectedResult
        new DspOrderVO() | new TransportGroupVO() || null
    }
}
