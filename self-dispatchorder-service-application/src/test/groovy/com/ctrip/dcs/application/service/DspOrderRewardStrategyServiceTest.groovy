package com.ctrip.dcs.application.service

import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotStatusEnum
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotTypeEnum
import com.ctrip.dcs.domain.common.enums.ScheduleType
import com.ctrip.dcs.domain.common.service.GrabDspOrderSnapshotRecordService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.entity.DspOrderRewardStrategyDO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.factory.DspOrderRewardStrategyFactory
import com.ctrip.dcs.domain.schedule.process.impl.GrabBroadcastProcess
import com.ctrip.dcs.domain.schedule.repository.DspOrderRewardStrategyRepository
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DspOrderRewardStrategyServiceTest extends Specification {

    private ScheduleRepository scheduleRepository = Mock(ScheduleRepository)

    private QueryDspOrderService queryDspOrderService = Mock(QueryDspOrderService)

    private ScheduleTaskRepository scheduleTaskRepository = Mock(ScheduleTaskRepository)

    private DspOrderRewardStrategyRepository dspOrderRewardStrategyRepository = Mock(DspOrderRewardStrategyRepository)

    private GrabDspOrderSnapshotRepository grabDspOrderSnapshotRepository = Mock(GrabDspOrderSnapshotRepository)

    private GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository = Mock(GrabDspOrderDriverIndexRepository)

    private GrabBroadcastProcess grabBroadcastProcessor = Mock(GrabBroadcastProcess)

    protected GrabDspOrderSnapshotRecordService grabDspOrderSnapshotRecordService = Mock(GrabDspOrderSnapshotRecordService)

    private DspOrderRewardStrategyFactory dspOrderRewardStrategyFactory = Mock(DspOrderRewardStrategyFactory)

    private MessageProviderService messageProviderService = Mock(MessageProviderService)

    DspOrderRewardStrategyService dspOrderRewardStrategyService = new DspOrderRewardStrategyService(
            scheduleRepository: scheduleRepository,
            queryDspOrderService: queryDspOrderService,
            scheduleTaskRepository: scheduleTaskRepository,
            dspOrderRewardStrategyRepository: dspOrderRewardStrategyRepository,
            grabDspOrderSnapshotRepository: grabDspOrderSnapshotRepository,
            grabDspOrderDriverIndexRepository: grabDspOrderDriverIndexRepository,
            grabBroadcastProcessor: grabBroadcastProcessor,
            grabDspOrderSnapshotRecordService: grabDspOrderSnapshotRecordService,
            messageProviderService: messageProviderService,
            dspOrderRewardStrategyFactory: dspOrderRewardStrategyFactory
    )

    def "test activeDspOrderRewardStrategy"() {
        given:
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO()
        snapshot.setDspOrderId("1")
        snapshot.setGrabPushTimeBj(DateUtil.addMinutes(new Date(), 10))
        snapshot.setGrabStatus(GrabDspOrderSnapshotStatusEnum.GRAB)
        GrabDspOrderDriverIndexDO index = new GrabDspOrderDriverIndexDO();
        DspOrderRewardStrategyDO strategy = new DspOrderRewardStrategyDO()
        strategy.setDspOrderId("1")
        strategy.setScheduleId(1L)
        strategy.setDspRewardTimeBj(new Date())
        DspOrderVO order = Mock(DspOrderVO)
        order.getDspOrderId() >> "1"
        ScheduleDO schedule = Mock(ScheduleDO)
        ScheduleTaskDO task = Mock(ScheduleTaskDO)
        SubSkuVO subSku = Mock(SubSkuVO)
        schedule.getDspOrderId() >> "1"
        schedule.getScheduleId() >> 1L
        schedule.isShutdown() >> false
        schedule.getType() >> ScheduleType.SYSTEM
        schedule.getRewardStrategyList() >> [new DspOrderRewardStrategyDO()]
        task.getSubSku() >> subSku
        subSku.getDspType() >> DspType.GRAB_BROADCAST
        subSku.getTakenType() >> TakenType.DEFAULT
        grabDspOrderSnapshotRepository.query("1") >> snapshot;
        queryDspOrderService.queryOrderDetail("1") >> order;
        scheduleRepository.query("1") >> [schedule];
        scheduleRepository.find(1L) >> schedule;
        scheduleTaskRepository.query(1L, "1") >> [task];
        grabDspOrderDriverIndexRepository.queryFomCache("1") >> [index];
        when:
        def result = dspOrderRewardStrategyService.activeDspOrderRewardStrategy(strategy)
        then:
        result == null
    }

    def "test updateRewardStrategy"() {
        given:
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO()
        snapshot.setDspOrderId("1")
        snapshot.setGrabPushTimeBj(DateUtil.addMinutes(new Date(), 10))
        snapshot.setGrabStatus(GrabDspOrderSnapshotStatusEnum.GRAB)
        GrabDspOrderDriverIndexDO index = new GrabDspOrderDriverIndexDO();
        DspOrderRewardStrategyDO strategy = new DspOrderRewardStrategyDO()
        strategy.setDspOrderId("1")
        strategy.setScheduleId(1L)
        strategy.setDspRewardStatus(1)
        DspOrderVO order = Mock(DspOrderVO)
        order.getDspOrderId() >> "1"
        ScheduleDO schedule = Mock(ScheduleDO)
        ScheduleTaskDO task = Mock(ScheduleTaskDO)
        SubSkuVO subSku = Mock(SubSkuVO)
        schedule.getDspOrderId() >> "1"
        schedule.getScheduleId() >> 1L
        schedule.isShutdown() >> false
        schedule.getType() >> ScheduleType.SYSTEM
        schedule.getRewardStrategyList() >> [new DspOrderRewardStrategyDO()]
        task.getSubSku() >> subSku
        subSku.getDspType() >> DspType.GRAB_BROADCAST
        subSku.getTakenType() >> TakenType.DEFAULT
        grabDspOrderSnapshotRepository.query("1") >> snapshot;
        queryDspOrderService.queryOrderDetail("1") >> order;
        scheduleRepository.query("1") >> [schedule];
        scheduleRepository.find(1L) >> schedule;
        scheduleTaskRepository.query(1L, "1") >> [task];
        grabDspOrderDriverIndexRepository.queryFomCache("1") >> [index];
        dspOrderRewardStrategyRepository.find(1L) >> strategy
        when:
        def result = dspOrderRewardStrategyService.updateRewardStrategy(1L)
        then:
        result == null
    }

    def "test updateScheduleTask"() {
        given:
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO()
        snapshot.setDspOrderId("1")
        snapshot.setGrabPushTimeBj(DateUtil.addMinutes(new Date(), 10))
        snapshot.setGrabStatus(GrabDspOrderSnapshotStatusEnum.GRAB)
        GrabDspOrderDriverIndexDO index = new GrabDspOrderDriverIndexDO();
        DspOrderRewardStrategyDO strategy = new DspOrderRewardStrategyDO()
        DspOrderVO order = Mock(DspOrderVO)
        ScheduleDO schedule = Mock(ScheduleDO)
        ScheduleTaskDO task = Mock(ScheduleTaskDO)
        SubSkuVO subSku = Mock(SubSkuVO)
        schedule.isShutdown() >> false
        schedule.getType() >> ScheduleType.SYSTEM
        schedule.getRewardStrategyList() >> [new DspOrderRewardStrategyDO()]
        task.getSubSku() >> subSku
        subSku.getDspType() >> DspType.GRAB_BROADCAST
        subSku.getTakenType() >> TakenType.DEFAULT
        grabDspOrderSnapshotRepository.query("1") >> snapshot;
        queryDspOrderService.queryOrderDetail("1") >> order;
        scheduleRepository.query("1") >> [schedule];
        when:
        def result = dspOrderRewardStrategyService.updateScheduleTask(order, task, strategy)
        then:
        result == null
    }

    def "test updateGrabDspOrderSnapshot"() {
        given:
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO()
        snapshot.setDspOrderId("1")
        snapshot.setGrabPushTimeBj(DateUtil.addMinutes(new Date(), 10))
        snapshot.setGrabStatus(GrabDspOrderSnapshotStatusEnum.GRAB)
        GrabDspOrderDriverIndexDO index = new GrabDspOrderDriverIndexDO();
        DspOrderVO order = Mock(DspOrderVO)
        ScheduleDO schedule = Mock(ScheduleDO)
        ScheduleTaskDO task = Mock(ScheduleTaskDO)
        SubSkuVO subSku = Mock(SubSkuVO)
        schedule.isShutdown() >> false
        schedule.getType() >> ScheduleType.SYSTEM
        schedule.getRewardStrategyList() >> [new DspOrderRewardStrategyDO()]
        task.getSubSku() >> subSku
        subSku.getDspType() >> DspType.GRAB_BROADCAST
        subSku.getTakenType() >> TakenType.DEFAULT
        grabDspOrderSnapshotRepository.query("1") >> snapshot;
        queryDspOrderService.queryOrderDetail("1") >> order;
        scheduleRepository.query("1") >> [schedule];
        when:
        def strategy = dspOrderRewardStrategyService.updateGrabDspOrderSnapshot(order, schedule, task, snapshot)
        then:
        strategy == null
    }

    def "test rebuildRewardStrategy"() {
        given:
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO()
        snapshot.setDspOrderId("1")
        snapshot.setGrabPushTimeBj(DateUtil.addMinutes(new Date(), 10))
        snapshot.setGrabStatus(GrabDspOrderSnapshotStatusEnum.GRAB)
        DspOrderVO order = Mock(DspOrderVO)
        ScheduleDO schedule = Mock(ScheduleDO)
        schedule.isShutdown() >> false
        schedule.getType() >> ScheduleType.SYSTEM
        schedule.getRewardStrategyList() >> [new DspOrderRewardStrategyDO()]
        grabDspOrderSnapshotRepository.query("1") >> snapshot;
        queryDspOrderService.queryOrderDetail("1") >> order;
        scheduleRepository.query("1") >> [schedule];
        when:
        def strategy = dspOrderRewardStrategyService.rebuildRewardStrategy("1")
        then:
        strategy == null
    }
}
