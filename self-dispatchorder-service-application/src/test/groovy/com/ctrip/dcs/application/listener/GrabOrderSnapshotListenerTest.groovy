package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.command.grabOrderSnapshot.CancelGrabOrderSnapshotExeCmd
import com.ctrip.dcs.application.command.grabOrderSnapshot.ConfirmGrabOrderSnapshotExeCmd
import com.ctrip.dcs.application.command.grabOrderSnapshot.CreateGrabOrderDriverIndexExeCmd
import com.ctrip.dcs.application.command.grabOrderSnapshot.UpdateGrabDriverIndexExeCmd
import com.ctrip.dcs.application.command.grabOrderSnapshot.UpdateGrabOrderSnapshotExeCmd
import com.ctrip.igt.framework.common.jackson.JacksonSerializer
import org.springframework.beans.factory.annotation.Autowired
import qunar.tc.qmq.Message
import qunar.tc.qmq.NeedRetryException
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class GrabOrderSnapshotListenerTest extends Specification {

    private CreateGrabOrderDriverIndexExeCmd createGrabOrderDriverIndexExeCmd;

    private ConfirmGrabOrderSnapshotExeCmd confirmGrabOrderSnapshotExeCmd;

    private CancelGrabOrderSnapshotExeCmd cancelGrabOrderSnapshotExeCmd;

    private UpdateGrabOrderSnapshotExeCmd changeGrabOrderSnapshotUseTimeExeCmd;

    private UpdateGrabDriverIndexExeCmd updateGrabDriverIndexExeCmd;

    GrabOrderSnapshotListener listener = new GrabOrderSnapshotListener(
            createGrabOrderDriverIndexExeCmd: createGrabOrderDriverIndexExeCmd,
            confirmGrabOrderSnapshotExeCmd: confirmGrabOrderSnapshotExeCmd,
            cancelGrabOrderSnapshotExeCmd: cancelGrabOrderSnapshotExeCmd,
            changeGrabOrderSnapshotUseTimeExeCmd: changeGrabOrderSnapshotUseTimeExeCmd,
            updateGrabDriverIndexExeCmd: updateGrabDriverIndexExeCmd
    )

    def "CreateGrabOrderDriverIndex"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("dspOrderId", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        when:
        listener.createGrabOrderDriverIndex(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }

    def "ConfirmDriverAndCar"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("dspOrderId", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        when:
        listener.confirmDriverAndCar(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }

    def "ConfirmDriver"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("dspOrderId", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        when:
        listener.confirmDriver(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }

    def "ConfirmDispatcher"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("dspOrderId", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        when:
        listener.confirmDispatcher(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }

    def "OrderTaken"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("supplyOrderIds", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        when:
        listener.orderTaken(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }

    def "Confirm"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("supplyOrderId", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        when:
        listener.confirm(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }

    def "Cancel"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("dspOrderId", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        when:
        listener.cancel(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }

    def "CancelOld"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("supplyOrderIds", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        when:
        listener.cancelOld(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }

    def "ChangeEstimatedUseTime"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("dspOrderId", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        when:
        listener.changeEstimatedUseTime(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }

    def "ChangeExpectBookTime"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("supplyOrderIds", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        when:
        listener.changeExpectBookTime(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }

    def "ChangeGrabRule"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("supplyOrderIds", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        message.setProperty("supplierId", 1L)
        when:
        listener.changeGrabRule(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }

    def "ChangeDriverPushConfig"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("supplyOrderIds", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        message.setProperty("driverId", 1L)
        message.setProperty("supplierId", 1L)
        when:
        listener.changeDriverPushConfig(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }

    def "ChangeDriverInfo"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("supplyOrderIds", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        message.setProperty("driverId", 1L)
        message.setProperty("supplierId", 1L)
        when:
        listener.changeDriverInfo(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }

    def "ChangeCarInfo"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("supplyOrderIds", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        message.setProperty("driverId", 1L)
        message.setProperty("supplierId", 1L)
        when:
        listener.changeCarInfo(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }

    def "ChangeDriverLeave"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("supplyOrderIds", "1")
        message.setProperty("driverIds", JacksonSerializer.INSTANCE().serialize([1L]))
        message.setProperty("driverId", 1L)
        message.setProperty("supplierId", 1L)
        when:
        listener.changeDriverLeave(message)
        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ChangeGrabOrderSnapshotListenerError"
    }
}
