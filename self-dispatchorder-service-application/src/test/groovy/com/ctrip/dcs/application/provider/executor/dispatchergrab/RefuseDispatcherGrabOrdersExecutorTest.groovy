package com.ctrip.dcs.application.provider.executor.dispatchergrab

import com.ctrip.dcs.application.command.dispatchergrab.RefuseDispatcherGrabOrderExeCmd
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDispatcherGrabOrdersResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.RefuseDispatcherGrabOrdersRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.RefuseDispatcherGrabOrdersResponseType
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class RefuseDispatcherGrabOrdersExecutorTest extends Specification {
    @Mock
    Logger logger
    @Mock
    RefuseDispatcherGrabOrderExeCmd cmd
    @Mock
    RefuseDispatcherGrabOrdersRequestType request
    @InjectMocks
    RefuseDispatcherGrabOrdersExecutor refuseDispatcherGrabOrdersExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(request.getDspOrderId()).thenReturn("1")
        when(request.getSupplierId()).thenReturn(0L)
        when:
        RefuseDispatcherGrabOrdersResponseType result = refuseDispatcherGrabOrdersExecutor.execute(request)

        then:
        result.getResponseResult().isSuccess()
    }

    def "test execute fail"() {
        given:
        when(request.getDspOrderId()).thenReturn(orderId)
        when(request.getSupplierId()).thenReturn(supplierId)
        when(cmd.execute(any())).thenThrow(error)
        when:
        RefuseDispatcherGrabOrdersResponseType result = refuseDispatcherGrabOrdersExecutor.execute(request)

        then:
        result.getResponseResult().getReturnCode() == code

        where:
        orderId | supplierId | error                                                    || code
        ""      | 0L         | ErrorCode.ERROR_PARAMS.getBizException()                 || ErrorCode.ERROR_PARAMS.getCode()
        "1"     | null       | ErrorCode.ERROR_PARAMS.getBizException()                 || ErrorCode.ERROR_PARAMS.getCode()
        "1"     | 1L         | ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getBizException() || ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getCode()
        "1"     | 1L         | new RuntimeException()                                   || "500"

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme