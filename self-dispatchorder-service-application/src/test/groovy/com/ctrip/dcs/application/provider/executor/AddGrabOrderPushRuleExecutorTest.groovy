package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.service.IGrabOrderPushRuleService
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.self.dispatchorder.interfaces.AddGrabOrderPushRuleRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.AddGrabOrderPushRuleResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.GrabOrderPushRuleSoaDTO
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class AddGrabOrderPushRuleExecutorTest extends Specification {

    IGrabOrderPushRuleService grabOrderPushRuleService = Mock(IGrabOrderPushRuleService)

    AddGrabOrderPushRuleExecutor executor = new AddGrabOrderPushRuleExecutor(grabOrderPushRuleService: grabOrderPushRuleService)

    def "Execute"() {
        given:
        GrabOrderPushRuleSoaDTO rule = new GrabOrderPushRuleSoaDTO()
        rule.setId(1L)
        rule.setSupplierId(2L)
        rule.setCityIds([1L])
        rule.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        rule.setSupplierName("supplierName")
        rule.setVehicleGroupIdList([117])
        rule.setRuleType(1)
        rule.setImmediatePushTime(1)
        rule.setFixedPushTime(null)
        rule.setStartBookTime("08:00")
        rule.setEndBookTime("12:00")
        rule.setBookTime("08:00")

        AddGrabOrderPushRuleRequestType request = new AddGrabOrderPushRuleRequestType(rule: rule, operateUser: "test")
        when:
        AddGrabOrderPushRuleResponseType response = executor.execute(request)
        then:
        response.getResponseResult().getReturnCode() == "500"
    }
}
