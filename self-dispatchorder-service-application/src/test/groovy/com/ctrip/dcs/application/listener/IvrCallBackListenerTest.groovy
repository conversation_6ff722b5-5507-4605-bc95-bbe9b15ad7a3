package com.ctrip.dcs.application.listener

import com.ctrip.dcs.domain.common.service.IvrCallService
import com.ctrip.dcs.domain.dsporder.entity.ivr.OutCallResultDataInfo
import com.ctrip.dcs.infrastructure.adapter.qconfig.IvrConfig
import com.ctrip.igt.framework.common.jackson.JacksonUtil
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class IvrCallBackListenerTest extends Specification {
    @Mock
    IvrConfig ivrConfig
    @Mock
    IvrCallService ivrCallService
    @InjectMocks
    IvrCallBackListener ivrCallBackListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }


    def "test ivr Out Msg Notice"() {
        given:
        when(ivrConfig.isIvrSkillGroupId(anyString(), anyString())).thenReturn(true)
        when(ivrCallService.ivrCallBackNotice4Igt(any())).thenReturn(true)
        when(ivrCallService.ivrUrgentOrderCallBackNotice4Igt(any())).thenReturn(true)

        when:
        Message message = new BaseMessage();
        OutCallResultDataInfo outCallResultDataInfo = new OutCallResultDataInfo();
        message.setProperty("OutCallResultData", JacksonUtil.serialize(outCallResultDataInfo));
        message.setProperty("OutCallOriginalID","1212");
        ivrCallBackListener.ivrOutMsgNotice(message)

        then:
        message != null//todo - validate something
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme