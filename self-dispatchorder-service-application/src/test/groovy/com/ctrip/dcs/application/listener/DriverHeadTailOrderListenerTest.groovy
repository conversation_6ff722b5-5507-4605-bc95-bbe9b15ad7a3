package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.command.UpdateDriverHeadTailOrderExeCmd
import com.ctrip.dcs.domain.common.constants.EventConstants
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.google.common.collect.Lists
import credis.java.client.CacheProvider
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*


/**
 *
 *
 * <AUTHOR>
 * @date 2024/12/9 18:54:38
 * @version 1.0
 */
class DriverHeadTailOrderListenerTest extends Specification {
    def testObj = new DriverHeadTailOrderListener()
    def queryDspOrderService = Mock(QueryDspOrderService)
    def queryDriverService = Mock(QueryDriverService)
    def updateDriverHeadTailOrderExeCmd = Mock(UpdateDriverHeadTailOrderExeCmd)
    def trocksCacheProvider = Mock(CacheProvider)

    def setup() {
        testObj.queryDspOrderService = queryDspOrderService
        testObj.queryDriverService = queryDriverService
        testObj.updateDriverHeadTailOrderExeCmd = updateDriverHeadTailOrderExeCmd
        testObj.trocksCacheProvider = trocksCacheProvider
    }

    @Unroll
    def "onNewDriverConfirmTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        def message = new BaseMessage("id", EventConstants.DSP_ORDER_DRIVER_CONFIRM_TOPIC)
        message.setProperty("dspOrderId", "123")
        message.setProperty("driverId", 1L)

        and: "Mock相关接口返回"

        and: "Spy相关接口"

        when:
        def result = spy.onNewDriverConfirm(message)

        then: "验证返回结果里属性值是否符合预期"
        1 * updateDriverHeadTailOrderExeCmd.execute(_)

        where: "表格方式验证多种分支调用场景"

    }


    @Unroll
    def "onOldDriverConfirmTest"() {
        given: "设定相关方法入参"
        def message = new BaseMessage("id", EventConstants.CAR_QBEST_ORDER_ORDERSTATE_ORDER_DISPATCHER_CONFIRM)
        message.setProperty("supplyOrderId", "123")
        message.setProperty("driverId", 1L)
        message.setProperty("msg", confirmType)

        when:
        def result = testObj.onOldDriverConfirm(message)

        then: "验证返回结果里属性值是否符合预期"
        expected * updateDriverHeadTailOrderExeCmd.execute(_)

        where: "表格方式验证多种分支调用场景"
        confirmType                  || expected
        "service_provider_confirmed" || 0
        "driver_confirmed"           || 1
    }


    @Unroll
    def "onNewDriverCarConfirmTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        def message = new BaseMessage("id", EventConstants.DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC)
        message.setProperty("dspOrderId", "123")
        message.setProperty("driverId", 1L)

        and: "Spy相关接口"

        when:
        def result = spy.onNewDriverCarConfirm(message)

        then: "验证返回结果里属性值是否符合预期"
        1 * updateDriverHeadTailOrderExeCmd.execute(_)

        where: "表格方式验证多种分支调用场景"

    }


    @Unroll
    def "onOldDriverCarConfirmTest"() {
        given: "设定相关方法入参"
        def message = new BaseMessage("id", EventConstants.CAR_QBEST_ORDER_ORDERSTATE_ORDER_TAKEN)
        message.setProperty("supplyOrderId", "123")
        message.setProperty("driverId", 1L)

        when:
        def result = testObj.onOldDriverCarConfirm(message)
        then: "验证返回结果里属性值是否符合预期"
        1 * updateDriverHeadTailOrderExeCmd.execute(_)

        where: "表格方式验证多种分支调用场景"

    }


    @Unroll
    def "onNewDriverOrderCancelTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        def message = new BaseMessage("id", EventConstants.DRIVER_ORDER_CANCEL_TOPIC)
        message.setProperty("dspOrderId", "123")
        message.setProperty("driverId", 1L)

        and: "Spy相关接口"

        when:
        def result = spy.onNewDriverOrderCancel(message)

        then: "验证返回结果里属性值是否符合预期"
        1 * updateDriverHeadTailOrderExeCmd.execute(_)

        where: "表格方式验证多种分支调用场景"
    }


    @Unroll
    def "onOldDriverOrderCancelTest"() {
        given: "设定相关方法入参"
        def message = new BaseMessage("id", EventConstants.OLD_DSP_ORDER_CANCEL_TOPIC)
        message.setProperty("supplyOrderIds", dspOrderIds)

        and: "Mock相关接口返回"
        queryDspOrderService.queryOrderDetail(_) >> dspOrder

        when:
        def result = testObj.onOldDriverOrderCancel(message)

        then: "验证返回结果里属性值是否符合预期"
        expected * updateDriverHeadTailOrderExeCmd.execute(_)

        where: "表格方式验证多种分支调用场景"
        dspOrderIds                   | dspOrder                                                    || expected
        "dspOrderId123"               | null                                                        || 0
        "dspOrderId123"               | new DspOrderVO(dspOrderId: "dspOrderId123", driverId: null) || 0
        "dspOrderId123"               | new DspOrderVO(dspOrderId: "dspOrderId123", driverId: 1L)   || 1
        "dspOrderId123,dspOrderId456" | new DspOrderVO(dspOrderId: "dspOrderId123", driverId: 1L)   || 2
    }


    @Unroll
    def "onNewOrderEstimateTimeChangeTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        def message = new BaseMessage("id", EventConstants.DRIVER_ORDER_ESTIMATE_TIME_CHANGE)
        message.setProperty("dspOrderId", "123")
        message.setProperty("driverId", 1L)

        and: "Spy相关接口"

        when:
        def result = spy.onNewOrderEstimateTimeChange(message)

        then: "验证返回结果里属性值是否符合预期"
        1 * updateDriverHeadTailOrderExeCmd.execute(_)

        where: "表格方式验证多种分支调用场景"
    }


    @Unroll
    def "onOldOrderEstimateTimeChangeTest"() {
        given: "设定相关方法入参"
        def message = new BaseMessage("id", EventConstants.QMQ_ORDER_SYS_EXPECT_BOOK_TIME_CHANGE)
        message.setProperty("supplyOrderIds", dspOrderIds)

        and: "Mock相关接口返回"
        queryDspOrderService.queryOrderDetail(_) >> dspOrder

        when:
        def result = testObj.onOldOrderEstimateTimeChange(message)

        then: "验证返回结果里属性值是否符合预期"
        expected * updateDriverHeadTailOrderExeCmd.execute(_)

        where: "表格方式验证多种分支调用场景"
        dspOrderIds                   | dspOrder                                                    || expected
        "dspOrderId123"               | null                                                        || 0
        "dspOrderId123"               | new DspOrderVO(dspOrderId: "dspOrderId123", driverId: null) || 0
        "dspOrderId123"               | new DspOrderVO(dspOrderId: "dspOrderId123", driverId: 1L)   || 1
        "dspOrderId123,dspOrderId456" | new DspOrderVO(dspOrderId: "dspOrderId123", driverId: 1L)   || 2
    }


    @Unroll
    def "onDriverChangeTest"() {
        given: "设定相关方法入参"
        def message = new BaseMessage("id", EventConstants.DRIVER_MODIFY_TOPIC)
        message.setProperty("drvId", 1L)
        message.setProperty("supplierId", 10L)

        and: "Mock相关接口返回"
        queryDriverService.queryDriver(_, _, _) >> new DriverVO(driverId: 1L, workTimes: new PeriodsVO(driverNewWorkTime))
        trocksCacheProvider.get(_) >> workTimeCacheValue

        when:
        def result = testObj.onDriverChange(message)

        then: "验证返回结果里属性值是否符合预期"
        expectedCount * updateDriverHeadTailOrderExeCmd.delDriverHeadTailOrder(_, _)

        where: "表格方式验证多种分支调用场景"
        workTimeCacheValue                                    || driverNewWorkTime                             || expectedCount
        ""                                                    || null                                          || 0
        null                                                  || null                                          || 0
        "NULL"                                                || null                                          || 0
        "[\"04:00~09:59\", \"17:00~23:59\", \"00:00~00:59\"]" || null                                          || 30
        ""                                                    || ["04:00~09:59", "17:00~23:59", "00:00~00:59"] || 30
        "[\"04:00~09:59\", \"17:00~23:59\", \"00:00~00:59\"]" || ["04:00~09:59", "17:00~23:59", "00:00~00:59"] || 0
        "[\"04:00~08:59\", \"17:00~23:59\", \"00:00~00:59\"]" || ["04:00~09:59", "17:00~23:59", "00:00~00:59"] || 30

    }


}

