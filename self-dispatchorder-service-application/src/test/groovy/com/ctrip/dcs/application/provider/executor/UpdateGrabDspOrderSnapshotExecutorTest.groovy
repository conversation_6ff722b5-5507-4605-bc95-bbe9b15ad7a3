package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.GrabDspOrderSnapshotDao
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabDspOrderSnapshotPO
import com.ctrip.dcs.self.dispatchorder.interfaces.GrabDspOrderSnapshotDTO
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabDspOrderSnapshotRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabDspOrderSnapshotResponseType
import com.ctrip.igt.CommonAllianceDTO
import com.ctrip.igt.CommonGPSDTO
import com.ctrip.igt.CommonMiniProgramDTO
import com.ctrip.igt.CommonUBTDTO
import com.ctrip.igt.RequestHeader
import com.ctrip.igt.ResponseResult
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.igt.framework.infrastructure.exception.ServiceValidationException
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import com.ctriposs.baiji.rpc.mobile.common.types.ExtensionFieldType
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class UpdateGrabDspOrderSnapshotExecutorTest extends Specification {
    @Mock
    GrabDspOrderSnapshotDao grabDspOrderSnapshotDao
    @InjectMocks
    UpdateGrabDspOrderSnapshotExecutor updateGrabDspOrderSnapshotExecutor

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    def "test execute"() {
        given:
        when(grabDspOrderSnapshotDao.query(anyString())).thenReturn(new GrabDspOrderSnapshotPO(grabStatus: 0))
        UpdateGrabDspOrderSnapshotRequestType requestType = new UpdateGrabDspOrderSnapshotRequestType(snapshots: [new GrabDspOrderSnapshotDTO(dspOrderId: 1, grabStatus: 1)])

        when:
        UpdateGrabDspOrderSnapshotResponseType result = updateGrabDspOrderSnapshotExecutor.execute(requestType)

        then:
        result.getResponseResult().getReturnCode() == "200"
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme