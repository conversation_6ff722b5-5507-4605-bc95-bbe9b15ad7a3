package com.ctrip.dcs.application.scheduler

import com.ctrip.dcs.application.command.CreateScheduleExeCmd
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderDao
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.ScheduleDao
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderPO
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.SchedulePO
import qunar.tc.schedule.MockParameter
import qunar.tc.schedule.Parameter
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class RecreateScheduleJobTest extends Specification {

    DspOrderDao dspOrderDao = Mock(DspOrderDao)

    ScheduleDao scheduleDao = Mock(ScheduleDao)

    CreateScheduleExeCmd createScheduleExeCmd = Mock(CreateScheduleExeCmd)

    RecreateScheduleJob job = new RecreateScheduleJob(dspOrderDao: dspOrderDao, scheduleDao: scheduleDao, createScheduleExeCmd: createScheduleExeCmd)

    def "Execute"() {
        given:
        Parameter parameter = new MockParameter()
        when:
        def result = job.execute(parameter)
        then:
        result == null
    }

    def "Execute 1"() {
        given:
        dspOrderDao.queryToBeConfirmed(_ as Date, _ as Date) >> [new DspOrderPO(dspOrderId: "1"), new DspOrderPO(dspOrderId: "2")]
        scheduleDao.queryByDspOrderIds(_ as Set) >> [new SchedulePO(dspOrderId: "1", scheduleId: 2L, scheduleStatus: 1), new SchedulePO(dspOrderId: "1", scheduleId: 3L, scheduleStatus: 0)]
        when:
        int result = job.execute(new Date(), new Date())
        then:
        result == 1
    }
}
