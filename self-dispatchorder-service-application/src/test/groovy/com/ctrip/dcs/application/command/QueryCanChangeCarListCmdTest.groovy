package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.QuerySupplierCanChangeCarListCommand
import com.ctrip.dcs.application.command.dto.CarInfo4VbkDTO
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.ProductTypeEnum
import com.ctrip.dcs.domain.common.service.QueryVehicleService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.VehicleVO
import com.ctrip.dcs.domain.dsporder.value.SeriesVO
import com.ctrip.dcs.domain.schedule.gateway.CarSeriesGateway
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import spock.lang.Specification
import spock.lang.Unroll

class QueryCanChangeCarListCmdTest extends Specification {
    def testObj = new QueryCanChangeCarListCmd()
    def selfOrderQueryGateway = Mock(SelfOrderQueryGateway)
    def carSeriesGateway = Mock(CarSeriesGateway)
    def queryVehicleService = Mock(QueryVehicleService)

    def setup() {

        testObj.selfOrderQueryGateway = selfOrderQueryGateway
        testObj.queryVehicleService = queryVehicleService
        testObj.carSeriesGateway = carSeriesGateway
    }

    @Unroll
    def "querySupplierCanChangeCarListTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        selfOrderQueryGateway.queryDspOrder(_) >> new DspOrderVO(categoryCode: categotyCode, carTypeId: 0, supplierId: supplierId, productType: ProductTypeEnum.APPOINT_CAR_TYPE.getCode())

        carSeriesGateway.query(_, _) >> new SeriesVO(Set.of(1L))
        queryVehicleService.queryUnBindDrvVehicleList(_, _, _, _) >> res

        when:
        def result = testObj.querySupplierCanChangeCarList(new QuerySupplierCanChangeCarListCommand("dsp", 6206L, 1))

        then: "验证返回结果里属性值是否符合预期"
        result.size() == expectedResult
        where: "表格方式验证多种分支调用场景"
        categotyCode                  | supplierId | res       || expectedResult
        CategoryCodeEnum.DAY_RENTAL   | 6206       | getRes()     || 1
        CategoryCodeEnum.C_DAY_RENTAL | 6206       | getRes()     || 1
        CategoryCodeEnum.FROM_AIRPORT | 6206       | getRes()    || 1
        CategoryCodeEnum.DAY_RENTAL   | 30804      | getRes()     || 0
        CategoryCodeEnum.DAY_RENTAL   | 6206       | []           || 0
    }

    @Unroll
    def "querySupplierCanChangeCarListTestNUll"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        selfOrderQueryGateway.queryDspOrder(_) >>null

        carSeriesGateway.query(_, _) >> new SeriesVO(Set.of(1L))
        queryVehicleService.queryUnBindDrvVehicleList(_, _, _, _) >> res

        when:
        def result = testObj.querySupplierCanChangeCarList(new QuerySupplierCanChangeCarListCommand("dsp", 6206L, 1))

        then: "验证返回结果里属性值是否符合预期"
        result.size() == expectedResult
        where: "表格方式验证多种分支调用场景"
        categotyCode                  | supplierId | res       || expectedResult
        CategoryCodeEnum.DAY_RENTAL   | 6206       | getRes()     || 0
    }


    def getRes() {
        return [new VehicleVO(carId: 1L, carLicense: "carLicense", carColorId: 1L, carColor: "carColor", carBrandId: 1L, carBrandName: "carBrandName", carTypeId: 1L, carTypeName: "carTypeName", carSeriesId: 1L, carSeriesName: "carSeriesName", temporaryDispatchMark: 0)]
    }
}
