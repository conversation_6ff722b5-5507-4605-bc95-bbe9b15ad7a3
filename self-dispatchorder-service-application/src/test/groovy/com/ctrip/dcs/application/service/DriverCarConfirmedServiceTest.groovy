package com.ctrip.dcs.application.service

import com.ctrip.dcs.application.command.CalculateDriverMileageProfitExeCmd
import com.ctrip.dcs.application.command.ShutdownScheduleExeCmd
import com.ctrip.dcs.application.command.dispatchergrab.CancelDispatcherGrabOrderExeCmd
import com.ctrip.dcs.domain.common.enums.ScheduleEventType
import com.ctrip.dcs.domain.common.service.IvrCallService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import com.ctrip.dcs.infrastructure.adapter.trocks.TRocksProviderAdapter
import com.ctrip.dcs.infrastructure.factory.VBKOperationRecordFactory
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyLong
import static org.mockito.Mockito.when

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR> ZhangZhen
 * @create 2024/7/17 15:17
 */
class DriverCarConfirmedServiceTest extends Specification {

    def queryDspOrderService = Mock(QueryDspOrderService)

    def queryDriverService = Mock(QueryDriverService)

    def dspOrderConfirmRecordRepository = Mock(DspOrderConfirmRecordRepository)

    def workBenchLogMessageFactory = Mock(WorkBenchLogMessageFactory)

    def workBenchLogGateway = Mock(WorkBenchLogGateway)

    def vbkOperationRecordFactory = Mock(VBKOperationRecordFactory)

    def vbkOperationRecordGateway = Mock(VBKOperationRecordGateway)

    def shutdownScheduleExeCmd = Mock(ShutdownScheduleExeCmd)

    def scheduleRepository = Mock(ScheduleRepository)

    def ivrCallService = Mock(IvrCallService)

    def cancelDispatcherGrabOrderExeCmd = Mock(CancelDispatcherGrabOrderExeCmd)

    def grabCentreRepository = Mock(GrabCentreRepository)

    def grabOrderDetailRepository = Mock(GrabOrderDetailRepository)

    def calculateDriverMileageProfitExeCmd = Mock(CalculateDriverMileageProfitExeCmd)

    def tRocksProviderAdapter = Mock(TRocksProviderAdapter)

    def messageProviderService = Mock(MessageProviderService)

    def messageProducer = Mock(MessageProviderService)


    def driverCarConfirmedService = new DriverCarConfirmedService(
            queryDspOrderService: queryDspOrderService,
            queryDriverService: queryDriverService,
            dspOrderConfirmRecordRepository: dspOrderConfirmRecordRepository,
            workBenchLogMessageFactory: workBenchLogMessageFactory,
            workBenchLogGateway: workBenchLogGateway,
            vbkOperationRecordFactory: vbkOperationRecordFactory,
            vbkOperationRecordGateway: vbkOperationRecordGateway,
            shutdownScheduleExeCmd: shutdownScheduleExeCmd,
            scheduleRepository: scheduleRepository,
            ivrCallService: ivrCallService,
            cancelDispatcherGrabOrderExeCmd: cancelDispatcherGrabOrderExeCmd,
            grabCentreRepository: grabCentreRepository,
            grabOrderDetailRepository: grabOrderDetailRepository,
            calculateDriverMileageProfitExeCmd: calculateDriverMileageProfitExeCmd,
            tRocksProviderAdapter: tRocksProviderAdapter,
            messageProviderService: messageProviderService,
            messageProducer: messageProducer
    )

    def "test confirmed"() {
        given:

        def dspOrderVO = Mock(DspOrderVO)
        queryDspOrderService.query(_) >> dspOrderVO
        dspOrderVO.getUserOrderId() >> "111"
        def dspOrderConfirmRecordVO = Mock(DspOrderConfirmRecordVO)
        dspOrderConfirmRecordRepository.find(_) >> dspOrderConfirmRecordVO
        when:

        driverCarConfirmedService.confirmed("111", 1)

        then:

        1 * messageProducer.send(_)
    }

    def "test confirmed 1"() {
        given:

        def dspOrderVO = Mock(DspOrderVO)
        queryDspOrderService.query(_) >> dspOrderVO
        dspOrderVO.getUserOrderId() >> "111"
        def dspOrderConfirmRecordVO = null
        dspOrderConfirmRecordRepository.find(_) >> dspOrderConfirmRecordVO
        when:

        driverCarConfirmedService.confirmed("111", 1)

        then:

        1 * messageProducer.send(_)
    }

}