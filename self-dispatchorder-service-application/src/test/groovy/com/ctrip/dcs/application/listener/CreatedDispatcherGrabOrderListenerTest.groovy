package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.command.dispatchergrab.RemindDispatcherGrabOrderExeCmd
import com.ctrip.dcs.domain.schedule.event.CreateDispatcherGrabOrderEvent
import com.ctrip.dcs.infrastructure.adapter.qmq.MapMessageEventDecorator
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class CreatedDispatcherGrabOrderListenerTest extends Specification {

    RemindDispatcherGrabOrderExeCmd cmd = Mock(RemindDispatcherGrabOrderExeCmd)

    CreatedDispatcherGrabOrderListener listener = new CreatedDispatcherGrabOrderListener(cmd: cmd)

    def "OnMessage"() {
        given:
        Message message = new BaseMessage()
        Map<String, String> data = new MapMessageEventDecorator(new CreateDispatcherGrabOrderEvent([1L, 2L])).toMap()
        data.forEach { k, v -> message.setProperty(k, v) }
        when:
        def result = listener.onMessage(message)
        then:
        result == null
    }
}
