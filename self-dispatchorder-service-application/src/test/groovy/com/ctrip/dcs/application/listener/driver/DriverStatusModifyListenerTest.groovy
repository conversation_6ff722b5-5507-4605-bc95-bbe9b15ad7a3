package com.ctrip.dcs.application.listener.driver

import com.ctrip.dcs.application.event.IDriverFreezeEventHandler
import com.ctrip.dcs.application.event.IDriverOfflineEventHandler
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.igt.framework.common.jackson.JacksonSerializer
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverStatusModifyListenerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    IDriverOfflineEventHandler driverOfflineEventHandler
    @Mock
    IDriverFreezeEventHandler driverFreezeEventHandler

    @InjectMocks
    DriverStatusModifyListener driverStatusModifyListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test handle"() {
        given:
        Message message = new BaseMessage()

        when:
        message.addTag("tag_driver_supplier_change")
        driverStatusModifyListener.handle(message)

        message.addTag("tag_driverstate_offline")
        driverStatusModifyListener.handle(message)

        then:
        message.getTags().size() == 2
    }

    def "test handle Driver Freeze"() {
        given:
        Message message = new BaseMessage()

        when:
        driverStatusModifyListener.handleDriverFreeze(message)

        message.setProperty("drvId", "0")
        driverStatusModifyListener.handleDriverFreeze(message)

        message.setProperty("drvId", "1")
        driverStatusModifyListener.handleDriverFreeze(message)

        message.setProperty("drvId", "1")
        message.setProperty("chgAllOrder", "1")
        message.setProperty("isToSendType", "1")
        driverStatusModifyListener.handleDriverFreeze(message)

        message.setProperty("drvId", "1")
        message.setProperty("chgAllOrder", "2")
        message.setProperty("isToSendType", "1")
        driverStatusModifyListener.handleDriverFreeze(message)

        then:
        message.getTags().size() == 0
    }

    def "test handle Driver Offline"() {
        given:
        Message message = new BaseMessage()

        when:
        driverStatusModifyListener.handleDriverOffline(message)

        message.setProperty("drvId", "0")
        driverStatusModifyListener.handleDriverOffline(message)


        message.setProperty("drvId", "1")
        driverStatusModifyListener.handleDriverOffline(message)

        then:
        message.getTags().size() == 0
    }

    def "test handleTourDriverPlatformQmq driver0"() {
        given:
        Message message = new BaseMessage()
        message.addTag(tag)
        message.setProperty("driverId", driverId)
        message.setProperty("supplierId", supplierId)
        message.setProperty("operateFrom", "2")
        message.setProperty("driverProductLineList", driverProductLineList)
        when(driverFreezeEventHandler.handle(any())).thenThrow(new BizException("tag_driver_freeze"))

        when:
        def result = ""
        try {
            driverStatusModifyListener.handleTourDriverPlatformQmq(message)
        } catch (Exception e) {
            result = e.getMessage()
        }

        then:
        result == expected

        where:
        tag                 || driverId || supplierId || driverProductLineList || expected
        "tag_driver_freeze" || 0L       || 1L         || ""                    || ""
    }

    def "test handleTourDriverPlatformQmq supplier0"() {
        given:
        Message message = new BaseMessage()
        message.addTag(tag)
        message.setProperty("driverId", driverId)
        message.setProperty("supplierId", supplierId)
        message.setProperty("operateFrom", "2")
        message.setProperty("driverProductLineList", driverProductLineList)
        when(driverFreezeEventHandler.handle(any())).thenThrow(new BizException("tag_driver_freeze"))

        when:
        def result = ""
        try {
            driverStatusModifyListener.handleTourDriverPlatformQmq(message)
        } catch (Exception e) {
            result = e.getMessage()
        }

        then:
        result == expected

        where:
        tag                 || driverId || supplierId || driverProductLineList || expected
        "tag_driver_freeze" || 1L       || 0L         || ""                    || ""
    }

    def "test handleTourDriverPlatformQmq driverProductLineList"() {
        given:
        Message message = new BaseMessage()
        message.addTag(tag)
        message.setProperty("driverId", driverId)
        message.setProperty("supplierId", supplierId)
        message.setProperty("operateFrom", "2")
        message.setProperty("driverProductLineList", driverProductLineList)
        when(driverFreezeEventHandler.handle(any())).thenThrow(new BizException("tag_driver_freeze"))

        when:
        def result = ""
        try {
            driverStatusModifyListener.handleTourDriverPlatformQmq(message)
        } catch (Exception e) {
            result = e.getMessage()
        }

        then:
        result == expected

        where:
        tag                 || driverId || supplierId || driverProductLineList || expected
        "tag_driver_freeze" || 1L       || 1L         || ""                    || ""
        "tag_driver_freeze" || 1L       || 1L         || ","                   || ""
        "tag_driver_freeze" || 1L       || 1L         || "G,P,C"               || ""
        "tag_driver_freeze" || 1L       || 1L         || "G"                   || ""
    }

    def "test handleTourDriverPlatformQmq tag_driver_supplier_change ok"() {
        given:
        Message message = new BaseMessage()
        message.addTag(tag)
        message.setProperty("driverId", driverId)
        message.setProperty("supplierId", supplierId)
        message.setProperty("operateFrom", "2")
        message.setProperty("startFreezeTime", startFreezeTime)
        message.setProperty("endFreezeTime", endFreezeTime)
        message.setProperty("driverProductLineList", driverProductLineList)
        when(driverFreezeEventHandler.handle(any())).thenThrow(new BizException("tag_driver_freeze"))
        when(driverOfflineEventHandler.handle(any())).thenThrow(new BizException("tag_driver_offline"))

        when:
        def result = ""
        try {
            driverStatusModifyListener.handleTourDriverPlatformQmq(message)
        } catch (Exception e) {
            result = e.getMessage()
        }

        then:
        result == expected

        where:
        tag                  || driverId || supplierId || startFreezeTime       || endFreezeTime         || driverProductLineList || expected
        "tag_driver_freeze"  || 1L       || 10L        || ""                    || ""                    || "V,G"                 || ""
        "tag_driver_freeze"  || 1L       || 10L        || "2024-01-01 00:00:00" || "2024-01-03 00:00:00" || "V,G"                 || "tag_driver_freeze"
        "tag_driver_offline" || 1L       || 10L        || ""                    || ""                    || "V,G"                 || "tag_driver_offline"
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme