package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.command.ShortDistanceStrategyExeCmd
import com.ctrip.dcs.application.command.api.SaveShortDistanceStrategyCommand
import com.ctrip.dcs.application.command.dto.SaveShortDistanceStrategyResDTO
import com.ctrip.dcs.application.provider.converter.ShortDistanceStrategyConverter
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.dsporder.entity.ShortDistanceStrategyDO
import com.ctrip.dcs.self.dispatchorder.interfaces.SaveShortDistanceStrategyRequestType
import com.ctrip.igt.framework.common.result.Result
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import spock.lang.Specification

class SaveShortDistanceStrategyExecutorTest extends Specification {
    def strategyExeCmd = Mock(ShortDistanceStrategyExeCmd)
    def shortDistanceStrategyConverter = Mock(ShortDistanceStrategyConverter)
    def executor = new SaveShortDistanceStrategyExecutor(
            strategyExeCmd: strategyExeCmd,
            shortDistanceStrategyConverter: shortDistanceStrategyConverter
    )

    def "test execute"() {
        given:
        shortDistanceStrategyConverter.convert(_ as SaveShortDistanceStrategyRequestType) >> new SaveShortDistanceStrategyCommand(new ShortDistanceStrategyDO())
        strategyExeCmd.save(_ as SaveShortDistanceStrategyCommand) >> result

        when:
        def result2 = executor.execute(new SaveShortDistanceStrategyRequestType())

        then:
        result2.responseResult.returnCode == returnCode
        where:
        returnCode | result
        "200"      | Result.Builder.<SaveShortDistanceStrategyResDTO> newResult().success().build()
        "09020002" | Result.Builder.<SaveShortDistanceStrategyResDTO> newResult().fail().withCode(ErrorCode.SAVE_SHORT_DISTANCE_STRATEGY_CONFLICT_ERROR.getCode())
                .withMsg(ErrorCode.SAVE_SHORT_DISTANCE_STRATEGY_CONFLICT_ERROR.getDesc()).withData(new SaveShortDistanceStrategyResDTO(conflictingStrategyId: 1)).build()

    }

    def "test execute exp"() {
        given:
        shortDistanceStrategyConverter.convert(_ as SaveShortDistanceStrategyRequestType) >> new SaveShortDistanceStrategyCommand(new ShortDistanceStrategyDO())
        strategyExeCmd.save(_ as SaveShortDistanceStrategyCommand) >> { throw new Exception("error") }

        when:
        def result = executor.execute(new SaveShortDistanceStrategyRequestType())

        then:
        result.responseResult.returnCode == "500"
    }

    def "test execute valid"() {
        given:
        AbstractValidator validator = new AbstractValidator(SaveShortDistanceStrategyRequestType.class)

        when:
        def responseType = executor.validate(validator)

        then:
        responseType == null
    }
}
