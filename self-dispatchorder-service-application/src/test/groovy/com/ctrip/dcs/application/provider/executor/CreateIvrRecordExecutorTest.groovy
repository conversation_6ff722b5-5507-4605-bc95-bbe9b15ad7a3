package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.command.CreateIvrRecordExeCmd
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateIvrRecordRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateIvrRecordResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.IvrRecordInfo
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class CreateIvrRecordExecutorTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CreateIvrRecordExeCmd createIvrRecordExeCmd
    @InjectMocks
    CreateIvrRecordExecutor createIvrRecordExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(createIvrRecordExeCmd.execute(any())).thenReturn(0l)

        when:
        CreateIvrRecordRequestType requestType = new CreateIvrRecordRequestType();
        IvrRecordInfo ivrRecordInfo = new IvrRecordInfo();
        ivrRecordInfo.setUserOrderId("11");
        ivrRecordInfo.setSupplyOrderId("888");
        ivrRecordInfo.setRecordGuid("123r23")
        requestType.setData(ivrRecordInfo);
        CreateIvrRecordResponseType result = createIvrRecordExecutor.execute(requestType)

        then:
        result != null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme