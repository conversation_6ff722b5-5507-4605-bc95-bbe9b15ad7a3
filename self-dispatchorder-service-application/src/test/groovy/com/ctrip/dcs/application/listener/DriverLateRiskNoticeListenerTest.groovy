package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.service.risk.DriverLateRiskService
import com.ctrip.igt.framework.common.clogging.Logger
import org.junit.Assert
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/12/27 14:02
 */
class DriverLateRiskNoticeListenerTest extends Specification {
    def testObj = new DriverLateRiskNoticeListener()
    def logger = Mock(Logger)
    def driverLateRiskService = Mock(DriverLateRiskService)

    def setup() {
        testObj.logger = logger
        testObj.driverLateRiskService = driverLateRiskService
    }

    @Unroll
    def "test onMessage"() {
        given:
        BaseMessage message = new BaseMessage();
        message.setMessageId("312312313");
        message.setSubject("32131314");

        when:
        testObj.onMessage(message)

        then:
        Assert.assertTrue(Objects.nonNull(message))
    }

    @Unroll
    def "test oldOrderOnMessage"() {
        given:
        BaseMessage message = new BaseMessage();
        message.setMessageId("312312313");
        message.setSubject("32131314");

        when:
        testObj.oldOrderOnMessage(message)

        then:
        Assert.assertTrue(Objects.nonNull(message))
    }
}
