package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.query.CheckDelayedDspExeQry
import com.ctrip.dcs.application.query.api.CheckOrderDelayedDspDTO
import com.ctrip.dcs.application.service.dto.DelayedDspCheckResultDTO
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.HighLevelCheckService
import com.ctrip.dcs.domain.dsporder.value.ConfirmType
import com.ctrip.dcs.self.dispatchorder.interfaces.CheckVBKOrderDelayedDispatchRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.PoiDTO
import com.ctrip.igt.ResponseResult
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.igt.framework.infrastructure.exception.ServiceValidationException
import spock.lang.Specification
import spock.lang.Unroll

/**
 * 单元测试
 *
 * <AUTHOR>
 * @date 2024/3/9 23:29:59
 * @version 1.0
 */
class CheckVBKOrderDelayedDispatchExecutorTest extends Specification {
    def testObj = new CheckVBKOrderDelayedDispatchExecutor()
    def checkDelayedDspExeQry = Mock(CheckDelayedDspExeQry)
    def highLevelCheckService = Mock(HighLevelCheckService)

    def setup() {

        testObj.checkDelayedDspExeQry = checkDelayedDspExeQry
        testObj.highLevelCheckService = highLevelCheckService
    }

    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        checkDelayedDspExeQry.checkOrderDelayedDispatch(_) >> resultDTO

        when:
        def result = testObj.execute(new CheckVBKOrderDelayedDispatchRequestType())

        then: "验证返回结果里属性值是否符合预期"
        result.responseResult == expectedResponseResult
        result.isDelayedDispatch == expectedIsDelayedDispatch
        result.confirmType == expectedConfirmType
        result.lastConfirmCarTime == expectedLastConfirmCarTime
        result.lastConfirmCarTimeBj == expectedLastConfirmCarTimeBj

        where: "表格方式验证多种分支调用场景"
        resultDTO || expectedResponseResult || expectedIsDelayedDispatch || expectedConfirmType || expectedLastConfirmCarTime || expectedLastConfirmCarTimeBj
        new DelayedDspCheckResultDTO(true, ConfirmType.DISPATCH_CONFIRMED, "", "") || new ResponseResult(true, "200", "") || true || ConfirmType.DISPATCH_CONFIRMED.getCode() || "" || ""
        new DelayedDspCheckResultDTO(true, null, "", "") || new ResponseResult(true, "200", "") || true || "" || "" || ""
        new DelayedDspCheckResultDTO(true, ConfirmType.SERVICE_PROVIDER_CONFIRMED, "1", "2") || new ResponseResult(true, "200", "") || true || ConfirmType.SERVICE_PROVIDER_CONFIRMED.getCode() || "1" || "2"
    }

    @Unroll
    def "onExecutingTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        highLevelCheckService.isHighLevelUserOrder(_, _) >> true

        when:
        def result = testObj.onExecuting(request)

        then: "验证返回结果里属性值是否符合预期"
        thrown(e)

        where: "表格方式验证多种分支调用场景"
        request || e
        new CheckVBKOrderDelayedDispatchRequestType()  || ServiceValidationException
        new CheckVBKOrderDelayedDispatchRequestType(userOrderId: "123")  || ServiceValidationException
        new CheckVBKOrderDelayedDispatchRequestType(userOrderId: "123", cityId: 1L)  || ServiceValidationException
        new CheckVBKOrderDelayedDispatchRequestType(userOrderId: "123", cityId: 1L, categoryCode: CategoryCodeEnum.FROM_AIRPORT.type) || ServiceValidationException
        new CheckVBKOrderDelayedDispatchRequestType(userOrderId: "123", cityId: 1L, categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, vehicleGroupid: 117) || ServiceValidationException
        new CheckVBKOrderDelayedDispatchRequestType(userOrderId: "123", cityId: 1L, categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, vehicleGroupid: 117, estimatedUseTime: "123") || ServiceValidationException
        new CheckVBKOrderDelayedDispatchRequestType(userOrderId: "123", cityId: 1L, categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, vehicleGroupid: 117, estimatedUseTime: "123", estimatedUseTimeBj: "456") || ServiceValidationException
        new CheckVBKOrderDelayedDispatchRequestType(userOrderId: "123", cityId: 1L, categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, vehicleGroupid: 117, estimatedUseTime: "123", estimatedUseTimeBj: "456", useDays: 0) || ServiceValidationException
        new CheckVBKOrderDelayedDispatchRequestType(userOrderId: "123", cityId: 1L, categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, vehicleGroupid: 117, estimatedUseTime: "123", estimatedUseTimeBj: "456", useDays: 1) || ServiceValidationException
        new CheckVBKOrderDelayedDispatchRequestType(userOrderId: "123", cityId: 1L, categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, vehicleGroupid: 117, estimatedUseTime: "123", estimatedUseTimeBj: "456", useDays: 1, skuId: 1L) || ServiceValidationException
        new CheckVBKOrderDelayedDispatchRequestType(userOrderId: "123", cityId: 1L, categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, vehicleGroupid: 117, estimatedUseTime: "123", estimatedUseTimeBj: "456", useDays: 1, skuId: 1L, fromLocation: new PoiDTO()) || ServiceValidationException
        new CheckVBKOrderDelayedDispatchRequestType(userOrderId: "123", cityId: 1L, categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, vehicleGroupid: 117, estimatedUseTime: "123", estimatedUseTimeBj: "456", useDays: 1, skuId: 1L, fromLocation: new PoiDTO(1.0, 2.0, "a", 1, "")) || ServiceValidationException
        new CheckVBKOrderDelayedDispatchRequestType(userOrderId: "123", cityId: 1L, categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, vehicleGroupid: 117, estimatedUseTime: "123", estimatedUseTimeBj: "456", useDays: 1, skuId: 1L, fromLocation: new PoiDTO(1.0, 2.0, "a", 1, ""), toLocation: new PoiDTO(1.0, 2.0, "a", 1, "")) || ServiceValidationException
        new CheckVBKOrderDelayedDispatchRequestType(userOrderId: "123", cityId: 1L, categoryCode: CategoryCodeEnum.FROM_AIRPORT.type, vehicleGroupid: 117, estimatedUseTime: "123", estimatedUseTimeBj: "456", useDays: 1, skuId: 1L, fromLocation: new PoiDTO(1.0, 2.0, "a", 1, ""), toLocation: new PoiDTO(1.0, 2.0, "a", 1, ""), distributionChannel: 5) || ServiceValidationException
    }
}
