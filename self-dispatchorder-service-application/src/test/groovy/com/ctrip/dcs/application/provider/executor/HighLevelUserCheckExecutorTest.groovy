package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.domain.common.service.HighLevelCheckService
import com.ctrip.dcs.self.dispatchorder.interfaces.HighLevelUserRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.HighLevelUserResponseType
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.Mockito.when

class HighLevelUserCheckExecutorTest extends Specification {
    @Mock
    HighLevelCheckService highLevelCheckService
    @InjectMocks
    HighLevelUserCheckExecutor highLevelUserCheckExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(highLevelCheckService.isHighLevelUserOrder(anyInt(), anyInt())).thenReturn(true)

        when:
        HighLevelUserRequestType requestType = new HighLevelUserRequestType();
        requestType.setVipGrade(0);
        requestType.setCityId(1);
        HighLevelUserResponseType result = highLevelUserCheckExecutor.execute(requestType)

        then:
        result != null
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme