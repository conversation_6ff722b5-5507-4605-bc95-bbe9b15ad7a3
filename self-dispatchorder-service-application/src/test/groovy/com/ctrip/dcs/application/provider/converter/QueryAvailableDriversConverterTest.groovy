package com.ctrip.dcs.application.provider.converter

import com.ctrip.dcs.application.query.api.AvailableDriversQuery
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryAvailableDriversRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.AvailableDriverDTO
import spock.lang.*

/**
 * <AUTHOR>
 */
class QueryAvailableDriversConverterTest extends Specification {

    def "test to Available Drivers Query"() {
        given:
        QueryAvailableDriversRequestType request = new QueryAvailableDriversRequestType()
        request.setDspOrderId("dspOrderId")
        request.setDuid("duid")
        when:
        AvailableDriversQuery result = QueryAvailableDriversConverter.toAvailableDriversQuery(request)

        then:
        with(result) {
            dspOrderId == "dspOrderId"
            duid == "duid"
        }
    }

    def "test to Available Driver DTO"() {
        given:
        DriverVO driverVO = Mock(DriverVO)
        DspOrderVO orderVO = Mock(DspOrderVO)
        DspModelVO dspModelVO = Mock(DspModelVO)
        SortModel sortModel = Mock(SortModel)
        driverVO.getDriverId() >> 1L
        orderVO.getDspOrderId() >> "dspOrderId"
        dspModelVO.getOrder() >> orderVO
        dspModelVO.getDriver() >> driverVO
        sortModel.getModel() >> dspModelVO
        sortModel.getScore() >> 1.0
        when:
        List<AvailableDriverDTO> result = QueryAvailableDriversConverter.toAvailableDriverDTO([sortModel], "duid")

        then:
        result.size() == 1
        result.each {
            it.dspOrderId == "dspOrderId"
            it.duid == "duid"
            it.driverId == "supplyOrderId"
            it.value == "1"
        }
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme