package com.ctrip.dcs.application.listener.grab.record

import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKGrabTaskRecordRepository
import com.ctrip.dcs.infrastructure.common.util.DateZoneConvertUtil
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class FinishGrabTaskOperateTaskRecordTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DateZoneConvertUtil dateZoneConvertUtil
    @Mock
    VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository
    @Mock
    VBKGrabTaskRecordRepository vbkGrabTaskRecordRepository
    @Mock
    VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository
    @Mock
    ConfigService map
    @InjectMocks
    FinishGrabTaskOperateTaskRecord finishGrabTaskOperateTaskRecord

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test build Record Content"() {
        given:
        when(map.getString(anyString(), anyString())).thenReturn("getStringResponse")

        when:
        String result = finishGrabTaskOperateTaskRecord.buildRecordContent(new VBKGrabTaskOperateDTO(successNum: 0))

        then:
        result == "getStringResponse"
    }

    def "test assemble Param"() {
        given:
        when(vbkDriverGrabTaskRepository.queryByVBKGrabTaskId(anyString())).thenReturn(new VBKDriverGrabTaskDO(cityId: 0))
        when(vbkDriverGrabOrderRepository.countByTaskIdAndStatus(anyString(), anyInt())).thenReturn(0)

        when:
        def result = finishGrabTaskOperateTaskRecord.assembleParam(new VBKGrabTaskOperateDTO(taskId: "taskId", successNum: 0, cityId: 0))

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme