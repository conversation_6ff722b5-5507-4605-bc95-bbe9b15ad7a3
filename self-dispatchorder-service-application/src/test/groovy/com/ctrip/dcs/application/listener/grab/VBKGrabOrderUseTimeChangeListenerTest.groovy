package com.ctrip.dcs.application.listener.grab

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.GrabTaskStatus
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.dcs.infrastructure.service.DistributedLockServiceImpl
import com.ctrip.igt.framework.common.clogging.Logger
import qunar.tc.qmq.NeedRetryException
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class VBKGrabOrderUseTimeChangeListenerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DistributedLockService distributedLockService
    @Mock
    VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository
    @Mock
    MessageProviderService messageProducer
    @Mock
    VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository
    @Mock
    DistributedLockService.DistributedLock lock
    @Mock
    SelfOrderQueryGateway selfOrderQueryGateway
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    VBKDriverGrabOrderDO vbkDriverGrabOrder
    @Mock
    BaseMessage message
    @InjectMocks
    VBKGrabOrderUseTimeChangeListener vBKGrabOrderUseTimeChangeListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test use Time Change"() {
        given:
        when(message.getStringProperty("dspOrderId")).thenReturn("1")
        when(selfOrderQueryGateway.queryDspOrderForVBKGrab(anyString(), anyBoolean())).thenThrow(new RuntimeException("VBKGrabOrderUseTimeChangeListener_useTimeChange"))
        when:
        vBKGrabOrderUseTimeChangeListener.useTimeChange(message)

        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "VBKGrabOrderUseTimeChangeListener_useTimeChange"
    }

    def "test old Use Time Change"() {
        given:
        when(message.getStringProperty("supplyOrderIds")).thenReturn("").thenReturn("1")
        when(selfOrderQueryGateway.queryDspOrderForVBKGrab(anyString(), anyBoolean())).thenThrow(new RuntimeException("VBKGrabOrderUseTimeChangeListener_useTimeChange"))
        when:
        vBKGrabOrderUseTimeChangeListener.oldUseTimeChange(message)
        vBKGrabOrderUseTimeChangeListener.oldUseTimeChange(message)

        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "VBKGrabOrderUseTimeChangeListener_oldUseTimeChange"
    }

    def "test useTimeChange"() {
        given:
        when(selfOrderQueryGateway.queryDspOrder("1")).thenReturn(dspOrderVO)
        when(distributedLockService.getLock(anyString())).thenReturn(lock)
        when(vbkDriverGrabOrder.getSupplierId()).thenReturn(1l)
        when(lock.tryLock()).thenReturn(true)
        when(vbkDriverGrabOrderRepository.queryByDspOrderId("1", GrabTaskStatus.IN_PROGRESS.getCode())).thenReturn([vbkDriverGrabOrder])
        when:
        def result1 = vBKGrabOrderUseTimeChangeListener.useTimeChange("")
        def result2 = vBKGrabOrderUseTimeChangeListener.useTimeChange("1")

        then:
        result1 == null
        result2 == null
    }

    def "test buildVbkDriverGrabOrderDO"() {
        given:
        when(vbkDriverGrabOrder.getSupplierId()).thenReturn(1l)
        when(lock.tryLock()).thenReturn(true)
        when(vbkDriverGrabOrder.getVbkGrabTaskId()).thenReturn("1")
        when(dspOrderVO.getLastConfirmCarTime()).thenReturn(new Date())
        when(dspOrderVO.getLastConfirmCarTimeBj()).thenReturn(new Date())
        when(dspOrderVO.getEstimatedUseTime()).thenReturn(new Date())
        when(vbkDriverGrabTaskRepository.queryByVBKGrabTaskIds(["1"], GrabTaskStatus.IN_PROGRESS.getCode())).thenReturn([new VBKDriverGrabTaskDO(vbkGrabTaskId: "1", grabLimitHours: 1, grabRewardHours: 2)])
        when:
        List<VBKDriverGrabOrderDO> result = vBKGrabOrderUseTimeChangeListener.buildVbkDriverGrabOrderDO([vbkDriverGrabOrder], dspOrderVO)

        then:
        result.size() == 1
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme