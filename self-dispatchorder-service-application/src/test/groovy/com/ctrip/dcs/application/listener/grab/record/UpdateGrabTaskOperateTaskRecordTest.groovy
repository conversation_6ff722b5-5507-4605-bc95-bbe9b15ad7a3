package com.ctrip.dcs.application.listener.grab.record

import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKGrabTaskRecordRepository
import com.ctrip.dcs.infrastructure.common.util.DateZoneConvertUtil
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class UpdateGrabTaskOperateTaskRecordTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DateZoneConvertUtil dateZoneConvertUtil
    @Mock
    VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository
    @Mock
    VBKGrabTaskRecordRepository vbkGrabTaskRecordRepository
    @Mock
    VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository
    @Mock
    ConfigService map
    @InjectMocks
    UpdateGrabTaskOperateTaskRecord updateGrabTaskOperateTaskRecord

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test build Record Content"() {
        given:
        when(map.getString(anyString(), anyString())).thenReturn("getStringResponse")

        when:
        String result = updateGrabTaskOperateTaskRecord.buildRecordContent(new VBKGrabTaskOperateDTO(grabEndTime: "1", makeUpEffectTime: "1", grabEndTimeOrigin: "1", makeUpEffectTimeOrigin: "1", initialType: 0, initialTypeOrigin: 0, rewardsType: 0, rewardsTypeOrigin: 0, originDriverNum: 1l, initialValue: 0 as BigDecimal, initialRate: 0 as BigDecimal, rewardsValue: 0 as BigDecimal, rewardsRate: 0 as BigDecimal, initialValueOrigin: 0 as BigDecimal, initialRateOrigin: 0 as BigDecimal, rewardsValueOrigin: 0 as BigDecimal, rewardsRateOrigin: 0 as BigDecimal, driverNum: 1l))

        then:
        result == "getStringResponse"
    }

    def "test build build grab time"() {
        given:
        when(map.getString(anyString(), anyString())).thenReturn("getStringResponse")

        when:
        String result1 = updateGrabTaskOperateTaskRecord.buildGrabTime("s", "e", "1", "2")
        String result2 = updateGrabTaskOperateTaskRecord.buildGrabTime("s", "e", "2", "2")

        then:
        result1 == "getStringResponse"
        result2 == ""
    }

    def "test build inti or reward value"() {
        given:
        when(map.getString(anyString(), anyString())).thenReturn("getStringResponse")

        when:
        String result = updateGrabTaskOperateTaskRecord.buildInitOrRewardValue(key, defaultKey, type, typeOrigin, value, valueOrigin, rate, rateOrigin)

        then:
        result == content

        where:
        key   | defaultKey   | type | typeOrigin | value          | valueOrigin     | rate           | rateOrigin     || content
        "key" | "defaultKey" | 1    | 1          | BigDecimal.ONE | BigDecimal.ZERO | BigDecimal.ONE | BigDecimal.ONE  | "getStringResponse"
        "key" | "defaultKey" | 2    | 2          | BigDecimal.ONE | BigDecimal.ZERO | BigDecimal.ONE | BigDecimal.ZERO | "getStringResponse"
        "key" | "defaultKey" | 2    | 2          | BigDecimal.ONE | BigDecimal.ZERO | null           | BigDecimal.ZERO | "getStringResponse"
        "key" | "defaultKey" | 2    | 2          | BigDecimal.ONE | BigDecimal.ZERO | BigDecimal.ONE | null            | "getStringResponse"
        "key" | "defaultKey" | 1    | 2          | BigDecimal.ONE | BigDecimal.ZERO | BigDecimal.ONE | BigDecimal.ONE  | "getStringResponse"
        "key" | "defaultKey" | 2    | 1          | BigDecimal.ONE | BigDecimal.ZERO | BigDecimal.ONE | BigDecimal.ONE  | "getStringResponse"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme