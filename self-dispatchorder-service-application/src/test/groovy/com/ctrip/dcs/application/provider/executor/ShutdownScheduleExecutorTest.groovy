package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.command.ShutdownScheduleExeCmd
import com.ctrip.dcs.application.service.ShutdownScheduleService
import com.ctrip.dcs.domain.common.enums.ScheduleStatus
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import com.ctrip.dcs.self.dispatchorder.interfaces.ShutdownScheduleRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.ShutdownScheduleResponseType
import com.google.common.collect.Lists
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ShutdownScheduleExecutorTest extends Specification {
    @Mock
    ShutdownScheduleExeCmd shutdownScheduleExeCmd
    @Mock
    ScheduleRepository scheduleRepository
    @Mock
    ShutdownScheduleService shutdownScheduleService
    @InjectMocks
    ShutdownScheduleExecutor shutdownScheduleExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        ShutdownScheduleRequestType requestType = new ShutdownScheduleRequestType()
        requestType.setScheduleIds(Lists.newArrayList(1l, 2l))

        when:
        ShutdownScheduleResponseType result = shutdownScheduleExecutor.execute(requestType)

        then:
        result.getResponseResult().getReturnCode() == "200"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme