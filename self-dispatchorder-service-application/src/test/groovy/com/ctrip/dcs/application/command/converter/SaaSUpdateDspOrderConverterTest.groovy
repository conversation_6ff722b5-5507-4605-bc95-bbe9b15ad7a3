package com.ctrip.dcs.application.command.converter

import com.ctrip.dcs.application.command.api.CreateDspOrderCommand
import com.ctrip.dcs.application.command.api.SaaSUpdateDspOrderCommand
import com.ctrip.dcs.application.provider.converter.SaaSCreateDspOrderConverter
import com.ctrip.dcs.application.provider.converter.SaaSUpdateDspOrderConverter
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.infrastructure.common.util.LocalDateUtils
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCreateDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasUpdateDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.*
import com.ctrip.igt.framework.common.exception.BizException
import org.assertj.core.util.Lists
import spock.lang.Specification

class SaaSUpdateDspOrderConverterTest extends Specification {


    def "test creat convert1"() {
        given:
        when: "执行校验方法"
        def cmd = SaaSUpdateDspOrderConverter.converter(getReq1())

        then: "验证校验结果"
        cmd.getDspOrderDO().getDspOrderId().toInteger() == 111
    }

    def "test creat convert2"() {
        given:
        when: "执行校验方法"
        def cmd = SaaSUpdateDspOrderConverter.converter(getReq2())

        then: "验证校验结果"
        cmd.getDspOrderDO().getDspOrderId().toInteger() == 111
    }

    def "test creat convert3"() {
        given:
        when: "执行校验方法"
        def cmd = SaaSUpdateDspOrderConverter.converter(getReq3())

        then: "验证校验结果"
        cmd.getDspOrderDO().getDspOrderId().toInteger() == 111
    }
//
//    def "test creat exception"() {
//        given:
//        def code = "09011000";
//        when: "执行校验方法"
//        def cmd = SaaSUpdateDspOrderConverter.converter(getReq2())
//        then: "验证校验结果"
//        def ex = thrown(BizException)
//        ex.code.equals( code)
//    }


    SaasUpdateDspOrderRequestType getReq3() {
        SaasUpdateDspOrderInfo updateDspOrderInfo = buildSaasUpdateDspOrderInfo()
        def saasUpdateDspOrderRequestType = new SaasUpdateDspOrderRequestType(null, null, updateDspOrderInfo,1L);
        return saasUpdateDspOrderRequestType
    }

    SaasUpdateDspOrderRequestType getReq2() {
        SaasUpdateDspOrderInfo updateDspOrderInfo = buildSaasUpdateDspOrderInfo()
        updateDspOrderInfo.setOrderSourceCode(2)
        updateDspOrderInfo.setExtraFlightInfo(null)
        updateDspOrderInfo.setUserCount(null)
        updateDspOrderInfo.setExtendInfo(null)
        updateDspOrderInfo.setExtraFlightInfo(null)
        def saasUpdateDspOrderRequestType = new SaasUpdateDspOrderRequestType(null, null, updateDspOrderInfo,1L);
        return saasUpdateDspOrderRequestType
    }

    SaasUpdateDspOrderRequestType getReq1() {
        SaasUpdateDspOrderInfo updateDspOrderInfo = buildSaasUpdateDspOrderInfo()
        updateDspOrderInfo.setOrderSourceCode(2)
        SaaSFeeInfo feeInfo = new SaaSFeeInfo()
        feeInfo.setCostAmount(new BigDecimal(1))
        feeInfo.setUserCurrency("CNY")
        feeInfo.setSupplierCurrency("CNY")
        updateDspOrderInfo.setFeeInfo(feeInfo)
        def saasUpdateDspOrderRequestType = new SaasUpdateDspOrderRequestType(null, null, updateDspOrderInfo,1L);
        return saasUpdateDspOrderRequestType
    }


    SaasUpdateDspOrderInfo buildSaasUpdateDspOrderInfo(){
        def saasUpdateDspOrderInfo = new SaasUpdateDspOrderInfo()
        SaaSBaseInfo baseInfo = new SaaSBaseInfo()
        baseInfo.setDspOrderId(null)
        baseInfo.setVbkOrderId("111")
        baseInfo.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        baseInfo.setUserOrderId("1111")
        baseInfo.setCityId(111)
        baseInfo.setCityName("beijing")
        baseInfo.setFromCityId(111)
        baseInfo.setDeptCity("beijing")
        baseInfo.setToCityId(111)
        baseInfo.setArriveCityName("beijing")
        baseInfo.setVehicleGroupId(1)
        baseInfo.setVehicleGroupName("预订车组名称")
        baseInfo.setEstimatedUseTime(new Date() as String)
            baseInfo.setEstimatedUseTime(LocalDateUtils.toString(new Date(), "yyyy-MM-dd HH:mm:ss"))

        baseInfo.setUseDays(null)
        baseInfo.setSupplierId(1)
        baseInfo.setDriverOrderId("")
        baseInfo.setOrderSourceCode(OrderSourceCodeEnum.DISTRIBUTOR.getCode())
        baseInfo.setOrderCancelReasonDetail(null)

        SaaSPoiInfo fromPoi = new SaaSPoiInfo()
        fromPoi.setAddress("beijing")
        fromPoi.setCityId("111")
        saasUpdateDspOrderInfo.setFromPoi(fromPoi)
        saasUpdateDspOrderInfo.setDspOrderId("111")
        SaaSPoiInfo toPoi = new SaaSPoiInfo()
        toPoi.setAddress("beijing")
        toPoi.setCityId("111")
        saasUpdateDspOrderInfo.setToPoi(toPoi)
        saasUpdateDspOrderInfo.setEstimatedUseTime(LocalDateUtils.toString(new Date(), "yyyy-MM-dd HH:mm:ss"))
        SaaSExtraFlightInfo extraFlightInfo = new SaaSExtraFlightInfo()
        extraFlightInfo.setFlightNo("BJ103")
        extraFlightInfo.setTerminalName("航站楼名称")
        extraFlightInfo.setFlightArriveTime(LocalDateUtils.toString(LocalDateUtils.addDays(new Date(), 12), "yyyy-MM-dd HH:mm:ss"))
        saasUpdateDspOrderInfo.setExtraFlightInfo(extraFlightInfo)


        SaaSXproductInfo xproductInfo = new SaaSXproductInfo()
        xproductInfo.setAdditionalServices("附加服务")
        saasUpdateDspOrderInfo.setXproductInfo(xproductInfo)

        SaaSUserCountInfo userCount = new SaaSUserCountInfo()
        userCount.setAdultCount(1)
        userCount.setChildCount(1)
        userCount.setBagCount(1)
        saasUpdateDspOrderInfo.setUserCount(userCount)

        SaaSExtendInfo extendInfo = new SaaSExtendInfo()
        extendInfo.setDriverRemark("司机可见备注")
        saasUpdateDspOrderInfo.setExtendInfo(extendInfo)


        SaaSFeeInfo feeInfo = new SaaSFeeInfo()
        feeInfo.setCostAmount(null)
        feeInfo.setUserCurrency(null)
        feeInfo.setSupplierCurrency(null)
        saasUpdateDspOrderInfo.setFeeInfo(feeInfo)
        return saasUpdateDspOrderInfo
    }


    List<DspOrderDO> getDspOrderVO() {
        def order = new DspOrderDO()
        order.setDspOrderId("16211561979363356")
        return Lists.newArrayList(order)
    }

    List<DspOrderDO> getDspOrderVOExist() {
        def order = new DspOrderDO()
        order.setDspOrderId("16211561979363356")
        return Lists.newArrayList(order)
    }


}
