package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.UpdateDriverHeadTailOrderCommand
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DriverHeadOrderVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DriverWorkTimeVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.schedule.repository.DriverHeadTailOrderRepository
import com.ctrip.dcs.infrastructure.common.converter.TmsTransportConvertor
import qunar.tc.qmq.NeedRetryException
import spock.lang.Specification
import spock.lang.Unroll

/**
 * UpdateDriverHeadTailOrderExeCmd 测试类
 *
 * <AUTHOR>
 * @date 2024/12/2 15:00:57
 * @version 1.0
 */
class UpdateDriverHeadTailOrderExeCmdTest extends Specification {
    def testObj = new UpdateDriverHeadTailOrderExeCmd()
    def distributedLockService = Mock(DistributedLockService)
    def queryDriverService = Mock(QueryDriverService)
    def queryDspOrderService = Mock(QueryDspOrderService)
    def driverHeadTailOrderRepository = Mock(DriverHeadTailOrderRepository)
    def commonConfConfig = Mock(ConfigService)
    def lock = Mock(DistributedLockService.DistributedLock)

    def setup() {
        testObj.distributedLockService = distributedLockService
        testObj.queryDriverService = queryDriverService
        testObj.queryDspOrderService = queryDspOrderService
        testObj.driverHeadTailOrderRepository = driverHeadTailOrderRepository
        testObj.commonConfConfig = commonConfConfig
    }

    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        queryDspOrderService.queryOrderDetailForSchedule(_,_,_) >> dspOrderVO
        queryDriverService.queryDriver(_, _, _) >> driver
        def lock = Mock(DistributedLockService.DistributedLock);
        distributedLockService.getLock(_) >> lock;
        lock.tryLock() >> true
        commonConfConfig.getInteger("update_driver_head_tail_switch", YesOrNo.NO.getCode()) >> 1
        when:
        spy.execute(command)

        then: "验证返回结果里属性值是否符合预期"
        expectedTimes * distributedLockService.getLock(_)

        where: "表格方式验证多种分支调用场景"
        command                                             || dspOrderVO                                                                                                  || driver         || expectedTimes
        new UpdateDriverHeadTailOrderCommand(1L, "")        || new DspOrderVO(estimatedUseTime: new Date(), categoryCode: CategoryCodeEnum.FROM_AIRPORT, supplierId: 1L)   || new DriverVO() || 0
        new UpdateDriverHeadTailOrderCommand(null, "12345") || new DspOrderVO(estimatedUseTime: new Date(), categoryCode: CategoryCodeEnum.FROM_AIRPORT, supplierId: 1L)   || new DriverVO() || 0
        new UpdateDriverHeadTailOrderCommand(1L, "12345")   || new DspOrderVO(estimatedUseTime: new Date(), categoryCode: null, supplierId: 1L)                            || new DriverVO() || 0
        new UpdateDriverHeadTailOrderCommand(1L, "12345")   || new DspOrderVO(estimatedUseTime: new Date(), categoryCode: CategoryCodeEnum.FROM_AIRPORT, supplierId: null) || new DriverVO() || 0
        new UpdateDriverHeadTailOrderCommand(1L, "12345")   || new DspOrderVO(estimatedUseTime: new Date(), categoryCode: CategoryCodeEnum.FROM_AIRPORT, supplierId: 1L)   || null           || 0
        new UpdateDriverHeadTailOrderCommand(1L, "12345")   || new DspOrderVO(estimatedUseTime: new Date(), categoryCode: CategoryCodeEnum.FROM_AIRPORT, supplierId: 1L)   || new DriverVO(1L, false) || 1
    }

    @Unroll
    def "execute2_Test retryException"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        def distributedLock = Mock(DistributedLockService.DistributedLock);
        distributedLockService.getLock(_) >> distributedLock;
        lock.tryLock() >> false
        commonConfConfig.getInteger("update_driver_head_tail_switch", YesOrNo.NO.getCode()) >> 1
        when:
        def result = spy.execute(driver, date)

        then: "验证返回结果里属性值是否符合预期"
        def excepiton = thrown(NeedRetryException)

        where: "表格方式验证多种分支调用场景"
        date       || driver
        new Date() || new DriverVO(driverId: 1L)
    }

    @Unroll
    def "execute2_Test success"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        def distributedLock = Mock(DistributedLockService.DistributedLock);
        distributedLockService.getLock(_) >> distributedLock;
        lock.tryLock() >> true
        commonConfConfig.getInteger("update_driver_head_tail_switch", YesOrNo.NO.getCode()) >> 1
        when:
        def result = spy.execute(driver, date)

        then: "验证返回结果里属性值是否符合预期"
        def excepiton = thrown(NeedRetryException)

        where: "表格方式验证多种分支调用场景"
        date       || driver
        new Date() || new DriverVO(driverId: 1L)
    }


    @Unroll
    def "queryOrderTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        queryDspOrderService.queryOrderDetail(_) >> new DspOrderVO()
        commonConfConfig.getInteger("update_driver_head_tail_switch", YesOrNo.NO.getCode()) >> 1
        when:
        def result = testObj.queryOrder(command)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult

        where: "表格方式验证多种分支调用场景"
        command || expectedResult
        null    || null
        new UpdateDriverHeadTailOrderCommand(12L, "")    || null
    }

    @Unroll
    def "queryOrderTest success"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        queryDspOrderService.queryOrderDetailForSchedule(_,_,_) >> new DspOrderVO(dspOrderId: "dspOrderId", userOrderId: "userOrderId", estimatedUseTime: DateUtil.parseDateStr2Date("2024-12-12 08:00:00"))
        commonConfConfig.getInteger("update_driver_head_tail_switch", YesOrNo.NO.getCode()) >> 1
        when:
        def result = testObj.queryOrder(command)

        then: "验证返回结果里属性值是否符合预期"
        result.getDspOrderId() == expectedResult.getDspOrderId()
        result.getUserOrderId() == expectedResult.getUserOrderId()
        result.getEstimatedUseTime() == expectedResult.getEstimatedUseTime()

        where: "表格方式验证多种分支调用场景"
        command || expectedResult
        new UpdateDriverHeadTailOrderCommand(12L, "dspOrderId")    || new DspOrderVO(dspOrderId: "dspOrderId", userOrderId: "userOrderId", estimatedUseTime: DateUtil.parseDateStr2Date("2024-12-12 08:00:00"))
    }

    @Unroll
    def "queryDriverTest  null"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        queryDriverService.queryDriver(_, _, _) >> new DriverVO(driverId: 12L, driverName: "test", driverPhone: "testPhone")
        commonConfConfig.getInteger("update_driver_head_tail_switch", YesOrNo.NO.getCode()) >> 1
        when:
        def result = testObj.queryDriver(command, dspOrder)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        if (result != null && expectedResult != null) {
            result.getDriverId() == expectedResult.getDriverId()
            result.getDriverName() == expectedResult.getDriverName()
            result.getDriverPhone() == expectedResult.getDriverPhone()
        }

        where: "表格方式验证多种分支调用场景"
        dspOrder                                                                      | command                                                    || expectedResult
        new DspOrderVO(categoryCode: CategoryCodeEnum.ALL, supplierId: 0)             | new UpdateDriverHeadTailOrderCommand(null, null)           || null
        null                                                                          | new UpdateDriverHeadTailOrderCommand(12L, "dspOrderId123") || null
        new DspOrderVO(categoryCode: null, supplierId: 1L)                            | new UpdateDriverHeadTailOrderCommand(12L, "dspOrderId123") || null
        new DspOrderVO(categoryCode: CategoryCodeEnum.FROM_AIRPORT, supplierId: null) | new UpdateDriverHeadTailOrderCommand(12L, "dspOrderId123") || null
    }

    @Unroll
    def "queryDriverTest  success"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        queryDriverService.queryDriver(_, _, _) >> new DriverVO(driverId: 12L, driverName: "test", driverPhone: "testPhone")
        commonConfConfig.getInteger("update_driver_head_tail_switch", YesOrNo.NO.getCode()) >> 1
        when:
        def result = testObj.queryDriver(command, dspOrder)

        then: "验证返回结果里属性值是否符合预期"
        result.getDriverId() == expectedResult.getDriverId()
        result.getDriverName() == expectedResult.getDriverName()
        result.getDriverPhone() == expectedResult.getDriverPhone()


        where: "表格方式验证多种分支调用场景"
        dspOrder                                                                    | command                                                    || expectedResult
        new DspOrderVO(categoryCode: CategoryCodeEnum.FROM_AIRPORT, supplierId: 1L) | new UpdateDriverHeadTailOrderCommand(12L, "dspOrderId123") || new DriverVO(driverId: 12L, driverName: "test", driverPhone: "testPhone")
    }

// 逻辑简单 暂时可以不用ut
//    @Unroll
//    def "handleHeadOrderTest"() {
//        given: "设定相关方法入参"
//        def spy = Spy(testObj)
//        and: "Mock相关接口返回"
//
//        and: "Spy相关接口"
//        spy.queryHeadOrder(_, _) >> null
//
//        when:
//        def result = spy.handleHeadOrder(driver, workTime)
//        then: "验证返回结果里属性值是否符合预期"
//
//        result == expectedResult
//        where: "表格方式验证多种分支调用场景"
//        driver         | workTime               || expectedResult
//        new DriverVO() | new DriverWorkTimeVO() || null
//    }
//
//
//    @Unroll
//    def "handleTailOrderTest"() {
//        given: "设定相关方法入参"
//        def spy = Spy(testObj)
//        and: "Mock相关接口返回"
//
//        and: "Spy相关接口"
//        spy.queryTailOrder(_, _) >> null
//
//        when:
//        def result = spy.handleTailOrder(driver, workTime)
//        then: "验证返回结果里属性值是否符合预期"
//
//        result == expectedResult
//        where: "表格方式验证多种分支调用场景"
//        driver         | workTime               || expectedResult
//        new DriverVO() | new DriverWorkTimeVO() || null
//    }


    @Unroll
    def "queryHeadOrderTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        and: "Spy相关接口"
        spy.queryDriverOrderList(_, _, _) >> orders
        commonConfConfig.getInteger("update_driver_head_tail_switch", YesOrNo.NO.getCode()) >> 1
        when:
        def result = spy.queryHeadOrder(new DriverVO(driverId: 1L), new DriverWorkTimeVO(start: DateUtil.parseDateStr2Date("2025-01-01 08:00"), end: DateUtil.parseDateStr2Date("2025-01-01 20:00")))

        then: "验证返回结果里属性值是否符合预期"
        result.getDriverId() == expectedResult.getDriverId()
        result.getDate() == expectedResult.getDate()
        result.getDspOrderId() == expectedResult.getDspOrderId()
        result.getEstimatedUseTime() == expectedResult.getEstimatedUseTime()

        where: "表格方式验证多种分支调用场景"
        orders || expectedResult
        []     || new DriverHeadOrderVO(1L, DateUtil.parseDateStr2Date("2025-01-01 08:00"))
        [new DspOrderVO(dspOrderId: "dspOrderId", estimatedUseTime: DateUtil.parseDateStr2Date("2025-01-01 08:30"))] || new DriverHeadOrderVO(1L, DateUtil.parseDateStr2Date("2025-01-01 08:00"), "dspOrderId", DateUtil.parseDateStr2Date("2025-01-01 08:30"))
        [new DspOrderVO(dspOrderId: "dspOrderId", estimatedUseTime: DateUtil.parseDateStr2Date("2025-01-01 09:30")), new DspOrderVO(dspOrderId: "dspOrderId22", estimatedUseTime: DateUtil.parseDateStr2Date("2025-01-01 08:30"))] || new DriverHeadOrderVO(1L, DateUtil.parseDateStr2Date("2025-01-01 08:00"), "dspOrderId22", DateUtil.parseDateStr2Date("2025-01-01 08:30"))
    }


    @Unroll
    def "queryTailOrderTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        and: "Spy相关接口"
        spy.queryDriverOrderList(_, _, _) >> orders
        commonConfConfig.getInteger("update_driver_head_tail_switch", YesOrNo.NO.getCode()) >> 1
        when:
        def result = spy.queryTailOrder(new DriverVO(driverId: 1L), new DriverWorkTimeVO(start: DateUtil.parseDateStr2Date("2025-01-01 08:00"), end: DateUtil.parseDateStr2Date("2025-01-01 20:00")))

        then: "验证返回结果里属性值是否符合预期"
        result.getDriverId() == expectedResult.getDriverId()
        result.getDate() == expectedResult.getDate()
        result.getDspOrderId() == expectedResult.getDspOrderId()
        result.getEstimatedUseTime() == expectedResult.getEstimatedUseTime()

        where: "表格方式验证多种分支调用场景"
        orders || expectedResult
        []     || new DriverHeadOrderVO(1L, DateUtil.parseDateStr2Date("2025-01-01 08:00"))
        [new DspOrderVO(dspOrderId: "dspOrderId", estimatedUseTime: DateUtil.parseDateStr2Date("2025-01-01 19:30"))] || new DriverHeadOrderVO(1L, DateUtil.parseDateStr2Date("2025-01-01 08:00"), "dspOrderId", DateUtil.parseDateStr2Date("2025-01-01 19:30"))
        [new DspOrderVO(dspOrderId: "dspOrderId", estimatedUseTime: DateUtil.parseDateStr2Date("2025-01-01 19:30")), new DspOrderVO(dspOrderId: "dspOrderId22", estimatedUseTime: DateUtil.parseDateStr2Date("2025-01-01 18:30"))] || new DriverHeadOrderVO(1L, DateUtil.parseDateStr2Date("2025-01-01 08:00"), "dspOrderId", DateUtil.parseDateStr2Date("2025-01-01 19:30"))
    }


    @Unroll
    def "queryDriverOrderListTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        queryDspOrderService.queryOrderListWithDriverAndTime(_, _, _, _, _, _) >> [new DspOrderVO()]
        commonConfConfig.getInteger("update_driver_head_tail_switch", YesOrNo.NO.getCode()) >> 1
        when:
        def result = testObj.queryDriverOrderList(driver, begin, end)

        then: "验证返回结果里属性值是否符合预期"
        result.size() == expectedResult.size()

        where: "表格方式验证多种分支调用场景"
        driver         | end        | begin      || expectedResult
        new DriverVO() | new Date() | new Date() || [new DspOrderVO()]
    }

    @Unroll
    def "execute_Test before today"() {
        given: "设定相关方法入参"
        distributedLockService.getLock(_) >> lock;
        lock.tryLock() >> true
        PeriodsVO periods = TmsTransportConvertor.INSTANCE.map(["07:00~11:59", "17:00~23:59", "00:00~01:59"])
        commonConfConfig.getInteger("update_driver_head_tail_switch", YesOrNo.NO.getCode()) >> 1
        when:
        def result = testObj.execute( new DriverVO(driverId: 1L, workTimes: periods), DateUtil.parse("00:32", DateUtil.TIME_FORMAT))

        then: "验证返回结果里属性值是否符合预期"
        result == null
    }

}

