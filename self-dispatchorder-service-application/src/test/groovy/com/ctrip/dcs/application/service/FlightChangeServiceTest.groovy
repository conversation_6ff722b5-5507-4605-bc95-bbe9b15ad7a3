package com.ctrip.dcs.application.service

import com.ctrip.dcs.domain.common.service.QueryTransportGroupService
import com.ctrip.dcs.domain.common.value.BaseDetailVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig
import com.ctrip.dcs.infrastructure.gateway.remind.FlightProtectAppPushRemindGatewayImpl
import spock.lang.Specification
import spock.lang.Unroll

class FlightChangeServiceTest extends Specification {
    def testObj = new FlightChangeService()
    def selfOrderQueryGateway = Mock(SelfOrderQueryGateway)
    def flightProtectAppPushRemindGateway = Mock(FlightProtectAppPushRemindGatewayImpl)
    def queryTransportGroupService = Mock(QueryTransportGroupService)
    def businessTemplateInfoConfig = Mock(BusinessTemplateInfoConfig)

    def setup() {

        testObj.selfOrderQueryGateway = selfOrderQueryGateway
        testObj.flightProtectAppPushRemindGateway = flightProtectAppPushRemindGateway
        testObj.businessTemplateInfoConfig = businessTemplateInfoConfig
        testObj.queryTransportGroupService = queryTransportGroupService
    }

    @Unroll
    def "flightProtectNoticeVbkTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        selfOrderQueryGateway.queryOrderBaseDetailForFlightProtect( _) >> new BaseDetailVO(userOrderId: "123456", orderStatus: orderStatus, transportGroupId: transportGroup, supplierId: supplier)
        queryTransportGroupService.queryTransportGroup(_) >> new TransportGroupVO(informEmail: "123456")
        businessTemplateInfoConfig.getValueByKey(_) >> switchOn

        when:
        def result = testObj.flightProtectNoticeVbk(userOrderId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        userOrderId | switchOn | orderStatus | transportGroup | supplier || expectedResult
        "123456"    | "ON"     | 220         | 1              | 123      || null
        "123456"    | "ON"     | 900         | 1              | 123      || null
        "123456"    | "ON"     | 220         | 0              | 123      || null
        "123456"    | "ON"     | 220         | 1              | 0        || null
        "123456"    | "ON"     | 220         | null           | 012      || null
        "123456"    | "ON"     | 220         | 1              | null     || null
        "123456"    | "OFF"     | 220         | 1              | null     || null

    }
}
