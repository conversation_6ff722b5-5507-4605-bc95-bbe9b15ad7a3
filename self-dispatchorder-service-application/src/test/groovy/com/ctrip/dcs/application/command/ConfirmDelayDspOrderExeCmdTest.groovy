package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.ConfirmDelayDspOrderCommand
import com.ctrip.dcs.application.command.validator.ConfirmDelayDspOrderValidator
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.process.impl.SystemAssignProcess
import com.ctrip.dcs.domain.schedule.process.impl.SystemDelayAssignProcess
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import org.mockito.Mockito
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ConfirmDelayDspOrderExeCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    QueryDriverService queryDriverService
    @Mock
    QueryTransportGroupService queryTransportGroupService
    @Mock
    ScheduleTaskRepository scheduleTaskRepository
    @Mock
    ConfirmDelayDspOrderValidator validator
    @Mock
    SortModel sortModel
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverVO driverVO
    @Mock
    TransportGroupVO transportGroupVO
    @Mock
    ScheduleTaskDO scheduleTaskDO
    @Mock
    SystemAssignProcess systemAssignProcess
    @Mock
    SystemDelayAssignProcess systemDelayAssignProcess;
    @InjectMocks
    ConfirmDelayDspOrderExeCmd confirmDelayDspOrderExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(queryDspOrderService.query(anyString())).thenReturn(dspOrderVO)
        when(queryDriverService.queryDriver(anyLong(),any(),anyLong())).thenReturn(driverVO)

        def res = false
        when:
        try {
            confirmDelayDspOrderExeCmd.execute(new ConfirmDelayDspOrderCommand("dspOrderId", 1l, 1l, "duid"))
        } catch (Exception e) {
            if (e instanceof IllegalArgumentException) {
                res = true
            }
        }

        then:
        res == true
    }

    def "test execute2"() {
        given:
        when(queryDspOrderService.query(anyString())).thenReturn(null)
        when(queryDriverService.queryDriver(anyLong(),any(),anyLong())).thenReturn(driverVO)

        def res = false
        when:
        try {
            confirmDelayDspOrderExeCmd.execute(new ConfirmDelayDspOrderCommand("dspOrderId", 1l, 1l, "duid"))
        } catch (Exception e) {
            if (e instanceof IllegalArgumentException) {
                res = true
            }
        }

        then:
        res == true
    }

    def "test execute3"() {
        given:
        when(queryDspOrderService.query(anyString())).thenReturn(new DspOrderVO(supplierId: null))
        when(queryDriverService.queryDriver(anyLong(),any(),anyLong())).thenReturn(driverVO)

        def res = false
        when:
        try {
            confirmDelayDspOrderExeCmd.execute(new ConfirmDelayDspOrderCommand("dspOrderId", 1l, 1l, "duid"))
        } catch (Exception e) {
            if (e instanceof IllegalArgumentException) {
                res = true
            }
        }

        then:
        res == true
    }

    def "test execute exception"() {
        given:
        DspOrderVO vo = new DspOrderVO();
        vo.setCategoryCode(CategoryCodeEnum.C_DAY_RENTAL)
        vo.setOrderStatus(OrderStatusEnum.SERVICE_PROVIDER_CONFIRMED.getCode())
        vo.setSupplierId(10)
        when(queryDspOrderService.query(anyString())).thenReturn(vo)
        when(queryDriverService.queryDriver(1L,ParentCategoryEnum.DAY, 10L)).thenReturn(driverVO)
        when(queryTransportGroupService.queryTransportGroup(anyLong())).thenReturn(transportGroupVO)
        when(scheduleTaskRepository.query(anyString())).thenReturn(scheduleTaskDO)
        when(systemAssignProcess.execute(any() as ScheduleTaskDO, any() as DspModelVO)).thenReturn(false)

        def res = false;
        when:
        try {
            confirmDelayDspOrderExeCmd.execute(new ConfirmDelayDspOrderCommand("dspOrderId", 1l, 1l, "duid"))
        } catch (Exception e2) {
            if (e2 instanceof BizException) {
                res = true
            }
        }

        then:
        res == false
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme