package com.ctrip.dcs.application.scheduler

import com.ctrip.dcs.domain.common.service.IdempotentCheckService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import qunar.tc.schedule.MockParameter
import qunar.tc.schedule.Parameter
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class RetryScheduleJobTest extends Specification {

    IdempotentCheckService idempotentCheckService = Mock(IdempotentCheckService)

    ScheduleRepository scheduleRepository = Mock(ScheduleRepository)

    ScheduleTaskRepository scheduleTaskRepository = Mock(ScheduleTaskRepository)

    ScheduleDO schedule = Mock(ScheduleDO)

    ScheduleTaskDO scheduleTask = Mock(ScheduleTaskDO)

    MessageProviderService messageProducer = Mock(MessageProviderService)

    RetryScheduleJob retryScheduleJob = new RetryScheduleJob(scheduleRepository: scheduleRepository, scheduleTaskRepository: scheduleTaskRepository, messageProducer: messageProducer, idempotentCheckService: idempotentCheckService)

    def "Execute"() {
        given:
        Parameter parameter = new MockParameter()
        scheduleRepository.queryBefore(_ as Date, _ as Date) >> []
        when:
        def result = retryScheduleJob.execute(parameter)
        then:
        result == null
    }

    def "sendMessage 1"() {
        given:
        scheduleTaskRepository.query(_, _) >> [scheduleTask]
        schedule.getExecuteTime() >> new Date()
        schedule.retry(_) >> 1L
        schedule.getRound() >> 1
        schedule.getDspOrderId() >> "1"
        schedule.getScheduleId() >> 1L
        when:
        def result = retryScheduleJob.sendMessage([schedule])
        then:
        result == null
    }

    def "sendMessage 2"() {
        given:
        scheduleTaskRepository.query(_, _) >> []
        schedule.retry(_) >> null
        when:
        def result = retryScheduleJob.sendMessage([schedule])
        then:
        result == null
    }

    def "executeScheduleDelayTime 1"() {
        given:
        scheduleTaskRepository.query(_, _) >> []
        when:
        def result = retryScheduleJob.executeScheduleDelayTime(schedule)
        then:
        result == null
    }

    def "executeScheduleDelayTime 2"() {
        given:
        scheduleTaskRepository.query(_, _) >> [scheduleTask]
        schedule.getExecuteTime() >> new Date()
        schedule.retry(_) >> 1L
        when:
        def result = retryScheduleJob.executeScheduleDelayTime(schedule)
        then:
        result >= 0L
    }

}
