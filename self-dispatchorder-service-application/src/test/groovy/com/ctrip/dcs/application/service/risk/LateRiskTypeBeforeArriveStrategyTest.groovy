package com.ctrip.dcs.application.service.risk

import com.ctrip.dcs.domain.common.enums.LateRiskTypeEnum
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.dsporder.gateway.LateRiskRemindGateway
import com.ctrip.igt.framework.common.clogging.Logger
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/12/30 17:09
 */
class LateRiskTypeBeforeArriveStrategyTest extends Specification {
    def testObj = new LateRiskTypeBeforeArriveStrategy()
    def logger = Mock(Logger)
    def lateRiskRemindGateway = Mock(LateRiskRemindGateway)

    def setup() {
        testObj.lateRiskRemindGateway = lateRiskRemindGateway
        testObj.logger = logger
    }

    @Unroll
    def "test getType"() {
        given:
        when:
        def result = testObj.getType()
        then:
        Assert.assertTrue(Objects.equals(result, LateRiskTypeEnum.BEFORE_ARRIVE.getType()))
    }

    @Unroll
    def "test sendEmail"() {
        given:
        when:
        testObj.sendEmail(dspOrder, transportGroupVO)
        then:
        Assert.assertTrue(Objects.nonNull(transportGroupVO))
        where:
        transportGroupVO                                | dspOrder                                 || expectedResult
        new TransportGroupVO(informEmail: "")           | new DspOrderVO(dspOrderId: "dspOrderId") || null
        new TransportGroupVO(informEmail: "2312321312") | new DspOrderVO(dspOrderId: "dspOrderId") || null
    }

    @Unroll
    def "test sendPcStationLetter"() {
        given:
        DspOrderVO dspOrder = new DspOrderVO();
        TransportGroupVO transportGroup = new TransportGroupVO();
        when:
        testObj.sendPcStationLetter(dspOrder, transportGroup)
        then:
        Assert.assertTrue(Objects.nonNull(dspOrder))
    }

    @Unroll
    def "test sendMobileSupplierRemind"() {
        given:
        DspOrderVO dspOrder = new DspOrderVO();
        TransportGroupVO transportGroup = new TransportGroupVO();
        when:
        testObj.sendMobileSupplierRemind(dspOrder, transportGroup)
        then:
        Assert.assertTrue(Objects.nonNull(dspOrder))
    }

    @Unroll
    def "test sendLateRiskIVR"() {
        given:
        DspOrderVO dspOrder = new DspOrderVO();
        TransportGroupVO transportGroup = new TransportGroupVO();
        when:
        testObj.sendLateRiskIVR(dspOrder, transportGroup)
        then:
        Assert.assertTrue(Objects.nonNull(dspOrder))
    }
}
