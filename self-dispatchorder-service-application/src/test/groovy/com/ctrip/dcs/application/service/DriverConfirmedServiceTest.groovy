package com.ctrip.dcs.application.service

import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.domain.schedule.gateway.DriverPointsGateway
import com.ctrip.dcs.infrastructure.adapter.soa.TmsTransportServiceProxy
import com.ctrip.dcs.infrastructure.service.QueryDriverServiceImpl
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailDTOSOA
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOAResponseType
import com.google.common.collect.Maps
import org.junit.Assert
import spock.lang.Specification

class DriverConfirmedServiceTest extends Specification {

    def dspOrderConfirmRecordRepository = Mock(DspOrderConfirmRecordRepository)

    def tmsTransportServiceProxy  = Mock(TmsTransportServiceProxy)

    def driverPointsGateway = Mock(DriverPointsGateway)

    def queryDriverService = Mock(QueryDriverServiceImpl)


    def driverConfirm = new DriverConfirmedService(dspOrderConfirmRecordRepository:dspOrderConfirmRecordRepository,
            tmsTransportServiceProxy:tmsTransportServiceProxy,
            driverPointsGateway:driverPointsGateway, queryDriverService:queryDriverService)


    def "ContractUpdateDrvLevel"() {
        given:
        DspOrderConfirmRecordVO dspOrderConfirmRecordVO = new DspOrderConfirmRecordVO()
        dspOrderConfirmRecordVO.setCategoryCode("11")
        QueryDrvDetailSOAResponseType soaResponseType = new QueryDrvDetailSOAResponseType()
        QueryDrvDetailDTOSOA dtosoa = new QueryDrvDetailDTOSOA()
        dtosoa.setInternalScope(1)
        dtosoa.setAreaScope(1)
        soaResponseType.setData(dtosoa)
        dspOrderConfirmRecordRepository.find(_) >> dspOrderConfirmRecordVO
        queryDriverService.queryDrvDetail(_, _) >> soaResponseType
        Map<Long, String> driverLevelMap = Maps.newHashMap()
        driverLevelMap.put(1L,"102")
        driverPointsGateway.queryDriverLevel(_,_) >> driverLevelMap
        when:
        Boolean result = driverConfirm.contractUpdateDrvLevel(1L,1L)
        then:
        Assert.assertTrue(result)
    }

    def "ContractUpdateDrvLevel1"() {
        given:
        DspOrderConfirmRecordVO dspOrderConfirmRecordVO = new DspOrderConfirmRecordVO()
        dspOrderConfirmRecordVO.setCategoryCode("11")
        QueryDrvDetailSOAResponseType soaResponseType = new QueryDrvDetailSOAResponseType()
        QueryDrvDetailDTOSOA dtosoa = new QueryDrvDetailDTOSOA()
        dtosoa.setInternalScope(1)
        soaResponseType.setData(dtosoa)
        dspOrderConfirmRecordRepository.find(_) >> dspOrderConfirmRecordVO
        queryDriverService.queryDrvDetail(_,_) >> soaResponseType
        Map<Long, String> driverLevelMap = Maps.newHashMap()
        driverLevelMap.put(1L,"102")
        driverPointsGateway.queryDriverLevel(_,_) >> driverLevelMap
        when:
        Boolean result = driverConfirm.contractUpdateDrvLevel(1L,1L)
        then:
        Assert.assertTrue(result)
    }


    def "ContractUpdateDrvLevel2"() {
        given:
        DspOrderConfirmRecordVO dspOrderConfirmRecordVO = new DspOrderConfirmRecordVO()
        dspOrderConfirmRecordVO.setCategoryCode("11")
        QueryDrvDetailSOAResponseType soaResponseType = new QueryDrvDetailSOAResponseType()
        QueryDrvDetailDTOSOA dtosoa = new QueryDrvDetailDTOSOA()
        dtosoa.setInternalScope(1)
        dtosoa.setAreaScope(1)
        soaResponseType.setData(dtosoa)
        dspOrderConfirmRecordRepository.find(_) >> null
        when:
        Boolean result = driverConfirm.contractUpdateDrvLevel(1L,1L)
        then:
        Assert.assertTrue(!result)
    }


    def "ContractUpdateDrvLevel3"() {
        given:
        DspOrderConfirmRecordVO dspOrderConfirmRecordVO = new DspOrderConfirmRecordVO()
        dspOrderConfirmRecordVO.setCategoryCode("11")
        QueryDrvDetailSOAResponseType soaResponseType = new QueryDrvDetailSOAResponseType()
        QueryDrvDetailDTOSOA dtosoa = new QueryDrvDetailDTOSOA()
        dtosoa.setInternalScope(1)
        dtosoa.setAreaScope(1)
        soaResponseType.setData(dtosoa)
        dspOrderConfirmRecordRepository.find(_) >> dspOrderConfirmRecordVO
        queryDriverService.queryDrvDetail(_,_) >> null
        when:
        Boolean result = driverConfirm.contractUpdateDrvLevel(1L,1L)
        then:
        Assert.assertTrue(!result)
    }


    def "ContractUpdateDrvLevel4"() {
        given:
        DspOrderConfirmRecordVO dspOrderConfirmRecordVO = new DspOrderConfirmRecordVO()
        dspOrderConfirmRecordVO.setCategoryCode("11")
        QueryDrvDetailSOAResponseType soaResponseType = new QueryDrvDetailSOAResponseType()
        QueryDrvDetailDTOSOA dtosoa = new QueryDrvDetailDTOSOA()
        dtosoa.setInternalScope(1)
        dtosoa.setAreaScope(1)
        soaResponseType.setData(dtosoa)
        dspOrderConfirmRecordRepository.find(_) >> dspOrderConfirmRecordVO
        queryDriverService.queryDrvDetail(_,_) >> soaResponseType
        Map<Long, String> driverLevelMap = Maps.newHashMap()
//        driverLevelMap.put(1L,"102")
        driverPointsGateway.queryDriverLevel(_,_) >> driverLevelMap
        when:
        Boolean result = driverConfirm.contractUpdateDrvLevel(1L,1L)
        then:
        Assert.assertTrue(result)
    }


    def "ContractUpdateDrvLevel5"() {
        given:
        DspOrderConfirmRecordVO dspOrderConfirmRecordVO = new DspOrderConfirmRecordVO()
        dspOrderConfirmRecordVO.setCategoryCode("11")
        QueryDrvDetailSOAResponseType soaResponseType = new QueryDrvDetailSOAResponseType()
        QueryDrvDetailDTOSOA dtosoa = new QueryDrvDetailDTOSOA()
        dtosoa.setAreaScope(0)
        soaResponseType.setData(dtosoa)
        dspOrderConfirmRecordRepository.find(_) >> dspOrderConfirmRecordVO
        queryDriverService.queryDrvDetail(_,_) >> soaResponseType
        Map<Long, String> driverLevelMap = Maps.newHashMap()
        driverLevelMap.put(1L,"102")
        driverPointsGateway.queryDriverLevel(_,_) >> driverLevelMap
        when:
        Boolean result = driverConfirm.contractUpdateDrvLevel(1L,1L)
        then:
        Assert.assertTrue(result)
    }


    def "ContractUpdateDrvLevel6"() {
        given:
        DspOrderConfirmRecordVO dspOrderConfirmRecordVO = new DspOrderConfirmRecordVO()
        dspOrderConfirmRecordVO.setCategoryCode("11")
        DspOrderConfirmRecordVO.ConfirmSnapShotInfo  confirmSnapShotInfo= new DspOrderConfirmRecordVO.ConfirmSnapShotInfo()
        confirmSnapShotInfo.setDriverType(1)
        dspOrderConfirmRecordVO.setConfirmSnapshotInfo(confirmSnapShotInfo)
        QueryDrvDetailSOAResponseType soaResponseType = new QueryDrvDetailSOAResponseType()
        QueryDrvDetailDTOSOA dtosoa = new QueryDrvDetailDTOSOA()
        dtosoa.setInternalScope(1)
        dtosoa.setAreaScope(1)
        soaResponseType.setData(dtosoa)
        dspOrderConfirmRecordRepository.find(_) >> dspOrderConfirmRecordVO
        queryDriverService.queryDrvDetail(_,_) >> soaResponseType
        Map<Long, String> driverLevelMap = Maps.newHashMap()
        driverLevelMap.put(2L,"102")
        driverPointsGateway.queryDriverLevel(_,_) >> driverLevelMap
        when:
        Boolean result = driverConfirm.contractUpdateDrvLevel(1L,1L)
        then:
        Assert.assertTrue(result)
    }
    def "ContractUpdateDrvLevel7"() {
        given:
        DspOrderConfirmRecordVO dspOrderConfirmRecordVO = new DspOrderConfirmRecordVO()
        dspOrderConfirmRecordVO.setCategoryCode("day_rental")
        DspOrderConfirmRecordVO.ConfirmSnapShotInfo  confirmSnapShotInfo= new DspOrderConfirmRecordVO.ConfirmSnapShotInfo()
        confirmSnapShotInfo.setDriverType(1)
        dspOrderConfirmRecordVO.setConfirmSnapshotInfo(confirmSnapShotInfo)

        dspOrderConfirmRecordRepository.find(_) >> dspOrderConfirmRecordVO

        Map<Long, String> driverLevelMap = Maps.newHashMap()
        driverLevelMap.put(2L,1)
        driverPointsGateway.isQueryGuideLevel(_) >> true
        driverPointsGateway.batchQueryDriverGuideLevel(_) >> driverLevelMap
        when:
        Boolean result = driverConfirm.contractUpdateDrvLevel(2L,1L)
        then:
        Assert.assertTrue(result)
    }
}
