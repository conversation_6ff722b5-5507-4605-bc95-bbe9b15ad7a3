package com.ctrip.dcs.application.provider.converter

import com.ctrip.dcs.application.command.api.QueryIvrRecordCommand
import com.ctrip.dcs.domain.dsporder.entity.IvrRecordDO
import com.ctrip.dcs.self.dispatchorder.interfaces.IvrRecordInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryIvrRecordRequestType
import spock.lang.*

class QueryIvrRecordConverterTest extends Specification {

    def "test converter"() {
        when:
        QueryIvrRecordRequestType requestType = new QueryIvrRecordRequestType();
        QueryIvrRecordCommand result = QueryIvrRecordConverter.converter(requestType)

        then:
        result !=null
    }

    def "test converter Info"() {
        when:
        IvrRecordInfo result = QueryIvrRecordConverter.converterInfo(new IvrRecordDO())

        then:
        result != null
    }
}

//Generated with love by <PERSON>Me :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme