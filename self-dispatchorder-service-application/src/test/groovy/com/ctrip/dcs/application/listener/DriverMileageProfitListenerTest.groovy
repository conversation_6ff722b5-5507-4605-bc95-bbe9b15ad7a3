package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.command.CalculateDriverMileageProfitExeCmd
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.util.CategoryUtils
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DriverMileageProfitListenerTest extends Specification {

    QueryDspOrderService queryDspOrderService = Mock(QueryDspOrderService)

    QueryDriverService queryDriverService = Mock(QueryDriverService)

    DspOrderVO dspOrder = Mock(DspOrderVO)

    DriverVO driver = Mock(DriverVO)

    CalculateDriverMileageProfitExeCmd calculateDriverMileageProfitExeCmd = Mock(CalculateDriverMileageProfitExeCmd)

    DriverMileageProfitListener listener = new DriverMileageProfitListener(queryDspOrderService: queryDspOrderService, queryDriverService: queryDriverService, calculateDriverMileageProfitExeCmd: calculateDriverMileageProfitExeCmd)

    def "OnCancel"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("supplyOrderIds", "1")
        message.setProperty("driverId", "1")
        queryDspOrderService.queryOrderDetail("1") >> dspOrder
        dspOrder.getCategoryCode() >> CategoryCodeEnum.FROM_AIRPORT
        dspOrder.getSupplierId() >> 1
        queryDriverService.queryDriver(1L, ParentCategoryEnum.JNT, 1L) >> driver
        driver.getDriverId() >> 1L
        when:
        def result = listener.onCancel(message)
        then:
        result == null
    }

    def "OnTaken"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("supplyOrderIds", "1")
        message.setProperty("driverId", "1")
        queryDspOrderService.queryOrderDetail("1") >> dspOrder
        dspOrder.getCategoryCode() >> CategoryCodeEnum.FROM_AIRPORT
        dspOrder.getSupplierId() >> 1
        dspOrder.getEstimatedUseTime() >> DateUtil.parseDateStr2Date("2024-12-11 12:00:00")
        queryDriverService.queryDriver(1L, ParentCategoryEnum.JNT, 1L) >> driver
        driver.getDriverId() >> 1L
        driver.getWorkTimes() >> new PeriodsVO(["08:00~20:00"])
        when:
        def result = listener.onTaken(message)
        then:
        result == null
    }

    def "OnFinish"() {
        given:
        Message message = new BaseMessage()
        message.setProperty("supplyOrderIds", "1")
        message.setProperty("driverId", "1")
        queryDspOrderService.queryOrderDetail("1") >> dspOrder
        dspOrder.getCategoryCode() >> CategoryCodeEnum.FROM_AIRPORT
        dspOrder.getSupplierId() >> 1
        dspOrder.getEstimatedUseTime() >> DateUtil.parseDateStr2Date("2024-12-11 12:00:00")
        queryDriverService.queryDriver(1L, ParentCategoryEnum.JNT, 1L) >> driver
        driver.getDriverId() >> 1L
        driver.getWorkTimes() >> new PeriodsVO(["08:00~20:00"])
        when:
        def result = listener.onFinish(message)
        then:
        result == null
    }
}
