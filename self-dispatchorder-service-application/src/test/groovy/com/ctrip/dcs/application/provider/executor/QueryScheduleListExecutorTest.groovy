package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.command.ExecuteScheduleExeCmd
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryScheduleListRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.ScheduleInfo
import com.ctrip.igt.framework.common.clogging.Logger
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2025/2/7 18:03
 */
class QueryScheduleListExecutorTest extends Specification {
    def testObj = new QueryScheduleListExecutor()
    def logger = Mock(Logger)
    def cmd = Mock(ExecuteScheduleExeCmd)

    def setup() {
        testObj.logger = logger
        testObj.cmd = cmd
    }

    @Unroll
    def "test execute"() {
        given:
        cmd.queryScheduleList(_) >> [new ScheduleInfo()]

        when:
        QueryScheduleListRequestType requestType = new QueryScheduleListRequestType();
        requestType.setDspOrderId(dspOrderId);
        def result = testObj.execute(requestType)

        then:
        Assert.assertTrue(Objects.equals(result.getResponseResult().isSuccess(), expect));

        where:
        dspOrderId || expect
        ""         || Boolean.FALSE
        "2131231"  || Boolean.TRUE
    }
}
