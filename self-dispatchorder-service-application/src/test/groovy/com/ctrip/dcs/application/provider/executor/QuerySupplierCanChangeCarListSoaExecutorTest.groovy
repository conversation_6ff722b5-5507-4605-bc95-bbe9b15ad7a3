package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.command.QueryCanChangeCarListCmd
import com.ctrip.dcs.application.command.dto.CarInfo4VbkDTO
import com.ctrip.dcs.order.interfaces.dto.VehicleInfo
import com.ctrip.dcs.order.interfaces.message.QuerySupplierCanChangeCarListSoaRequestType
import com.ctrip.dcs.order.interfaces.message.QuerySupplierCanChangeCarListSoaResponseType
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.Specification
import spock.lang.Unroll

class QuerySupplierCanChangeCarListSoaExecutorTest extends Specification {
    def testObj = new QuerySupplierCanChangeCarListSoaExecutor()
    def queryCanChangeCarListCmd = Mock(QueryCanChangeCarListCmd)

    def setup() {

        testObj.queryCanChangeCarListCmd = queryCanChangeCarListCmd
    }

    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        queryCanChangeCarListCmd.querySupplierCanChangeCarList(_) >> resDto

        when:
        def result = testObj.execute(requestType)

        then: "验证返回结果里属性值是否符合预期"
        result.responseResult.success == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                                                                          |resDto || expectedResult
        new QuerySupplierCanChangeCarListSoaRequestType(cityId: 1L, dspOrderId: "dspOrderId", supplierId: 1L)|[new CarInfo4VbkDTO()] || true
        new QuerySupplierCanChangeCarListSoaRequestType(cityId: 1L, dspOrderId: "dspOrderId", supplierId: 1L)|new RuntimeException() || false
    }

    @Unroll
    def "validateTest"() {
        given: "设定相关方法入参"
        when:
        def result
        try{
            testObj.validate(validator, req)

        }catch(BizException e){
           result = e.message
        }



        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        validator | req                                                                                                   || expectedResult
        null      | new QuerySupplierCanChangeCarListSoaRequestType(cityId: 1L, dspOrderId: "dspOrderId", supplierId: 1L) || null
        null      | new QuerySupplierCanChangeCarListSoaRequestType(cityId: 1L, dspOrderId: "", supplierId: 1L) || "dspOrderId is empty"
        null      | new QuerySupplierCanChangeCarListSoaRequestType(cityId: null, dspOrderId: "dspOrderId", supplierId: 1L) || "cityId is empty"
        null      | new QuerySupplierCanChangeCarListSoaRequestType(cityId: 1L, dspOrderId: "dspOrderId", supplierId: null) || "supplierId is null"
    }
}
