package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.command.SubmitBroadcastGrabExeCmd
import com.ctrip.dcs.domain.common.constants.ConfigKey
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotStatusEnum
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO
import com.ctrip.dcs.domain.schedule.event.GuideGrabCentreEvent
import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway
import com.ctrip.dcs.domain.schedule.process.impl.GrabCentreProcess
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository
import com.ctrip.dcs.domain.schedule.service.CommonService
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import org.apache.commons.lang3.math.NumberUtils
import qunar.tc.qmq.Message
import spock.lang.Specification

import javax.annotation.Resource

/**
 * <AUTHOR>
 */
class DspOrderGrabCentrePushListenerTest extends Specification {

    DspOrderGrabCentrePushListener listener
    GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository
    GrabDspOrderSnapshotRepository grabDspOrderSnapshotRepository
    MessageProviderService messageProviderService
    GrabCentreProcess grabCentreProcess
    ConfigService broadcastGrabConfig
    SubmitBroadcastGrabExeCmd submitBroadcastGrabExeCmd
    SysSwitchConfigGateway sysSwitchConfigGateway
    CommonService commonService

    def setup() {
        grabDspOrderDriverIndexRepository = Mock(GrabDspOrderDriverIndexRepository)
        grabDspOrderSnapshotRepository = Mock(GrabDspOrderSnapshotRepository)
        messageProviderService = Mock(MessageProviderService)
        grabCentreProcess = Mock(GrabCentreProcess)
        broadcastGrabConfig = Mock(ConfigService)
        submitBroadcastGrabExeCmd = Mock(SubmitBroadcastGrabExeCmd)
        sysSwitchConfigGateway = Mock(SysSwitchConfigGateway)
        commonService = Mock(CommonService)

        listener = new DspOrderGrabCentrePushListener()
        listener.grabDspOrderDriverIndexRepository = grabDspOrderDriverIndexRepository
        listener.grabDspOrderSnapshotRepository = grabDspOrderSnapshotRepository
        listener.messageProviderService = messageProviderService
        listener.grabCentreProcess = grabCentreProcess
        listener.broadcastGrabConfig = broadcastGrabConfig
        listener.submitBroadcastGrabExeCmd = submitBroadcastGrabExeCmd
        listener.sysSwitchConfigGateway = sysSwitchConfigGateway
        listener.commonService = commonService
    }

    def "test onMessage"() {
        given:
        Message message = Mock(Message)
        message.getStringProperty("dspOrderId") >> "123"
        message.getStringProperty("driverIds") >> "1,2,3"

        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO()
        snapshot.setDuid("113886940277293104-20361818-113887137136967718-1-311-11-5-S113887137136967718-0-0")
        snapshot.setGrabStatus(GrabDspOrderSnapshotStatusEnum.GRAB)

        grabDspOrderSnapshotRepository.query("123") >> snapshot

        GrabDspOrderDriverIndexDO index1 = new GrabDspOrderDriverIndexDO()
        index1.setDspOrderId("123")
        index1.setDriverId(1L)
        index1.setCityId(1)
        index1.setIsValid(YesOrNo.YES.getCode())
        index1.setIsBroadcast(YesOrNo.YES.getCode())
        index1.setCategoryCode(ParentCategoryEnum.DAY)

        GrabDspOrderDriverIndexDO index2 = new GrabDspOrderDriverIndexDO()
        index2.setDspOrderId("123")
        index2.setDriverId(2L)
        index2.setCityId(1)
        index2.setIsValid(YesOrNo.YES.getCode())
        index2.setIsBroadcast(YesOrNo.YES.getCode())
        index2.setCategoryCode(ParentCategoryEnum.DAY)

        grabDspOrderDriverIndexRepository.query("123", [1L, 2L, 3L]) >> [index1, index2]

        submitBroadcastGrabExeCmd.isGrayscaleCity(1L) >> true
        grabCentreProcess.notPushDriverByOrder(1L, "123") >> true
        grabCentreProcess.notPushDriverByOrder(2L, "123") >> true
        grabCentreProcess.notPushDriverByDuration(1L) >> true
        grabCentreProcess.notPushDriverByDuration(2L) >> true

        broadcastGrabConfig.getLong(ConfigKey.BROADCAST_DRIVER_DELAY_KEY, NumberUtils.LONG_ZERO) >> 10L

        when:
        listener.onMessage(message)

        then:
        1 * messageProviderService.send(_ as GuideGrabCentreEvent)
    }
}
