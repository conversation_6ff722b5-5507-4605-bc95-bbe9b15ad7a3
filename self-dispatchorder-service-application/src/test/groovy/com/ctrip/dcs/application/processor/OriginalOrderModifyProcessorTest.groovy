package com.ctrip.dcs.application.processor

import com.ctrip.dcs.application.command.CreateScheduleExeCmd
import com.ctrip.dcs.application.command.ReDispatchConfirmExeCmd
import com.ctrip.dcs.domain.common.service.*
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.domain.schedule.service.RecommendService
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig
import spock.lang.Specification

class OriginalOrderModifyProcessorTest extends Specification {

    def processor = new OriginalOrderModifyProcessor()

    def reDispatchConfirmService = Mock(ReDispatchConfirmExeCmd)
    def createScheduleService = Mock(CreateScheduleExeCmd)
    def dispatchOrderService = Mock(QueryDspOrderService)
    def dispatchContextService = Mock(DspContextService)
    def subSkuRepository = Mock(SubSkuRepository)
    def dispatchOrderRepository = Mock(DspOrderRepository)
    def recommendService = Mock(RecommendService)
    def checkService = Mock(CheckService)
    def confirmDspOrderService = Mock(ConfirmDspOrderService)
    def driverOrderFactory = Mock(DriverOrderFactory)
    def driverOrderGateway = Mock(DriverOrderGateway)
    def queryDriverService = Mock(QueryDriverService)
    def queryVehicleService = Mock(QueryVehicleService)
    def queryTransportGroupService = Mock(QueryTransportGroupService)
    def messageProviderService = Mock(MessageProviderService)
    def scheduleRepository = Mock(ScheduleRepository)
    def businessTemplateInfoConfig = Mock(BusinessTemplateInfoConfig)

    def setup() {
        processor.reDispatchConfirmService = reDispatchConfirmService
        processor.createScheduleService = createScheduleService
        processor.dispatchOrderService = dispatchOrderService
        processor.dispatchContextService = dispatchContextService
        processor.subSkuRepository = subSkuRepository
        processor.dispatchOrderRepository = dispatchOrderRepository
        processor.recommendService = recommendService
        processor.checkService = checkService
        processor.confirmDspOrderService = confirmDspOrderService
        processor.driverOrderFactory = driverOrderFactory
        processor.driverOrderGateway = driverOrderGateway
        processor.queryDriverService = queryDriverService
        processor.queryVehicleService = queryVehicleService
        processor.queryTransportGroupService = queryTransportGroupService
        processor.messageProviderService = messageProviderService
        processor.scheduleRepository = scheduleRepository
        processor.businessTemplateInfoConfig = businessTemplateInfoConfig
    }

    def "doOldDriverTakenNewOrder"() {
        when: "执行doOldDriverTakenNewOrder方法"
        processor.doOldDriverTakenNewOrder("oldDispatchOrderId", "newDispatchOrderId")
        then: "验证抛出异常"
        thrown(IllegalStateException)
    }

    def "doApply"() {
        when: "执行doApply方法"
        processor.doApply("oldDispatchOrderId", "newDispatchOrderId", false)
        then: "验证结果"
        processor != null
    }

} 