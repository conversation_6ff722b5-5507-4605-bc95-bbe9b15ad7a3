package com.ctrip.dcs.application.service.risk

import com.ctrip.dcs.domain.common.service.QueryTransportGroupService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.dcs.domain.schedule.value.PerformanceConfigVO
import com.ctrip.igt.framework.common.exception.BizException
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/12/27 14:18
 */
class DriverLateRiskServiceTest extends Specification {
    def testObj = new DriverLateRiskService()
    def lateRiskTypeManager = Mock(LateRiskTypeManager)
    def driverOrderGateway = Mock(DriverOrderGateway)
    def queryTransportGroupService = Mock(QueryTransportGroupService)
    def selfOrderQueryGateway = Mock(SelfOrderQueryGateway)

    def setup() {
        testObj.selfOrderQueryGateway = selfOrderQueryGateway
        testObj.driverOrderGateway = driverOrderGateway
        testObj.lateRiskTypeManager = lateRiskTypeManager
        testObj.queryTransportGroupService = queryTransportGroupService
    }

    @Unroll
    def "test sendLateRiskNotice failed"() {
        given:
        lateRiskTypeManager.get(_) >> strategy;
        driverOrderGateway.queryPerformanceConfig(_) >> performanceConfig;
        queryTransportGroupService.queryTransportGroup(_) >> transportGroupVO;
        selfOrderQueryGateway.queryDspOrder(_) >> dspOrder;

        when:
        testObj.sendLateRiskNotice("313131", 1)

        then:
        def ex = thrown(BizException);
        Assert.assertTrue(ex != null)

        where:
        dspOrder                             | strategy                   | performanceConfig         | transportGroupVO || expectedResult
        null                                 | null                       | null                      | null             || null
        new DspOrderVO(transportGroupId: 1L) | null                       | null                      | null             || null
        new DspOrderVO(transportGroupId: 1L) | Mock(LateRiskTypeStrategy) | null                      | null             || null
        new DspOrderVO(transportGroupId: 1L) | Mock(LateRiskTypeStrategy) | Mock(PerformanceConfigVO) | null             || null
    }

    @Unroll
    def "test sendLateRiskNotice success"() {
        given:
        lateRiskTypeManager.get(_) >> strategy;
        driverOrderGateway.queryPerformanceConfig(_) >> performanceConfig;
        queryTransportGroupService.queryTransportGroup(_) >> transportGroupVO;
        selfOrderQueryGateway.queryDspOrder(_,_) >> dspOrder;

        when:
        testObj.sendLateRiskNotice("313131", 1)

        then:
        Assert.assertTrue(Objects.nonNull(performanceConfig));

        where:
        dspOrder         | strategy                   | performanceConfig         | transportGroupVO || expectedResult
        new DspOrderVO() | Mock(LateRiskTypeStrategy) | Mock(PerformanceConfigVO) | Mock(TransportGroupVO) || null
        new DspOrderVO() | Mock(LateRiskTypeStrategy) | getPerformanceConfig() | Mock(TransportGroupVO)             || null
    }

    private static PerformanceConfigVO getPerformanceConfig() {
        PerformanceConfigVO performanceConfigVO = new PerformanceConfigVO();
        performanceConfigVO.setMobileSupplierRemind(Boolean.TRUE);
        performanceConfigVO.setOpenEmailRemind(Boolean.TRUE);
        performanceConfigVO.setOpenIvr(Boolean.TRUE);
        performanceConfigVO.setPcStationNotice(Boolean.TRUE);
        return performanceConfigVO;
    }

}
