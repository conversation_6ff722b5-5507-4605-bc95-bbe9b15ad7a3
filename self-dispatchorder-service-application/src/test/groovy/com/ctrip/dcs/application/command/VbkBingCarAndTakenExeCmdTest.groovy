package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.OperateAssignDriverCommand
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.*
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

/**
 * <AUTHOR>
 */
class VbkBingCarAndTakenExeCmdTest extends Specification {

    def orderQueryService = Mock(QueryDspOrderService)
    def queryDriverService = Mock(QueryDriverService)
    def dspOrderConfirmRecordRepository = Mock(DspOrderConfirmRecordRepository)
    def checkService = Mock(CheckService)
    def driverOrderFactory = Mock(DriverOrderFactory)
    def confirmDspOrderService = Mock(ConfirmDspOrderService)
    def queryVehicleService = Mock(QueryVehicleService)
    def messageProducer = Mock(MessageProviderService)
    def manualSubSkuConf = Mock(ManualSubSkuConf)
    def queryDspOrderService = Mock(QueryDspOrderService)

    def subSkuRepository = Mock(SubSkuRepository)
    def distributedLockService = Mock(DistributedLockService)
    def queryTransportGroupService = Mock(QueryTransportGroupService)
    def dspOrderRepository = Mock(DspOrderRepository)
    def driverOrderGateway = Mock(DriverOrderGateway)


    def executor = new VbkBindCarAndTakenExeCmd(
            orderQueryService: orderQueryService,
            queryDriverService: queryDriverService,
            dspOrderConfirmRecordRepository: dspOrderConfirmRecordRepository,
            checkService: checkService,
            driverOrderFactory: driverOrderFactory,
            confirmDspOrderService: confirmDspOrderService,
            queryVehicleService: queryVehicleService,
            messageProducer: messageProducer,
            manualSubSkuConf: manualSubSkuConf,

            queryDspOrderService: queryDspOrderService,
            subSkuRepository: subSkuRepository,
            distributedLockService: distributedLockService,
            queryTransportGroupService: queryTransportGroupService,
            dspOrderRepository: dspOrderRepository,
            driverOrderGateway: driverOrderGateway


    )

    @Unroll
    def "test execute"() {

        given: "Mock数据"
        queryDspOrderService.query(_) >> dspOrdervo



        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock


        queryDriverService.queryDriver(_ as Long,_ as ParentCategoryEnum, _ as Long) >> driverVO
        dspOrderRepository.find(_) >> dspOrderDO
        dspOrderConfirmRecordRepository.find(_) >> confirmRecord
        queryTransportGroupService.queryTransportGroup(_) >> transportGroupVO
        queryTransportGroupService.queryTransportGroups(_, _, _) >> [transportGroupVO]
        manualSubSkuConf.matchSubSku(_, _, _, _, _) >> 1
        subSkuRepository.find(_) >> subsku
        queryVehicleService.query(_,_) >> vehicle
        checkService.check(_) >> checkmode
        driverOrderFactory.create(_, _) >> driverOrder

        when: "执行校验方法"
        def order = executor.execute(req)

        then: "验证校验结果"
        order == res

        where:
        req      | lock | dspOrdervo      | driverVO      | dspOrderDO      | confirmRecord                   | transportGroupVO      | subsku      | vehicle        | checkmode       | driverOrder        || res
        getReq() | true | getDspOrderVO() | getDriverVO() | getDspOrderDO() | getDspOrderConfirmRecordVOEx1() | getTransportGroupVO() | getsubsku() | getVehicleVO() | getCheckModel() | getDriverOrderVO() || true


    }

    @Unroll
    def "test exception"() {

        given: "Mock数据"
        queryDspOrderService.query(_) >> dspOrdervo



        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock


        queryDriverService.queryDriver(_ as Long,_ as ParentCategoryEnum, _ as Long) >> driverVO
        dspOrderRepository.find(_) >> dspOrderDO
        dspOrderConfirmRecordRepository.find(_) >> confirmRecord
        queryTransportGroupService.queryTransportGroup(_) >> transportGroupVO
        queryTransportGroupService.queryTransportGroups(_, _, _) >> [transportGroupVO]
        manualSubSkuConf.matchSubSku(_, _, _, _, _) >> 1
        subSkuRepository.find(_) >> subsku
        queryVehicleService.query(_,_) >> vehicle
        checkService.check(_) >> checkmode
        driverOrderFactory.create(_, _) >> driverOrder

        when: "执行校验方法"
        def order = executor.execute(req)

        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == res

        where:
        req       | flowSwitch | lock  | dspOrdervo      | driverVO      | dspOrderDO         | confirmRecord                   | transportGroupVO         | subsku      | vehicle        | checkmode       | driverOrder        || res
        getReq()  | true       | false | getDspOrderVO() | _             | _                  | _                               | _                        | _           | _              | _               | _                  || "3"
        getReq()  | true       | true  | getDspOrderVO() | null          | getDspOrderDO()    | getDspOrderConfirmRecordVO()    | getTransportGroupVO()    | getsubsku() | getVehicleVO() | getCheckModel() | getDriverOrderVO() || "5002"
        getReq()  | true       | true  | getDspOrderVO() | getDriverVO() | getDspOrderDOEx1() | getDspOrderConfirmRecordVOEx4() | getTransportGroupVO()    | getsubsku() | getVehicleVO() | getCheckModel() | getDriverOrderVO() || "09011000"
        getReq2() | true       | true  | getDspOrderVO() | getDriverVO() | getDspOrderDOEx1() | getDspOrderConfirmRecordVO()    | getTransportGroupVO()    | getsubsku() | getVehicleVO() | getCheckModel() | getDriverOrderVO() || "09011000"

        getReq()  | true       | true  | getDspOrderVO() | getDriverVO() | getDspOrderDO()    | getDspOrderConfirmRecordVOEx2() | getTransportGroupVO()    | getsubsku() | getVehicleVO() | getCheckModel() | getDriverOrderVO() || "09011000"
        getReq1() | true       | true  | getDspOrderVO() | getDriverVO() | getDspOrderDOEx1() | getDspOrderConfirmRecordVOEx3() | getTransportGroupVO()    | getsubsku() | getVehicleVO() | getCheckModel() | getDriverOrderVO() || "09011000"
        getReq1() | true       | true  | getDspOrderVO() | getDriverVO() | getDspOrderDOEx1() | getDspOrderConfirmRecordVOEx1() | getTransportGroupVO()    | getsubsku() | getVehicleVO() | getCheckModel() | getDriverOrderVO() || "09011000"
        getReq()  | true       | true  | getDspOrderVO() | getDriverVO() | getDspOrderDO()    | getDspOrderConfirmRecordVOEx1() | null                     | getsubsku() | getVehicleVO() | getCheckModel() | getDriverOrderVO() || "10040"
        getReq()  | true       | true  | getDspOrderVO() | getDriverVO() | getDspOrderDO()    | getDspOrderConfirmRecordVOEx1() | getTransportGroupVOEx1() | getsubsku() | getVehicleVO() | getCheckModel() | getDriverOrderVO() || "10040"


    }

    OperateAssignDriverCommand getReq1() {
        def cmd = getReq()
        cmd.setCarLicense("")
        return cmd

    }

    OperateAssignDriverCommand getReq2() {
        def cmd = getReq()
        cmd.setDriverId(345278799L)
        return cmd

    }

    OperateAssignDriverCommand getReq() {
        def request = new OperateAssignDriverCommand(userOrderId: "***********",
                dspOrderId: "*****************",
                transportGroupId: 249,
                driverId: 3452787,
                duid: "****************-108984431463809169-208984431463809170-1-11-123-5-0-0-0",
                carLicense: "nb3322",
                carTypeId: 117,
                carDesc: "奔驰",
                carColor: "白色",
                sysUserAccount: "sysxxx",
                extraFee: BigDecimal.valueOf(22),
                operUserType: "sysxxx",
                operUserName: "sysxxx",
                supplierId: 1105877,
                tkeSource: "app",
                event: OrderStatusEvent.VBK_ASSIGN
        )
        return request

    }

    DriverOrderVO getDriverOrderVO() {
        def order = new DriverOrderVO("D*****************", "*****************", 3452787)
        return order

    }


    CheckModel getCheckModel() {
        def check = new CheckModel(checkCode: CheckCode.PASS)
        return check
    }

    CheckModel getCheckModelEx1() {
        def check = new CheckModel(checkCode: CheckCode.NULL)
        return check
    }

    VehicleVO getVehicleVO() {

        def vehicle = new VehicleVO(1l, "carLicense", 1l, "carColor", 1l, "carBrandName", 1l, "carTypeName", 1l, "carSeriesName", 0,1,1,"dsc",0)
        return vehicle
    }


    SubSkuVO getsubsku() {
        def subSku = SubSkuVO.builder().subSkuId(1).subSkuName("1").dspType(DspType.SYSTEM_ASSIGN).takenType(TakenType.ASSISTANT).retrySecond(30).build()
        return subSku
    }

    TransportGroupVO getTransportGroupVO() {
        def group = new TransportGroupVO(transportGroupId: 249L, transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN, transportGroupName: "运力组名称", informSwitch: 1, informEmail: "<EMAIL>", orderLimitConfigs: null,shortDisSwitch: 0)
        return group
    }

    TransportGroupVO getTransportGroupVOEx1() {
        def group = new TransportGroupVO(transportGroupId: 2491L, transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN, transportGroupName: "运力组名称", informSwitch: 1, informEmail: "<EMAIL>", orderLimitConfigs: null,shortDisSwitch: 0)
        return group
    }


    DspOrderConfirmRecordVO getDspOrderConfirmRecordVO() {
        def driverInfo = new DspOrderConfirmRecordVO.DriverRecord(driverId: 3452787, transportGroupId: 249L)
        def supplierInfo = new DspOrderConfirmRecordVO.SupplierRecord(1105877, "",1, "xxx", "en")

        def confirm = new DspOrderConfirmRecordVO(driverInfo: driverInfo, supplierInfo: supplierInfo, confirmType: "20")
        return confirm
    }

    DspOrderConfirmRecordVO getDspOrderConfirmRecordVOEx1() {
        def driverInfo = new DspOrderConfirmRecordVO.DriverRecord(driverId: 3452787, transportGroupId: 249L)
        def supplierInfo = new DspOrderConfirmRecordVO.SupplierRecord(1105877, "",1, "xxx", "en")

        def confirm = new DspOrderConfirmRecordVO(driverInfo: driverInfo, supplierInfo: supplierInfo, confirmType: "30")
        return confirm
    }

    DspOrderConfirmRecordVO getDspOrderConfirmRecordVOEx2() {
        def driverInfo = new DspOrderConfirmRecordVO.DriverRecord(driverId: 3452788, transportGroupId: 249L)
        def supplierInfo = new DspOrderConfirmRecordVO.SupplierRecord(7, "",1, "xxx", "en")

        def confirm = new DspOrderConfirmRecordVO(driverInfo: driverInfo, supplierInfo: supplierInfo, confirmType: "20")
        return confirm
    }

    DspOrderConfirmRecordVO getDspOrderConfirmRecordVOEx3() {
        def driverInfo = new DspOrderConfirmRecordVO.DriverRecord(driverId: 3452787, transportGroupId: 249L)
        def supplierInfo = new DspOrderConfirmRecordVO.SupplierRecord(1105877, "",1, "xxx", "en")

        def confirm = new DspOrderConfirmRecordVO(driverInfo: driverInfo, supplierInfo: supplierInfo, confirmType: "20")
        return confirm
    }

    DspOrderConfirmRecordVO getDspOrderConfirmRecordVOEx4() {
        def driverInfo = new DspOrderConfirmRecordVO.DriverRecord(driverId: 3452788, transportGroupId: 249L)
        def supplierInfo = new DspOrderConfirmRecordVO.SupplierRecord(1105877, "",1, "xxx", "en")
        def car = new DspOrderConfirmRecordVO.CarRecord(carLicense: "京xxxx")
        def confirm = new DspOrderConfirmRecordVO(driverInfo: driverInfo, supplierInfo: supplierInfo, confirmType: "20", carInfo: car)
        return confirm
    }


    DriverVO getDriverVO() {
        def supplier = new SupplierVO(1105877L, [1105877L])
        def car = new CarVO(carId: 1L, carLicense: "京xxxx", carColorId: 1L, carColor: "red", carBrandId: 1L, carBrandName: "carBrandName", carTypeId: 119L, carTypeName: "carTypeName", carSeriesId: 1L, carSeriesName: "carSeriesName", isEnergy: 0)

        def drv = new DriverVO(3452787, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [new TransportGroupVO(249, "transportGroupName", com.ctrip.dcs.domain.common.enums.TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1105877, 0, 0,"informPhone", "informEmail", null, null, null)], car, supplier, 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId",0, 0, 1l, null, YesOrNo.NO,false,1,1, "", "","","","",null)
        return drv
    }

    DriverVO getDriverVO1() {
        def supplier = new SupplierVO(1105877L, [1105877L])
        def car = new CarVO(carId: 1L, carLicense: "", carColorId: 1L, carColor: "red", carBrandId: 1L, carBrandName: "carBrandName", carTypeId: 119L, carTypeName: "carTypeName", carSeriesId: 1L, carSeriesName: "carSeriesName", isEnergy: 0)

        def drv = new DriverVO(3452787, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [new TransportGroupVO(249, "transportGroupName", com.ctrip.dcs.domain.common.enums.TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1105877, 0,0, "informPhone", "informEmail", null, null, null)], car, supplier, 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", 0,0, 1l, null, YesOrNo.NO,false,0,1, "", "","","","",null)
        return drv
    }


    DspOrderVO getDspOrderVO() {
        def order = new DspOrderVO(dspOrderId: "*****************", productType: 0, countryId: 1L, cityId: 1, categoryCode: CategoryCodeEnum.TO_AIRPORT, spId: 1, driverOrderId: "D123456", supplierId: 1L)
        return order
    }

    DspOrderVO getDspOrderVOEx1() {
        def order = new DspOrderVO(dspOrderId: "*****************", productType: 1, countryId: 1L, cityId: 1, categoryCode: CategoryCodeEnum.TO_AIRPORT, spId: 1)
        return order
    }

    DspOrderVO getDspOrderVOEx2() {
        def order = new DspOrderVO(dspOrderId: "*****************", productType: 0, countryId: 1L, cityId: 1, categoryCode: CategoryCodeEnum.DAY_RENTAL, spId: 1)
        return order
    }


    DspOrderDO getDspOrderDO() {
        def useDays = new UseDays(BigDecimal.valueOf(1))
        def dspOrderDO = new DspOrderDO(userOrderId: "***********", dspOrderId: "*****************", categoryCode: "airport_dropoff", cityId: 2, fromCityId: 2, toCityId: 2, vehicleGroupId: 117
                , estimatedUseTime: new Timestamp(System.currentTimeMillis()), estimatedUseTimeBj: new Timestamp(System.currentTimeMillis())
                , predicServiceStopTime: new Timestamp(System.currentTimeMillis()), predicServiceStopTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTime: new Timestamp(System.currentTimeMillis()), lastConfirmTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTimeBj: new Timestamp(System.currentTimeMillis()), lastConfirmTime: new Timestamp(System.currentTimeMillis())
                , useDays: useDays, estimatedKm: BigDecimal.valueOf(100), estimatedMin: BigDecimal.valueOf(200), supplierId: 1105877, spId: 1000, confirmRecordId: 123)

        return dspOrderDO
    }

    DspOrderDO getDspOrderDOEx1() {
        def useDays = new UseDays(BigDecimal.valueOf(1))
        def dspOrderDO = new DspOrderDO(userOrderId: "***********", dspOrderId: "*****************", categoryCode: "day_rental", cityId: 2, fromCityId: 2, toCityId: 2, vehicleGroupId: 117
                , estimatedUseTime: new Timestamp(System.currentTimeMillis()), estimatedUseTimeBj: new Timestamp(System.currentTimeMillis())
                , predicServiceStopTime: new Timestamp(System.currentTimeMillis()), predicServiceStopTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTime: new Timestamp(System.currentTimeMillis()), lastConfirmTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTimeBj: new Timestamp(System.currentTimeMillis()), lastConfirmTime: new Timestamp(System.currentTimeMillis())
                , useDays: useDays, estimatedKm: BigDecimal.valueOf(100), estimatedMin: BigDecimal.valueOf(200), supplierId: 1105877, spId: 1000, confirmRecordId: 123)

        return dspOrderDO
    }

}
