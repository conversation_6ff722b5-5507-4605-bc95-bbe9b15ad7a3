package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.CustomerCheckSingleDriverCommand
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp
import java.util.concurrent.ExecutorService

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class CustomerCheckSingleDriversCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    VbkDriverCheckListExeCmd vbkDriverCheckListExeCmd;

    @Mock
    QueryDspOrderService queryDspOrderService;

    @Mock
    QueryDriverService queryDriverService;

    @Mock
    ManualSubSkuConf manualSubSkuConf;

    @Mock
    CheckService checkService;

    @Mock
    SubSkuRepository subSkuRepository;

    @Mock
    ExecutorService simpleTaskThreadPool;

    @Mock
    Map<String, String> commonConf;

    @Mock
    DspOrderRepository dspOrderRepository;

    @Mock
    QueryDspOrderService orderQueryService;

    @Mock
    SubSkuVO subSkuVO;

    @InjectMocks
    CustomerCheckSingleDriverCmd customerCheckSingleDriverCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "execute  Test1"() {
        given:
        def cmd = new CustomerCheckSingleDriverCommand(userOrderId: "111", driverId: "10004")
        when(queryDspOrderService.query(anyString())).thenReturn(dspOrderVo)
        when(dspOrderRepository.queryValidDspOrders(anyString())).thenReturn(dspOrderDOS)
        when(commonConf.get(anyString())).thenReturn("NOT_ALLOWED_TO_BE_ASSIGNED")
        when(queryDriverService.queryDriver(anyLong(),any(ParentCategoryEnum.class), anyLong())).thenReturn(driver)
        when(vbkDriverCheckListExeCmd.getDriverId(any())).thenReturn(10005L)
        when(manualSubSkuConf.matchSubSku(any(), any(), any(), any(), any())).thenReturn(100)
        when(subSkuRepository.find(100)).thenReturn(subSkuVO)
        when(checkService.check(any() as DspCheckCommand)).thenReturn([new CheckModel(new DspModelVO(driver: driver, transportGroup: driver != null ? driver.getTransportGroups()[0] : null), CheckCode.PASS)])


        when:
        def resdto = customerCheckSingleDriverCmd.check(cmd)
        then:
        resdto != null == res

        where:
        flowSwitch | dspOrderDOS     | dspOrderVo       | driver        || res
        false      | null            | null             | null          || true
        true       | []              | null             | null          || true
        true       | [getdsporder()] | null             | null          || true
        true       | [getdsporder()] | getDspOrderVO()  | getDriverVO() || true
        true       | [getdsporder()] | getDspOrderVO1() | getDriverVO() || true
    }


    DspOrderDO getdsporder() {
        def useDays = new UseDays(BigDecimal.valueOf(1))
        def dspOrderDO = new DspOrderDO(userOrderId: "11", dspOrderId: "123", categoryCode: "airport_dropoff", cityId: 2, fromCityId: 2, toCityId: 2, vehicleGroupId: 117
                , estimatedUseTime: new Timestamp(System.currentTimeMillis()), estimatedUseTimeBj: new Timestamp(System.currentTimeMillis())
                , predicServiceStopTime: new Timestamp(System.currentTimeMillis()), predicServiceStopTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTime: new Timestamp(System.currentTimeMillis()), lastConfirmTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTimeBj: new Timestamp(System.currentTimeMillis()), lastConfirmTime: new Timestamp(System.currentTimeMillis())
                , useDays: useDays, estimatedKm: BigDecimal.valueOf(100), estimatedMin: BigDecimal.valueOf(200), supplierId: 10086, spId: 1000)

        return dspOrderDO
    }

    DspOrderVO getDspOrderVO() {
        def dspOrderVO = new DspOrderVO(userOrderId: "1111", productType: 1)
        return dspOrderVO
    }

    DspOrderVO getDspOrderVO1() {
        def dspOrderVO = new DspOrderVO(userOrderId: "1111", dspOrderId: "222", productType: 0, countryId: 1, categoryCode: CategoryCodeEnum.FROM_AIRPORT)
        return dspOrderVO
    }

    DriverVO getDriverVO() {
        def supplier = new SupplierVO(1105877L, [1105877L])
        def car = new CarVO(carId: 1L, carLicense: "京xxxx", carColorId: 1L, carColor: "red", carBrandId: 1L, carBrandName: "carBrandName", carTypeId: 119L, carTypeName: "carTypeName", carSeriesId: 1L, carSeriesName: "carSeriesName", isEnergy: 0)

        def drv = new DriverVO(10004, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [new TransportGroupVO(249, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1105877, 0,0, "informPhone", "informEmail", null, null, null)], car, supplier, 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId",1, 0, 1l, null, YesOrNo.NO, false,0,1, "", "","","","",null)
        return drv
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
