package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.command.SaaSUpdateDspOrderExeCmd
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateOrderRemarkRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateOrderRemarkResponseType
import com.ctrip.igt.framework.common.exception.BizException
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/8/4 17:36
 */
class UpdateOrderRemarkExecutorTest extends Specification {
    def testObj = new UpdateOrderRemarkExecutor()
    def saaSUpdateDspOrderExeCmd = Mock(SaaSUpdateDspOrderExeCmd)

    def setup() {

        testObj.saaSUpdateDspOrderExeCmd = saaSUpdateDspOrderExeCmd
    }

    @Unroll
    def "test execute"() {
        given:
        UpdateOrderRemarkRequestType requestType = new UpdateOrderRemarkRequestType();

        when:
        def result = testObj.execute(requestType)

        then:
        Objects.equals(result.getResponseResult().isSuccess(), Boolean.TRUE);
    }

    def "test execute is failed"() {
        given:
        saaSUpdateDspOrderExeCmd.updateOrderRemark(_) >> {throw  new BizException("")}
        UpdateOrderRemarkRequestType requestType = new UpdateOrderRemarkRequestType();

        when:
        def result = testObj.execute(requestType)

        then:
        Objects.equals(result.getResponseResult().isSuccess(),Boolean.FALSE)
    }

    def "test execute is failed2"() {
        given:
        saaSUpdateDspOrderExeCmd.updateOrderRemark(_) >> {throw  new Exception("")}
        UpdateOrderRemarkRequestType requestType = new UpdateOrderRemarkRequestType();

        when:
        def result = testObj.execute(requestType)

        then:
        Objects.equals(result.getResponseResult().isSuccess(),Boolean.FALSE)
    }


    @Unroll
    def "test validate is failed"() {
        given:
        UpdateOrderRemarkRequestType requestType = new UpdateOrderRemarkRequestType();
        requestType.setDspOrderId(dspOrderId);
        requestType.setVbkDriverSettlePrice(vbkDriverSettlePrice);
        requestType.setVbkDriverSettleCurrency(vbkDriverSettleCurrency);

        when:
        testObj.validate(Mock(AbstractValidator) as AbstractValidator<UpdateOrderRemarkRequestType>, requestType)

        then:
        def ex = thrown(BizException);
        Assert.assertTrue(ex!=null)

        where:
        dspOrderId   | vbkDriverSettlePrice | vbkDriverSettleCurrency || expectedResult
        null         | null                 | null                    || null
        "dspOrderId" | new BigDecimal(1)    | null                    || null
        "dspOrderId" | null                 | "CNY"                   || null
    }

    def "test validate is success"() {
        given:
        UpdateOrderRemarkRequestType requestType = new UpdateOrderRemarkRequestType();
        requestType.setDspOrderId(dspOrderId);
        requestType.setVbkDriverSettlePrice(vbkDriverSettlePrice);
        requestType.setVbkDriverSettleCurrency(vbkDriverSettleCurrency);

        when:
        testObj.validate(Mock(AbstractValidator) as AbstractValidator<UpdateOrderRemarkRequestType>, requestType)

        then:
        Assert.assertTrue(Objects.nonNull(requestType))

        where:
        dspOrderId   | vbkDriverSettlePrice | vbkDriverSettleCurrency || expectedResult
        "dspOrderId" | new BigDecimal(1)    | "CNY"                   || null
        "dspOrderId" | null                 | null                    || null
    }
}
