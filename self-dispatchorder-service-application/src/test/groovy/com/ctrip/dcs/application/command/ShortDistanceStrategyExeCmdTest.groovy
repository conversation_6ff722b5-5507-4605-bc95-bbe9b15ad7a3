package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.SaveShortDistanceStrategyCommand
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.dsporder.entity.ShortDistanceStrategyDO
import com.ctrip.dcs.domain.dsporder.entity.ShortDistanceStrategyDateDTO
import com.ctrip.dcs.domain.dsporder.repository.ShortDistanceStrategyRepository
import com.ctrip.dcs.domain.schedule.value.QueryShortDistanceStrategyCondition
import com.ctrip.dcs.infrastructure.adapter.qconfig.BaseConfigService
import com.ctrip.dcs.shopping.common.channelnumber.NChannelNumber
import com.ctrip.dcs.shopping.common.channelnumber.NChannelNumberRepository
import com.ctrip.dcs.vehicle.domain.repository.StandardVehicleModelRepository
import com.ctrip.dcs.vehicle.domain.value.StandardVehicleModel
import com.ctrip.igt.PaginatorDTO
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp
import java.time.LocalDate
import java.time.LocalDateTime

class ShortDistanceStrategyExeCmdTest extends Specification {
    def repository = Mock(ShortDistanceStrategyRepository)
    def commonConfig = Mock(BaseConfigService)
    def nChannelNumberRepository = Mock(NChannelNumberRepository)
    def standardVehicleModelRepository = Mock(StandardVehicleModelRepository)
    def strategyExeCmd = new ShortDistanceStrategyExeCmd(
            repository: repository,
            commonConfig: commonConfig,
            nChannelNumberRepository: nChannelNumberRepository,
            standardVehicleModelRepository: standardVehicleModelRepository
    )

    @Unroll
    def "checkShortDisOrder should return #expected when: cityId=#cityId, category=#categoryCode, vehicleGroup=#vehicleGroupId, channel=#channelNumber, km=#estimatedKm, date=#useTime"() {
        given:
        def strategyDO = new ShortDistanceStrategyDO(
                categoryCodeList: ["CAT1", "CAT2"],
                vehicleGroupIdList: [500L, 600L],
                channelIdList: channelConfigs,
                startDis: 0,
                endDis: 100,
                startEndDateBjList: [new ShortDistanceStrategyDateDTO(
                        startDate: LocalDate.parse("2025-04-01"),
                        endDate: LocalDate.parse("2025-04-30")
                )]
        )
        repository.find(_, _) >> (cityId == 1 ? [strategyDO] : [])
        standardVehicleModelRepository.findOne(_) >> StandardVehicleModel.builder().vehicleModelGroupId(actualVehicleGroup).build()
        nChannelNumberRepository.findOne(_) >> NChannelNumber.newBuilder().withPrimaryChannelId(101L).withSecondaryChannelId(201L).withTertiaryChannelId(301L).build()

        when:
        def result = strategyExeCmd.checkShortDisOrder(
                cityId, categoryCode, vehicleGroupId, channelNumber,
                new BigDecimal(estimatedKm), new Timestamp(useTime)
        )

        then:
        result == expected

        where:
        cityId | categoryCode | vehicleGroupId | actualVehicleGroup | channelNumber | estimatedKm | useTime       | channelConfigs  || expected
        // 正常匹配用例
        1      | "CAT1"       | 100L           | 500L               | 200L          | "50.0"      | 1744881244000 | ["101"]         || true
        1      | "CAT1"       | 100L           | 500L               | 200L          | "50.0"      | 1744881244000 | ["101-201"]     || true
        1      | "CAT1"       | 100L           | 500L               | 200L          | "50.0"      | 1744881244000 | ["101-201-301"] || true

        // 渠道不匹配用例
        1      | "CAT1"       | 100L           | 500L               | 200L          | "50.0"      | 1744881244000 | ["102"]         || false
        1      | "CAT1"       | 100L           | 500L               | 200L          | "50.0"      | 1744881244000 | ["101-202"]     || false

        // 其他失败用例
        2      | "CAT1"       | 100L           | 500L               | 200L          | "50.0"      | 1744881244000 | ["101"]         || false // 城市不匹配
        1      | "CAT3"       | 100L           | 500L               | 200L          | "50.0"      | 1744881244000 | ["101"]         || false // 品类不匹配
        1      | "CAT1"       | 200L           | 700L               | 200L          | "50.0"      | 1744881244000 | ["101"]         || false // 车组不匹配
        1      | "CAT1"       | 100L           | 500L               | 200L          | "150.0"     | 1744881244000 | ["101"]         || false // 距离超出
        1      | "CAT1"       | 100L           | 500L               | 200L          | "50.0"      | 1747756800000 | ["101"]         || false // 时间超出
    }

    @Unroll
    def "save should handle channel conflicts when: channelA=#channelA and channelB=#channelB"() {
        given:
        def existingStrategy = new ShortDistanceStrategyDO(
                id: 1L,
                categoryCodeList: ["CAT1"],
                vehicleGroupIdList: [500L],
                channelIdList: [channelA],
                state: 1
        )

        def newStrategy = new ShortDistanceStrategyDO(
                categoryCodeList: ["CAT1"],
                vehicleGroupIdList: [500L],
                channelIdList: [channelB],
                state: 1,
                cityId: 1L
        )

        repository.save(_) >> true

        when:
        def result = strategyExeCmd.save(new SaveShortDistanceStrategyCommand(newStrategy))

        then:
        1 * repository.find(1, 1) >> [existingStrategy]
        result.code == (shouldConflict ? ErrorCode.SAVE_SHORT_DISTANCE_STRATEGY_CONFLICT_ERROR.code : null)

        where:
        channelA      | channelB      || shouldConflict
        "101"         | "101"         || true
        "101"         | "101-201"     || true
        "101-201"     | "101-201-301" || true
        "101-201-301" | "101-201"     || true
        "101"         | "102"         || false
        "101-201"     | "101-202"     || false
        "101-201-301" | "101-201-302" || false
    }

    def "save should detect time overlap"() {
        given:
        def cmd = new SaveShortDistanceStrategyCommand(new ShortDistanceStrategyDO(
                startEndDateBjList: [
                        new ShortDistanceStrategyDateDTO(
                                startDate: LocalDate.parse("2025-01-01"),
                                endDate: LocalDate.parse("2025-01-10")
                        ),
                        new ShortDistanceStrategyDateDTO(
                                startDate: LocalDate.parse("2025-01-05"),
                                endDate: LocalDate.parse("2025-01-15")
                        )
                ]
        ))

        when:
        def result = strategyExeCmd.save(cmd)

        then:
        result.code == ErrorCode.SAVE_SHORT_DISTANCE_STRATEGY_START_END_STATE_HAVE_OVERLAP.code
    }

    def "save more than max size"() {
        given:
        def cmd = new SaveShortDistanceStrategyCommand(new ShortDistanceStrategyDO(
                cityId: 2,
                startEndDateBjList: [
                        new ShortDistanceStrategyDateDTO(
                                startDate: LocalDate.parse("2025-01-01"),
                                endDate: LocalDate.parse("2025-01-10")
                        )
                ]
        ))

        repository.countByCityId(_) >> 30

        when:
        def result = strategyExeCmd.save(cmd)

        then:
        result.code == ErrorCode.SAVE_SHORT_DISTANCE_STRATEGY_OVER_MAX_SIZE_ERROR.code
    }

    @Unroll
    def "test query with condition: #scenario"() {
        given: "准备测试数据和模拟仓库返回"
        def condition = new QueryShortDistanceStrategyCondition(
                categoryCodeList: categoryCodes,
                vehicleGroupIdList: vehicleGroups,
                channelIdList: channels,
                paginator: new PaginatorDTO(pageNo: page, pageSize: size)
        )

        def mockData = [
                new ShortDistanceStrategyDO(
                        categoryCodeList: ["A", "B"],
                        vehicleGroupIdList: [101L, 102L],
                        channelIdList: ["101", "102-5"],
                        state: 1,
                        createTime: LocalDateTime.now().minusDays(2)
                ),
                new ShortDistanceStrategyDO(
                        categoryCodeList: ["B", "C"],
                        vehicleGroupIdList: [102L, 103L],
                        channelIdList: ["102", "104"],
                        state: 0,
                        createTime: LocalDateTime.now().minusDays(1)
                )
        ]

        repository.find(condition) >> mockData

        when: "执行查询方法"
        def result = strategyExeCmd.query(condition)

        then: "验证过滤和分页结果"
        result.shortDistanceStrategyList.size() == expectedPageSize

        and: "验证排序顺序"
        result.shortDistanceStrategyList.state == expectedOrder

        where:
        scenario            | categoryCodes | vehicleGroups | channels         | page | size | expectedPageSize | expectedOrder
        "基础分页"          | null          | null          | null             | 1    | 10   | 2                | [1, 0]
        "品类过滤"          | ["B"]         | null          | null             | 1    | 5    | 2                | [1, 0]
        "车组过滤"          | null          | [102L]        | null             | 2    | 1    | 1                | [0]
        "渠道过滤"          | null          | null          | ["101", "102-5"] | 1    | 10   | 2                | [1, 0]
        "组合过滤+分页边界" | ["B"]         | [102L]        | ["102"]          | 3    | 1    | 1                | [0]
    }
}
