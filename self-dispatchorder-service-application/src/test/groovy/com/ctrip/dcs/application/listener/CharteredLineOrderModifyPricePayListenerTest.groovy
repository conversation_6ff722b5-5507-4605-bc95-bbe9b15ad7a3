package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.command.ModifyCharteredLineOrderExeCmd
import com.ctrip.platform.dal.dao.helper.JsonUtils
import com.google.common.collect.Maps
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*


class CharteredLineOrderModifyPricePayListenerTest extends Specification {
    def testObj = new CharteredLineOrderModifyPricePayListener()
    def modifyCharteredLineOrderExeCmd = Mock(ModifyCharteredLineOrderExeCmd)

    def setup() {

        testObj.modifyCharteredLineOrderExeCmd = modifyCharteredLineOrderExeCmd
    }


    @Unroll
    def "onMessageTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        modifyCharteredLineOrderExeCmd.modifyCharteredLineOrderPriceStatus(_, _) >> true

        when:
        def result = testObj.onMessage(message)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        message || expectedResult
        baseMessage()    || null
    }

    def baseMessage(){
        Map<String, Object> map = new HashMap<>()
        map.put("orderId", "11")
        map.put("eventtype","refund.confirmed")
        Map<String,Integer> contentmap = Maps.newHashMap()
        contentmap.put("scenesType",502)
        map.put("content",JsonUtils.toJson(contentmap))
        def msg = new BaseMessage(attrs: map)
    }

}

