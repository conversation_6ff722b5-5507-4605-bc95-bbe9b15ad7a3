package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.command.ModifyCharteredLineOrderExeCmd
import com.ctrip.dcs.self.dispatchorder.interfaces.AddressInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.AmountInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.ModifyCharteredLineOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.ModifyCharteredLineOrderResponseType
import org.junit.Assert
import spock.lang.*
import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*


class ModifyCharteredLineOrderExecutorTest extends Specification {
    def testObj = new ModifyCharteredLineOrderExecutor()
    def modifyCharteredLineOrderExeCmd = Mock(ModifyCharteredLineOrderExeCmd)

    def setup() {

        testObj.modifyCharteredLineOrderExeCmd = modifyCharteredLineOrderExeCmd
    }


    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        modifyCharteredLineOrderExeCmd.modifyCharteredLineOrder(_) >> modifyCharteredLineOrder


        when:
        def result = testObj.execute(requestType)

        then: "验证返回结果里属性值是否符合预期"

        result.getResponseResult().success == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                                                                                                                                                                                                                                                                                                                                                    |modifyCharteredLineOrder || expectedResult
            new ModifyCharteredLineOrderRequestType(userOrderId: "1", estimateUseTime: "2024-12-12 12:12:12", estimateUseTimeBJ: "2024-12-12 12:12:12", predicServiceStopTime: "predicServiceStopTime", predicServiceStopTimeBJ: "predicServiceStopTimeBJ", fromAddressInfo: new AddressInfo(), toAddressInfo: new AddressInfo(), uniqueKey: "uniqueKey", amountInfo: new AmountInfo()) | true || true
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", estimateUseTime: "2024-12-12 12:12:12", estimateUseTimeBJ: "2024-12-12 12:12:12", predicServiceStopTime: "predicServiceStopTime", predicServiceStopTimeBJ: "predicServiceStopTimeBJ", fromAddressInfo: new AddressInfo(), toAddressInfo: new AddressInfo(), uniqueKey: "uniqueKey", amountInfo: new AmountInfo()) | false || true
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", estimateUseTime: "2024-12-12 12:12:12", estimateUseTimeBJ: "2024-12-12 12:12:12", predicServiceStopTime: "predicServiceStopTime", predicServiceStopTimeBJ: "predicServiceStopTimeBJ", fromAddressInfo: new AddressInfo(), toAddressInfo: new AddressInfo(), uniqueKey: "uniqueKey", amountInfo: new AmountInfo()) | false || true
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", estimateUseTime: "2024-12-12 12:12:12", estimateUseTimeBJ: "2024-12-12 12:12:12", predicServiceStopTime: "predicServiceStopTime", predicServiceStopTimeBJ: "predicServiceStopTimeBJ", toAddressInfo: new AddressInfo(), uniqueKey: "uniqueKey", amountInfo: new AmountInfo()) | false || false
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", estimateUseTime: "2024-12-12 12:12:12", estimateUseTimeBJ: "2024-12-12 12:12:12", predicServiceStopTime: "predicServiceStopTime", predicServiceStopTimeBJ: "predicServiceStopTimeBJ", fromAddressInfo: new AddressInfo(),  uniqueKey: "uniqueKey", amountInfo: new AmountInfo()) | false || false
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", estimateUseTime: "2024-12-12 12:12:12", estimateUseTimeBJ: "2024-12-12 12:12:12", predicServiceStopTime: "predicServiceStopTime", predicServiceStopTimeBJ: "predicServiceStopTimeBJ", fromAddressInfo: new AddressInfo(), toAddressInfo: new AddressInfo(), amountInfo: new AmountInfo()) | false || false
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", estimateUseTime: "2024-12-12 12:12:12", estimateUseTimeBJ: "2024-12-12 12:12:12", predicServiceStopTime: "predicServiceStopTime", predicServiceStopTimeBJ: "predicServiceStopTimeBJ", fromAddressInfo: new AddressInfo(), toAddressInfo: new AddressInfo(), uniqueKey: "uniqueKey") | false || false
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", estimateUseTime: "2024-12-12 12:12:12", estimateUseTimeBJ: "2024-12-12 12:12:12", predicServiceStopTime: "predicServiceStopTime",  fromAddressInfo: new AddressInfo(), toAddressInfo: new AddressInfo(), uniqueKey: "uniqueKey", amountInfo: new AmountInfo()) | false || false
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", estimateUseTime: "2024-12-12 12:12:12", estimateUseTimeBJ: "2024-12-12 12:12:12",  predicServiceStopTimeBJ: "predicServiceStopTimeBJ", fromAddressInfo: new AddressInfo(), toAddressInfo: new AddressInfo(), uniqueKey: "uniqueKey", amountInfo: new AmountInfo()) | false || false
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", estimateUseTime: "2024-12-12 12:12:12",  predicServiceStopTime: "predicServiceStopTime", predicServiceStopTimeBJ: "predicServiceStopTimeBJ", fromAddressInfo: new AddressInfo(), toAddressInfo: new AddressInfo(), uniqueKey: "uniqueKey", amountInfo: new AmountInfo()) | false || false
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", estimateUseTimeBJ: "2024-12-12 12:12:12", predicServiceStopTime: "predicServiceStopTime", predicServiceStopTimeBJ: "predicServiceStopTimeBJ", fromAddressInfo: new AddressInfo(), toAddressInfo: new AddressInfo(), uniqueKey: "uniqueKey", amountInfo: new AmountInfo()) | false || false
        new ModifyCharteredLineOrderRequestType( estimateUseTime: "2024-12-12 12:12:12", estimateUseTimeBJ: "2024-12-12 12:12:12", predicServiceStopTime: "predicServiceStopTime", predicServiceStopTimeBJ: "predicServiceStopTimeBJ", fromAddressInfo: new AddressInfo(), toAddressInfo: new AddressInfo(), uniqueKey: "uniqueKey", amountInfo: new AmountInfo()) | false || false
    }


}

