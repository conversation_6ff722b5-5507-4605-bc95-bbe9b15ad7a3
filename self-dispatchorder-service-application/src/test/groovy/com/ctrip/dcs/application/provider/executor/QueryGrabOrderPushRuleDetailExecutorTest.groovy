package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.service.IGrabOrderPushRuleService
import com.ctrip.dcs.application.service.dto.GrabOrderPushRuleDTO
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleDetailRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleDetailResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.GrabOrderPushRuleSoaDTO
import com.ctrip.igt.CommonAllianceDTO
import com.ctrip.igt.CommonGPSDTO
import com.ctrip.igt.CommonMiniProgramDTO
import com.ctrip.igt.CommonUBTDTO
import com.ctrip.igt.RequestHeader
import com.ctrip.igt.ResponseResult
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.igt.framework.infrastructure.exception.ServiceValidationException
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class QueryGrabOrderPushRuleDetailExecutorTest extends Specification {
    @Mock
    IGrabOrderPushRuleService grabOrderPushRuleService
    @InjectMocks
    QueryGrabOrderPushRuleDetailExecutor queryGrabOrderPushRuleDetailExecutor

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    def "test execute"() {
        given:
        when(grabOrderPushRuleService.queryById(anyLong())).thenReturn(new GrabOrderPushRuleDTO(id: 1l, supplierId: 1l, cityId: 1l, categoryCode: "categoryCode", supplierName: "supplierName", vehicleGroupIdList: [1l], ruleType: 0, immediatePushTime: 0, fixedPushTime: "fixedPushTime", startBookTime: "startBookTime", endBookTime: "endBookTime", bookTime: "bookTime"))

        when:
        QueryGrabOrderPushRuleDetailResponseType result = queryGrabOrderPushRuleDetailExecutor.execute(new QueryGrabOrderPushRuleDetailRequestType(new RequestHeader("language", "host", "languageCode", "locale", "currency", 0, 0, 0, 0, "severFrom", "cid", new CommonUBTDTO(["abtest": "abtest"], "pageid", "pvid", "sid", "vid"), new CommonAllianceDTO("aid", "sid", "ouid", "mktinfo"), "uid", "ip", "ticket", new CommonGPSDTO("lat", "lng", 0, "cnm", "coord", "qcid"), "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmsToken", "osVersion", "did", "appid", new CommonMiniProgramDTO("openid", "unionid"), "username", "accountId", "accountType", "accountName", "source", "plateForm", "clientType", "platform", "miniType", "stId"), 1l))

        then:
        result.getResponseResult().getReturnCode() == "200"
        with(result.getRule()) {
            id == 1l
            supplierId == 1l
            cityId == 1l
            categoryCode == "categoryCode"
            supplierName == "supplierName"
            vehicleGroupIdList == [1l]
            ruleType == 0
            immediatePushTime == 0
            fixedPushTime == "fixedPushTime"
            startBookTime == "startBookTime"
            endBookTime == "endBookTime"
            bookTime == "bookTime"
        }
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme