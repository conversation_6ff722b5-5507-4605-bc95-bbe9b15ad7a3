package com.ctrip.dcs.application.listener.driver

import com.ctrip.dcs.application.event.IDriverAccountModifyEventHandler
import com.ctrip.dcs.application.event.IDriverCityModifyEventHandler
import com.ctrip.dcs.application.event.IDriverInfoModifyEventHandler
import com.ctrip.dcs.application.event.IDriverSupplierModifyEventHandler
import com.ctrip.dcs.application.event.IDriverVehicleInfoModifyEventHandler
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.igt.framework.common.jackson.JacksonSerializer
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DriverModifyListenerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    IDriverAccountModifyEventHandler driverAccountModifyEventHandler
    @Mock
    IDriverCityModifyEventHandler driverCityModifyEventHandler
    @Mock
    IDriverInfoModifyEventHandler driverInfoModifyEventHandler
    @Mock
    IDriverSupplierModifyEventHandler driverSupplierModifyEventHandler
    @Mock
    IDriverVehicleInfoModifyEventHandler driverVehicleInfoModifyEventHandler;

    @InjectMocks
    DriverModifyListener driverModifyListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test tag_driver_supplier_change"() {
        given:
        Message message = new BaseMessage()
        message.addTag("tag_driver_supplier_change")

        when:
        driverModifyListener.handle(message)

        message.setProperty("drvId", "0")
        driverModifyListener.handle(message)

        message.setProperty("drvId", "1")
        driverModifyListener.handle(message)

        message.setProperty("drvId", "1")
        message.setProperty("supplierId", "0")
        driverModifyListener.handle(message)

        message.setProperty("drvId", "1")
        message.setProperty("supplierId", "1")
        driverModifyListener.handle(message)

        then:
        message.getTags().size() == 1
    }

    def "test tag_driver_city_change"() {
        given:
        Message message = new BaseMessage()
        message.addTag("tag_driver_city_change")

        when:
        driverModifyListener.handle(message)

        message.setProperty("drvId", "0")
        driverModifyListener.handle(message)

        message.setProperty("drvId", "1")
        driverModifyListener.handle(message)

        message.setProperty("drvId", "1")
        message.setProperty("cityId", "0")
        driverModifyListener.handle(message)

        message.setProperty("drvId", "1")
        message.setProperty("cityId", "1")
        driverModifyListener.handle(message)

        then:
        message.getTags().size() == 1
    }

    def "test tag_driverinfo_modify"() {
        given:
        Message message = new BaseMessage()
        message.addTag("tag_driverinfo_modify")

        when:
        driverModifyListener.handle(message)

        message.setProperty("drvId", "0")
        driverModifyListener.handle(message)

        message.setProperty("drvId", "1")
        driverModifyListener.handle(message)

        then:
        message.getTags().size() == 1
    }

    def "test handleTourDriverPlatformQmq driver0"() {
        given:
        Message message = new BaseMessage()
        message.addTag(tag)
        message.setProperty("driverId", driverId)
        message.setProperty("supplierId", supplierId)
        message.setProperty("newGeoId", newGeoId)
        message.setProperty("vehicleId", vehicleId)
        message.setProperty("driverProductLineList", driverProductLineList)
        when(driverSupplierModifyEventHandler.handle(any())).thenThrow(new BizException("tag_driver_supplier_change"))

        when:
        def result = ""
        try {
            driverModifyListener.handleTourDriverPlatformQmq(message)
        } catch (Exception e) {
            result = e.getMessage()
        }

        then:
        result == expected

        where:
        tag                          || driverId || supplierId || newGeoId || vehicleId || driverProductLineList || expected
        "tag_driver_supplier_change" || 0L       || 1L         || 1L       || 1L        || ""                    || ""
    }

    def "test handleTourDriverPlatformQmq supplier0"() {
        given:
        Message message = new BaseMessage()
        message.addTag(tag)
        message.setProperty("driverId", driverId)
        message.setProperty("supplierId", supplierId)
        message.setProperty("newGeoId", newGeoId)
        message.setProperty("vehicleId", vehicleId)
        message.setProperty("driverProductLineList", driverProductLineList)
        when(driverSupplierModifyEventHandler.handle(any())).thenThrow(new BizException("tag_driver_supplier_change"))

        when:
        def result = ""
        try {
            driverModifyListener.handleTourDriverPlatformQmq(message)
        } catch (Exception e) {
            result = e.getMessage()
        }

        then:
        result == expected

        where:
        tag                          || driverId || supplierId || newGeoId || vehicleId || driverProductLineList || expected
        "tag_driver_supplier_change" || 1L       || 0L         || 1L       || 1L        || ""                    || ""
    }

    def "test handleTourDriverPlatformQmq tag_driver_supplier_change ok"() {
        given:
        Message message = new BaseMessage()
        message.addTag(tag)
        message.setProperty("driverId", driverId)
        message.setProperty("supplierId", supplierId)
        message.setProperty("newGeoId", newGeoId)
        message.setProperty("vehicleId", vehicleId)
        message.setProperty("driverProductLineList", driverProductLineList)
        when(driverSupplierModifyEventHandler.handle(any())).thenThrow(new BizException("tag_driver_supplier_change"))
        when(driverCityModifyEventHandler.handle(any())).thenThrow(new BizException("tag_driver_geoid_change"))
        when(driverInfoModifyEventHandler.handle(any())).thenThrow(new BizException("tag_driver_info_change"))
        when(driverVehicleInfoModifyEventHandler.handle(any())).thenThrow(new BizException("tag_driver_vehicle_change"))

        when:
        def result = ""
        try {
            driverModifyListener.handleTourDriverPlatformQmq(message)
        } catch (Exception e) {
            result = e.getMessage()
        }

        then:
        //1 * verify(driverSupplierModifyEventHandler).handle(new DriverSupplierModifyEvent(driverId: driverId, supplierId: supplierId, accountType: "2", fromDrvGuide: true))
        //verify(driverSupplierModifyEventHandler, times(1)).handle(any())
        result == expected

        where:
        tag                          || driverId || supplierId || newGeoId || vehicleId || driverProductLineList || expected
        "tag_driver_supplier_change" || 1L       || 10L        || 0L       || 0L        || "V,G"                 || "tag_driver_supplier_change"
        "tag_driver_geoid_change"    || 1L       || 10L        || 1L       || 0L        || "V,G"                 || "tag_driver_geoid_change"
        "tag_driver_info_change"     || 1L       || 10L        || 1L       || 0L        || "V,G"                 || "tag_driver_info_change"
        "tag_driver_vehicle_change"  || 1L       || 10L        || 1L       || 1L        || "V,G"                 || "tag_driver_vehicle_change"
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme