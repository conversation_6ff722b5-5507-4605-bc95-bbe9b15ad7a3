package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.OperateDriverCheckListCommand
import com.ctrip.dcs.application.command.dto.DriverCheckDTO
import com.ctrip.dcs.domain.common.enums.CarTypeLevelRelation
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.enums.ToTakenEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.DuidVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand
import com.ctrip.dcs.domain.schedule.gateway.CarTypeRelationGateway
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.value.CarTypeLevelRelationsVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import com.ctrip.dcs.infrastructure.adapter.carconfig.SubSkuEnforcePassConfig
import com.ctrip.dcs.infrastructure.adapter.soa.SelfOrderQueryServiceProxy
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.dcs.self.order.query.api.QueryOrderDetailResponseType
import com.ctrip.dcs.self.order.query.dto.BaseDetail
import com.ctrip.dcs.self.order.query.dto.OrderDetail
import com.ctrip.igt.framework.common.clogging.Logger
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.junit.Assert
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import java.util.concurrent.Callable
import java.util.concurrent.CountDownLatch
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.Future
import java.util.concurrent.TimeUnit

import static org.mockito.Mockito.*

class VbkDriverCheckListExeCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    QueryDriverService queryDriverService
    @Mock
    SelfOrderQueryServiceProxy selfOrderQueryServiceProxy
    @Mock
    QueryTransportGroupService queryTransportGroupService
    @Mock
    CarTypeRelationGateway carTypeRelationGateway
    @Mock
    CheckService checkService
    @Mock
    ManualSubSkuConf manualSubSkuConf
    @Mock
    SubSkuRepository subSkuRepository
    @Mock
    DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository
    @Mock
    ConfigService commonConfConfig
    @Mock
    SubSkuEnforcePassConfig subSkuEnforcePassConfig
    @Mock
    QueryDspOrderService orderQueryService
    @Mock
    DriverCheckDTO driverCheckDTO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModelVO
    @Mock
    protected ExecutorService executorService
    @InjectMocks
    VbkDriverCheckListExeCmd vbkDriverCheckListExeCmd
    @Mock
    ExecutorService driverCheckListThreadPool = Executors.newFixedThreadPool(2);

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "test execute"() {
        given:
        when(queryDspOrderService.query(anyString())).thenReturn(buildDspVO(isNull))
        def carVO = new CarVO()
        carVO.setCarId(1L)
        carVO.setCarTypeId(1L)
        def periodsVO = new PeriodsVO(Lists.newArrayList("1:00 ~ 2:00"))
        when(queryDriverService.queryDriversByCondition(anyInt(), anyString(), anyString(), any(ParentCategoryEnum.class), anyLong())).thenReturn([new DriverVO(1l, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [new TransportGroupVO(1l, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1l, 0,0, "informPhone", "informEmail", new TakeOrderLimitTimeMinuteVO(0), null, null)], carVO, null, 0, periodsVO, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", 1, 0, 1l, null, YesOrNo.NO, false, 0, 1, "", "","","","",null)])
        when(selfOrderQueryServiceProxy.queryOrderDetail(any())).thenReturn(null)
        when(selfOrderQueryServiceProxy.queryDrvTakenNum(any())).thenReturn(null)
        when(queryTransportGroupService.queryTransportGroups(anyInt(), anyInt(), anyInt())).thenReturn([new TransportGroupVO(1l, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1l, 0,0, "informPhone", "informEmail", new TakeOrderLimitTimeMinuteVO(0), null, null)]);
        when(carTypeRelationGateway.queryCarTypeLevelRelations(anyInt())).thenReturn(new CarTypeLevelRelationsVO([(1l): CarTypeLevelRelation.BELOW]))
        def driverVO = DriverVO.builder().driverId(1L).build()
        def dspOrderVO = DspOrderVO.builder().build()
        def transportGroupVO = new TransportGroupVO(123L, null, null, null, null, null, null, null, null,null, null, null, null, null, null)
        def dspModelVO = new DspModelVO(dspOrderVO, driverVO, transportGroupVO)
        when(checkService.check((DspCheckCommand) any())).thenReturn([new CheckModel(dspModelVO, CheckCode.PASS)])
        when(manualSubSkuConf.matchSubSku(anyInt(), anyInt(), anyString(), anyInt(), anyString())).thenReturn(0)
        when(subSkuRepository.find(anyInt())).thenReturn(new SubSkuVO(0, "subSkuName", DspType.SYSTEM_ASSIGN, TakenType.DEFAULT, null, null, 1l))
        when(commonConfConfig.getInteger(anyString(), anyInt())).thenReturn(0)

        when:
        def result = null == vbkDriverCheckListExeCmd.execute(new OperateDriverCheckListCommand(dspOrderId: "123", transportGroupId: 1, supplierId: 6206, driverName: "ctrip", driverPhone: "15812345678", available: 1, pageNo: 1, pageSize: 20))

        then:
        result == res

        where:
        isNull || res
        true   || false
        false  || false
    }


    def "test batchQueryDriverCountAsyncNEW" () {
        given:
        List<DriverVO> drvInfos = Lists.newArrayList();
        Boolean newProcess = Boolean.TRUE;
        CountDownLatch latch = new CountDownLatch(1);
        Map<String, Object> contextHolder = Maps.newHashMap();

        when:
        vbkDriverCheckListExeCmd.batchQueryDriverCountAsync(drvInfos, newProcess, "313213", latch, contextHolder);

        then:
        def result = contextHolder.get("driverCountMap");
        Assert.assertTrue(Objects.nonNull(result));
    }

    def "test checkDriversNEW" () {
        given:
        DspOrderVO dspOrder = new DspOrderVO();
        List<DriverVO> drvInfos = Lists.newArrayList();
        List<TransportGroupVO> transportGroups = Lists.newArrayList();
        DuidVO duid = Mock(DuidVO);
        duid.getSubSkuId() >> 1;
        CountDownLatch latch = new CountDownLatch(1);
        Map<String, Object> contextHolder = Maps.newHashMap();
        when(subSkuRepository.find(anyInt())).thenReturn(new SubSkuVO(0, "subSkuName", DspType.SYSTEM_ASSIGN, TakenType.DEFAULT, null, null, 1l))
        checkService.check(_) >> Lists.newArrayList();

        when:
        vbkDriverCheckListExeCmd.checkDrivers(dspOrder, drvInfos, transportGroups, duid, "2312312", latch, contextHolder);

        then:
        def result = contextHolder.get("checkModels");
        Assert.assertTrue(Objects.nonNull(result));
    }


    def "test buildContext waitTimeout"() {
        given:
        OperateDriverCheckListCommand command = new OperateDriverCheckListCommand();
        DspOrderVO dspOrder = new DspOrderVO();
        List<DriverVO> drvInfos = Lists.newArrayList();
        List<TransportGroupVO> transportGroups = Lists.newArrayList();
        DuidVO duid = new DuidVO();
        CountDownLatch latch = Mock(CountDownLatch);
        latch.await(_,_) >> {throw new RuntimeException("131")}

        when:
        Map<String, Object> map = vbkDriverCheckListExeCmd.buildContext(command, dspOrder, drvInfos, transportGroups, duid, latch);

        then:
        Assert.assertTrue(Objects.nonNull(map));
    }

    def "test buildContext"() {
        given:
        OperateDriverCheckListCommand command = new OperateDriverCheckListCommand();
        DspOrderVO dspOrder = new DspOrderVO();
        List<DriverVO> drvInfos = Lists.newArrayList();
        List<TransportGroupVO> transportGroups = Lists.newArrayList();
        DuidVO duid = new DuidVO();
        CountDownLatch latch = Mock(CountDownLatch);

        when:
        Map<String, Object> map = vbkDriverCheckListExeCmd.buildContext(command, dspOrder, drvInfos, transportGroups, duid, latch);

        then:
        Assert.assertTrue(Objects.nonNull(map));
    }

    @Unroll
    def "test executeNewProcess"() {
        given:
        def queryDsp = DspOrderVO.builder()
                .dspOrderId("123")
                .countryId(1L)
                .cityId(1)
                .categoryCode(CategoryCodeEnum.TO_STATION).orderSourceCode(orderSourceCode)
                .build()
        when(queryDspOrderService.query(anyString())).thenReturn(queryDsp)
        def carVO = new CarVO()
        carVO.setCarId(1L)
        carVO.setCarTypeId(1L)
        def periodsVO = new PeriodsVO(Lists.newArrayList("1:00 ~ 2:00"))
        when(queryDriverService.queryDriversByCondition(anyInt(), anyString(), anyString(), any(ParentCategoryEnum.class), anyLong())).thenReturn(driverList)
        when(selfOrderQueryServiceProxy.queryOrderDetail(any())).thenReturn(null)
        when(selfOrderQueryServiceProxy.queryDrvTakenNum(any())).thenReturn(null)
        when(queryTransportGroupService.queryTransportGroups(any(DspOrderVO), anyInt(), anyInt())).thenReturn([new TransportGroupVO(1l, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1l, 0, 0,"informPhone", "informEmail", new TakeOrderLimitTimeMinuteVO(0), null, null)]);
        when(carTypeRelationGateway.queryCarTypeLevelRelations(anyInt())).thenReturn(new CarTypeLevelRelationsVO([(1l): CarTypeLevelRelation.BELOW]))
        when(queryDriverService.queryDriversForVbk(any())).thenReturn(driverList)
        def driverVO = DriverVO.builder().driverId(1L).build()
        def dspOrderVO = DspOrderVO.builder().orderSourceCode(OrderSourceCodeEnum.TRIP.code).build()
        def transportGroupVO = new TransportGroupVO(123L, null, null, null, null, null, null, null,null, null, null, null, null, null, null)
        def dspModelVO = new DspModelVO(dspOrderVO, driverVO, transportGroupVO)
        when(checkService.check((DspCheckCommand) any())).thenReturn([new CheckModel(dspModelVO, CheckCode.PASS)])
        when(manualSubSkuConf.matchSubSku(anyInt(), anyInt(), anyString(), anyInt(), anyString())).thenReturn(0)
        when(subSkuRepository.find(anyInt())).thenReturn(new SubSkuVO(0, "subSkuName", DspType.SYSTEM_ASSIGN, TakenType.DEFAULT, null, null, 1l))
        when(commonConfConfig.getInteger(anyString(), anyInt())).thenReturn(0)

        when:
        def result = null == vbkDriverCheckListExeCmd.execute(new OperateDriverCheckListCommand(dspOrderId: "123", transportGroupId: 1, supplierId: 6206, driverName: "ctrip", driverPhone: "15812345678", available: 1, pageNo: 1, pageSize: 20, newProcess: 1))

        then:
        result == res

        where:
        orderSourceCode | driverList      || res
        1               | getDriverList() || false
        2               | getDriverList() || false
        2               | []              || false
    }

    def getDriverList() {
        def carVO = new CarVO()
        carVO.setCarId(1L)
        carVO.setCarTypeId(1L)
        def periodsVO = new PeriodsVO(Lists.newArrayList("1:00 ~ 2:00"))
        return [new DriverVO(1l, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [new TransportGroupVO(1l, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1l, 0, 0,"informPhone", "informEmail", new TakeOrderLimitTimeMinuteVO(0), null, null)], carVO, null, 0, periodsVO, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", 1, 0, 1l, null, YesOrNo.NO, false, 0, 1, "", "","","","",null)]

    }


    @Unroll
    def "test getDriverId"() {
        given:
        when(dspOrderVO.getDriverOrderId()).thenReturn(driverOrderId)
        when(selfOrderQueryServiceProxy.queryOrderDetail(any())).thenReturn(response)

        when:
        def result = vbkDriverCheckListExeCmd.getDriverId(dspOrderVO)

        then:
        result == res

        where:
        driverOrderId | response                                                                                              || res
        null          | null                                                                                                  || 0L
        "123"         | null                                                                                                  || 0L
        "123"         | new QueryOrderDetailResponseType(orderDetail: new OrderDetail(baseDetail: new BaseDetail(drvId: 1L))) || 1L
    }

    @Unroll
    def "test convrtToTaken"() {
        given:

        when(checkModel.getModel()).thenReturn(dspModelVO)
        when(checkModel.getCheckCode()).thenReturn(checkCode)
        when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        when(dspOrderVO.getEstimatedUseTimeBj()).thenReturn(DateUtil.addMinutes(new Date(), estimatedUseTimeBj))
        when(commonConfConfig.getInteger(any(), any())).thenReturn(timeDiff)
//        when(subSkuEnforcePassConfig.get([new SubSkuEnforcePassConfig.Key(anyInt())])).thenReturn(enforcePass)
        when(subSkuEnforcePassConfig.get([any()])).thenReturn(enforcePass)


        when:
        def result = vbkDriverCheckListExeCmd.convrtToTaken(checkModel, duid)

        then:
        result == res

        where:
        checkCode      | estimatedUseTimeBj | enforcePass | timeDiff | duid | res
        CheckCode.PASS | 10                 | getList(1)  | 10       | null | ToTakenEnum.PASS.getCode()
//        CheckCode.PASS | 10                 | getList(1)       | 10       | new DuidVO() | ToTakenEnum.PASS.getCode()
//        CheckCode.NULL | 10                 | getList(1)       | 10       | new DuidVO() | ToTakenEnum.FORCED_ASSIGNMENT.getCode()
//        CheckCode.NULL | 10                 | getList(2)        | 20       | new DuidVO() | ToTakenEnum.NO_PASS.getCode()
//        CheckCode.NULL | 10                 | getList(2)        | 20       | null | ToTakenEnum.PASS.getCode()
    }

    @Unroll
    def "test queryCarLevelRelation"() {
        given:
        when(carTypeRelationGateway.queryCarTypeLevelRelations(any())).thenReturn(relations)

        when:
        def map = vbkDriverCheckListExeCmd.queryCarLevelRelation(orderCarId)

        then:
        map.isEmpty() == result

        where:
        orderCarId | relations                                                      || result
        1          | null                                                           || true
        1          | new CarTypeLevelRelationsVO([1L: CarTypeLevelRelation.HIGHER]) || false
    }

    @Unroll
    def "test filterAndSort"() {
        given:

        when:
        def map = vbkDriverCheckListExeCmd.filterAndSort(list, new DspOrderVO(carTypeId: 117), available)

        then:
        map.get(0).getDriverId() == result

        where:
        list                                                                                                                                                                                                    | available || result
        [new DriverCheckDTO(driverId: 12, checkCode: 0, orderCounts: 1, driveLevel: 101), new DriverCheckDTO(driverId: 13, checkCode: 0, orderCounts: 1, driveLevel: 102)]                                      | 1         || 13
        [new DriverCheckDTO(driverId: 12, checkCode: 0, orderCounts: 1, driveLevel: null, driverName: 123), new DriverCheckDTO(driverId: 13, checkCode: 0, orderCounts: 1, driveLevel: 102, driverName: 234)]   | 1         || 13
        [new DriverCheckDTO(driverId: 12, checkCode: 0, orderCounts: 1, driveLevel: 101, driverName: 123), new DriverCheckDTO(driverId: 13, checkCode: 0, orderCounts: 1, driveLevel: null, driverName: 234)]   | 1         || 12
        [new DriverCheckDTO(driverId: 12, checkCode: -1, orderCounts: 1, driveLevel: 101, driverName: 123), new DriverCheckDTO(driverId: 13, checkCode: -1, orderCounts: 1, driveLevel: 102, driverName: 234)]  | 0         || 13
        [new DriverCheckDTO(driverId: 12, checkCode: -1, orderCounts: 1, driveLevel: 101, driverName: 123), new DriverCheckDTO(driverId: 13, checkCode: -1, orderCounts: 1, driveLevel: null, driverName: 234)] | 0         || 12
    }

    def "test batchQueryDriverCountAsync"() {
        given:
        DriverVO driver = mock(DriverVO)
        List<DriverVO> drivers = [driver]
        Boolean newProcess = true
        Future<Map<Integer, Integer>> future = mock(Future)
        when(executorService.submit(new Callable<Map<Integer, Integer>>() {
            @Override
            Map<Integer, Integer> call() throws Exception {
                return null
            }
        })).thenReturn(future)
        when(future.get(2000, TimeUnit.MILLISECONDS)).thenReturn([1: 5])

        when:
        Map<Integer, Integer> result = vbkDriverCheckListExeCmd.batchQueryDriverCountAsync(drivers, newProcess)

        then:
        result == [:]
    }

    List<SubSkuEnforcePassConfig.Value> getList(type1) {
        List<SubSkuEnforcePassConfig.Value> list = Lists.newArrayList()
        if (type1 == 1) {
            list.add(new SubSkuEnforcePassConfig.Value("1"))
            list.add(new SubSkuEnforcePassConfig.Value("802"))
        }
        if (type1 == 2) {
            list.add(new SubSkuEnforcePassConfig.Value("0"))
            list.add(new SubSkuEnforcePassConfig.Value("804"))
        }
        return list
    }

    DspOrderVO buildDspVO(isNull) {
        if (isNull) {
            return null
        }
        def dspOrderVO = DspOrderVO.builder()
                .dspOrderId("123")
                .countryId(1L)
                .cityId(1)
                .categoryCode(CategoryCodeEnum.TO_STATION).orderSourceCode(1).skuId(1)
                .build()
        return dspOrderVO
    }

}
