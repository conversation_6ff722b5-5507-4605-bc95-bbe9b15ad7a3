package com.ctrip.dcs.application.listener.grab

import com.ctrip.dcs.application.command.ShutdownScheduleExeCmd
import com.ctrip.dcs.domain.common.enums.ScheduleStatus
import com.ctrip.dcs.domain.common.enums.ScheduleType
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import com.ctrip.igt.framework.common.clogging.Logger
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ShutdownGrabScheduleTaskListenerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    ShutdownScheduleExeCmd shutdownScheduleExeCmd
    @Mock
    ScheduleRepository scheduleRepository
    @Mock
    GrabCentreRepository grabCentreRepository
    @Mock
    ScheduleDO schedule
    @Mock
    BaseMessage message
    @Mock
    GrabOrderDetailRepository grabOrderDetailRepository
    @InjectMocks
    ShutdownGrabScheduleTaskListener shutdownGrabScheduleTaskListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message"() {
        given:
        when(scheduleRepository.query(anyString())).thenReturn([schedule])
        when(message.times()).thenReturn(times)
        when(message.getStringProperty("orderIdStr")).thenReturn(orderIds)

        when:
        def result = shutdownGrabScheduleTaskListener.onMessage(message)

        then:
        result == code

        where:
        times | orderIds  || code
        21    | ""        || null
        1     | ""        || null
        1     | "123,456" || null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme