package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.UpdateOrderSettleToDriverBeforeTakenCommand
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryOrderSettlePriceService
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDetailDO
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class UpdateOrderSettleToDriverBeforeTakenCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    QueryOrderSettlePriceService queryOrderSettlePriceService
    @Mock
    DspOrderDetailRepository dspOrderDetailRepository
    @Mock
    Map<String, String> commonConf
    @Mock
    MessageProviderService messageProducer
    @Mock
    UpdateOrderSettlementBeforeTakenCmd cmd;
    @InjectMocks
    UpdateOrderSettleToDriverBeforeTakenCmd updateOrderSettleToDriverBeforeTakenCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(queryOrderSettlePriceService.isPayForDriver(anyLong(), any(), anyInt(),anyInt())).thenReturn(Boolean.TRUE)
        when(dspOrderDetailRepository.find(anyString())).thenReturn(new DspOrderDetailDO())

        when:
        def result = updateOrderSettleToDriverBeforeTakenCmd.execute(new UpdateOrderSettleToDriverBeforeTakenCommand("userOrderId", "dspOrderId", 0, "airport_pickup", 1l, 0, 1, 5, 0, 1))

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme