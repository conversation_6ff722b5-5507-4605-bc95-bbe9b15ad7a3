package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.command.CreateScheduleExeCmd
import com.ctrip.dcs.application.command.ResetScheduleTaskExeCmd
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.ScheduleType
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ExecuteVBKDriverGrabTaskListenerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CreateScheduleExeCmd createScheduleExeCmd
    @Mock
    BaseMessage message
    @Mock
    ScheduleRepository scheduleRepository;
    @Mock
    ScheduleDO schedule;
    @Mock
    ResetScheduleTaskExeCmd resetScheduleTaskExeCmd;
    @InjectMocks
    ExecuteVBKDriverGrabTaskListener executeVBKDriverGrabTaskListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message Exception"() {
        given:
        when(message.getStringProperty("dspOrderId")).thenReturn("dspOrderId")
        when(createScheduleExeCmd.execute(any())).thenThrow(new RuntimeException("test"))
        when:
        executeVBKDriverGrabTaskListener.onMessage(message)

        then:
        BizException e = thrown(BizException)
        e.getCode() == ErrorCode.SCHEDULE_CREATE_ERROR.getCode()
    }

    def "test on Message"() {
        given:
        when(message.getStringProperty("dspOrderId")).thenReturn("dspOrderId")
        when(scheduleRepository.query("dspOrderId")).thenReturn([schedule])
        when(scheduleRepository.query("dspOrderId")).thenReturn([schedule])
        when(schedule.isShutdown()).thenReturn(shutdonw)
        when(schedule.getType()).thenReturn(type)
        when(schedule.getScheduleId()).thenReturn(1L)

        when:
        def result = executeVBKDriverGrabTaskListener.onMessage(message)

        then:
        result == null

        where:
        shutdonw | type                || code
        true     | ScheduleType.SYSTEM || null
        false    | ScheduleType.SYSTEM || null
        false    | ScheduleType.VBK    || null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme