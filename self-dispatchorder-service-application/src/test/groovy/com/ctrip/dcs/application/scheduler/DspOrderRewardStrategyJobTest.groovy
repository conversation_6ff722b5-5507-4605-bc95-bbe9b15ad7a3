package com.ctrip.dcs.application.scheduler

import com.ctrip.dcs.application.service.DspOrderRewardStrategyService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.schedule.entity.DspOrderRewardStrategyDO
import com.ctrip.dcs.domain.schedule.repository.DspOrderRewardStrategyRepository
import qunar.tc.schedule.MockParameter
import qunar.tc.schedule.Parameter
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DspOrderRewardStrategyJobTest extends Specification {

    private DspOrderRewardStrategyRepository dspOrderRewardStrategyRepository = Mock(DspOrderRewardStrategyRepository)

    private MessageProviderService messageProviderService = Mock(MessageProviderService)

    private DspOrderRewardStrategyService dspOrderRewardStrategyService = Mock(DspOrderRewardStrategyService)

    DspOrderRewardStrategyJob dspOrderRewardStrategyJob = new DspOrderRewardStrategyJob(dspOrderRewardStrategyRepository: dspOrderRewardStrategyRepository, dspOrderRewardStrategyService: dspOrderRewardStrategyService)

    def "test execute with empty strategies"() {
        given:
        dspOrderRewardStrategyRepository.queryByRewardTime(_, _) >> []

        when:
        def result = dspOrderRewardStrategyJob.execute(new MockParameter())

        then:
        result == null
    }

    def "test execute with non-empty strategies"() {
        given:
        DspOrderRewardStrategyDO strategy = new DspOrderRewardStrategyDO()
        dspOrderRewardStrategyRepository.queryByRewardTime(_, _) >> [strategy]

        when:
        def result = dspOrderRewardStrategyJob.execute(new MockParameter())

        then:
        result == null
    }
}
