package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.command.dispatchergrab.DispatcherGrabOrderIvrExeCmd
import com.ctrip.igt.framework.common.clogging.Logger
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class CreateDispatchGrabOrderIvrListenerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DispatcherGrabOrderIvrExeCmd dispatcherGrabOrderIvrExeCmd
    @InjectMocks
    CreateDispatchGrabOrderIvrListener createDispatchGrabOrderIvrListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message"() {
        when:
        Message message = new BaseMessage();
        message.setProperty("dispatcherGrabOrderId",1L);
        createDispatchGrabOrderIvrListener.onMessage(message)

        then:
        message != null//todo - validate something
    }
}

//Generated with love by <PERSON>Me :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme