package com.ctrip.dcs.application.scheduler

import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.driver.domain.account.DriverUDL
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig
import com.ctrip.dcs.infrastructure.common.config.SysSwitchConfig
import com.ctrip.dcs.infrastructure.service.QueryDriverServiceImpl
import com.ctrip.igt.framework.common.clogging.Logger
import org.junit.Assert
import qunar.tc.schedule.MockParameter
import qunar.tc.schedule.Parameter
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2025/6/11 07:29
 */
class SyncDrvUdlAndUidJobTest extends Specification {
    def testObj = new SyncDrvUdlAndUidJob()
    def logger = Mock(Logger)
    def dspOrderRepository = Mock(DspOrderRepository)
    def dspOrderConfirmRecordRepository = Mock(DspOrderConfirmRecordRepository)
    def queryDriverService = Mock(QueryDriverServiceImpl)
    def sysSwitchConfig = Mock(SysSwitchConfig)
    def businessTemplateInfoConfig = Mock(BusinessTemplateInfoConfig)

    def setup() {
        testObj.queryDriverService = queryDriverService
        testObj.logger = logger
        testObj.sysSwitchConfig = sysSwitchConfig
        testObj.businessTemplateInfoConfig = businessTemplateInfoConfig
        testObj.dspOrderRepository = dspOrderRepository
        testObj.dspOrderConfirmRecordRepository = dspOrderConfirmRecordRepository
    }

    @Unroll
    def "test execute"() {
        dspOrderRepository.find(_, _) >> [new DspOrderDO(dspOrderId: "dspOrderId")]
        dspOrderConfirmRecordRepository.findByDspOrderId(_) >> [new DspOrderConfirmRecordVO(driverInfo: new DspOrderConfirmRecordVO.DriverRecord(driverId: 1L, driverUdl: "driverUdl"), id: 1L)]
        dspOrderConfirmRecordRepository.updateDriverUdlAndUid(_, _, _) >> true
        queryDriverService.getDrvUdls(_) >> [new DriverUDL(udl: "udl", uid: "uid")]
        sysSwitchConfig.getSyncDrvUdlAndUidSwitch() >> syncDrvUdlAndUidSwitch
        businessTemplateInfoConfig.getFlushSyncDrvUdlAndUidIntervalTime() >> 1L
        Parameter parameter = new MockParameter(jsonParam)

        when:
        testObj.execute(parameter)

        then:
        Assert.assertTrue(Objects.nonNull(parameter))

        where:
        jsonParam                                                                                 | syncDrvUdlAndUidSwitch || expectedResult
        "{\"startCreateTime\":\"\",\"endCreateTime\":\"\"}"                                       | false                  || null
        "{\"startCreateTime\":\"2025-06-10 00:00:00\",\"endCreateTime\":\"\"}"                    | false                  || null
        "{\"startCreateTime\":\"2025-06-10 00:00:00\",\"endCreateTime\":\"2025-06-10 02:00:00\"}" | false                  || null
        "{\"startCreateTime\":\"2025-06-10 00:00:00\",\"endCreateTime\":\"2025-06-10 02:00:00\"}" | true                   || null
    }

    @Unroll
    def "test syncDrvUdlAndUid"() {
        given:
        DspOrderConfirmRecordVO confirmRecord = new DspOrderConfirmRecordVO();
        confirmRecord.setDriverInfo(driverInfo)
        queryDriverService.getDrvUdls(_) >> drvUdls

        when:
        testObj.syncDrvUdlAndUid(confirmRecord)

        then:
        Assert.assertTrue(Objects.nonNull(confirmRecord))

        where:
        driverInfo                                                              | drvUdls                          || expect
        null                                                                    | []                               || null
        new DspOrderConfirmRecordVO.DriverRecord(driverUdl: "udl")              | []                               || null
        new DspOrderConfirmRecordVO.DriverRecord(driverUdl: "")                 | []                               || null
        new DspOrderConfirmRecordVO.DriverRecord(driverUdl: "", driverId: 0L)   | []                               || null
        new DspOrderConfirmRecordVO.DriverRecord(driverUdl: "", driverId: 123L) | []                               || null
        new DspOrderConfirmRecordVO.DriverRecord(driverUdl: "", driverId: 123L) | [new DriverUDL(driverid: "123")] || null
    }



}
