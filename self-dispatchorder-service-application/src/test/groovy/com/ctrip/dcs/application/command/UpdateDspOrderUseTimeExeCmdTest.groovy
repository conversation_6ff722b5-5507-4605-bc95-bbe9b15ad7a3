package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.UpdateDspOrderUseTimeCommand
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDetailDO
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderUseTimeVO
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class UpdateDspOrderUseTimeExeCmdTest extends Specification {

    private DspOrderRepository dspOrderRepository = Mock(DspOrderRepository);

    private DspOrderDetailRepository dspOrderDetailRepository = Mock(DspOrderDetailRepository);

    UpdateDspOrderUseTimeExeCmd cmd = new UpdateDspOrderUseTimeExeCmd(dspOrderRepository: dspOrderRepository, dspOrderDetailRepository: dspOrderDetailRepository)

    def "Execute"() {
        given:
        DspOrderUseTimeVO time = new DspOrderUseTimeVO("1", "", "", "", "", "", "", "", "", 1)
        dspOrderDetailRepository.find("1") >> new DspOrderDetailDO()
        when:
        def result = cmd.execute(new UpdateDspOrderUseTimeCommand([time]))
        then:
        result == null
    }
}
