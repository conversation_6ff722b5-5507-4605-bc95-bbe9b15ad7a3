package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.service.DispatcherConfirmedService
import com.ctrip.dcs.application.service.SpContractInfoService
import com.ctrip.igt.framework.common.exception.BizException
import qunar.tc.qmq.Message
import qunar.tc.qmq.NeedRetryException
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.never
import static org.mockito.Mockito.verify
import static org.powermock.api.mockito.PowerMockito.*

class DispatcherConfirmListenerTest extends Specification {
    def testObj = new DispatcherConfirmListener()
    def dispatcherConfirmedService = Mock(DispatcherConfirmedService)
    def spContractInfoService = Mock(SpContractInfoService)
    def message = Mock(Message)

    def setup() {

        testObj.dispatcherConfirmedService = dispatcherConfirmedService
        testObj.spContractInfoService = spContractInfoService
    }

    @Unroll
    def "onMessageTest"() {
        given: "设定相关方法入参"
        message.times() >> 0
        message.localRetries() >> 0

        when:
        testObj.onMessage(message)

        then: "验证返回结果里属性值是否符合预期"
        message.times() == 0
    }

    @Unroll
    def "onMessageMaxTimes"() {
        given: "设定相关方法入参"
        message.times() >> 5
        message.localRetries() >> 0

        when:
        testObj.onMessage(message)

        then: "验证返回结果里属性值是否符合预期"
        message.times() == 5
    }

    @Unroll
    def "onMessageMaxLocalRetries"() {
        given: "设定相关方法入参"
        message.times() >> 0
        message.localRetries() >> 3

        when:
        testObj.onMessage(message)

        then: "验证返回结果里属性值是否符合预期"
        def ex = thrown(NeedRetryException)
        ex.message == "Multiple local retries still failed"
    }

    @Unroll
    def "contractInfoUpdateTest"() {
        given: "设定相关方法入参"
        message.times() >> 0
        message.localRetries() >> 0

        when:
        testObj.contractInfoUpdate(message)

        then: "验证返回结果里属性值是否符合预期"
        message.times() == 0
    }

    @Unroll
    def "contractInfoUpdateMaxTimesTest"() {
        given: "设定相关方法入参"
        message.times() >> 5
        message.localRetries() >> 0

        when:
        testObj.contractInfoUpdate(message)

        then: "验证返回结果里属性值是否符合预期"
        message.times() == 5
    }

    @Unroll
    def "contractInfoUpdateMaxLocalRetriesTest"() {
        given: "设定相关方法入参"
        message.times() >> 0
        message.localRetries() >> 3

        when:
        testObj.contractInfoUpdate(message)

        then: "验证返回结果里属性值是否符合预期"
        def ex = thrown(NeedRetryException)
        ex.message == "Multiple local retries still failed"
    }
}
