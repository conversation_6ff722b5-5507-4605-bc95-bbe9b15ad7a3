package com.ctrip.dcs.application.provider.executor.dispatchergrab

import com.ctrip.dcs.application.command.dispatchergrab.SubmitDispatcherGrabOrderExeCmd
import com.ctrip.dcs.application.command.dto.DispatcherGrabSubmitResDTO
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.self.dispatchorder.interfaces.RefuseDispatcherGrabOrdersResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.SubmitDispatcherGrabOrdersRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.SubmitDispatcherGrabOrdersResponseType
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.result.Result
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class SubmitDispatcherGrabOrderExecutorTest extends Specification {
    @Mock
    Logger logger
    @Mock
    SubmitDispatcherGrabOrderExeCmd cmd
    @Mock
    SubmitDispatcherGrabOrdersRequestType request
    @InjectMocks
    SubmitDispatcherGrabOrderExecutor submitDispatcherGrabOrderExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(request.getUserOrderId()).thenReturn("1")
        when(request.getDspOrderId()).thenReturn("1")
        when(request.getOperatorUserId()).thenReturn("1")
        when(request.getOperatorUserName()).thenReturn("1")
        when(request.getSupplierId()).thenReturn(1L)
        when(request.getUrgent()).thenReturn(1)
        when(cmd.execute(any())).thenReturn(Result.Builder.<DispatcherGrabSubmitResDTO>newResult().withData(new DispatcherGrabSubmitResDTO()).success().build())
        when:
        SubmitDispatcherGrabOrdersResponseType result = submitDispatcherGrabOrderExecutor.execute(request)

        then:
        result.getResponseResult().isSuccess()
    }

    def "test execute fail"() {
        given:
        when(request.getUserOrderId()).thenReturn("1")
        when(request.getDspOrderId()).thenReturn("1")
        when(request.getOperatorUserId()).thenReturn("1")
        when(request.getOperatorUserName()).thenReturn("1")
        when(request.getSupplierId()).thenReturn(1L)
        when(request.getUrgent()).thenReturn(1)
        when(cmd.execute(any())).thenThrow(error)
        when:
        SubmitDispatcherGrabOrdersResponseType result = submitDispatcherGrabOrderExecutor.execute(request)

        then:
        result.getResponseResult().getReturnCode() == code

        where:
        error                                                    || code
        ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getBizException() || ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getCode()
        new RuntimeException()                                   || "500"

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
