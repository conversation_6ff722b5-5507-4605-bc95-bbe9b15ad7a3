package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand
import com.ctrip.dcs.domain.common.service.*
import com.ctrip.dcs.domain.common.value.saas.SaaSBusinessVO
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.DcsVbkSupplierOrderGateway
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.infrastructure.service.ConfirmSaasDspOrderServiceImpl
import spock.lang.Specification
import spock.lang.Unroll

class AbstractSaaSOperateExeCmdTest extends Specification {
    def testObj = new SaaSOtherBindCarExeCmd()
    def queryDriverService = Mock(QueryDriverService)
    def queryVehicleService = Mock(QueryVehicleService)
    def orderQueryService = Mock(QueryDspOrderService)
    def checkService = Mock(CheckService)
    def driverOrderFactory = Mock(DriverOrderFactory)
    def confirmDspOrderService = Mock(ConfirmDspOrderService)
    def confirmSaasDspOrderService = Mock(ConfirmSaasDspOrderServiceImpl)
    def messageProducer = Mock(MessageProviderService)
    def manualSubSkuConf = Mock(ManualSubSkuConf)
    def subSkuRepository = Mock(SubSkuRepository)
    def distributedLockService = Mock(DistributedLockService)
    def igtOrderQueryServiceGateway = Mock(IGTOrderQueryServiceGateway)
    def queryTransportGroupService = Mock(QueryTransportGroupService)
    def dcsVbkSupplierOrderGateway = Mock(DcsVbkSupplierOrderGateway)

    def setup() {

        testObj.distributedLockService = distributedLockService
        testObj.confirmSaasDspOrderService = confirmSaasDspOrderService
        testObj.queryVehicleService = queryVehicleService
        testObj.subSkuRepository = subSkuRepository
        testObj.messageProducer = messageProducer
        testObj.manualSubSkuConf = manualSubSkuConf
        testObj.checkService = checkService
        testObj.confirmDspOrderService = confirmDspOrderService
        testObj.driverOrderFactory = driverOrderFactory
        testObj.igtOrderQueryServiceGateway = igtOrderQueryServiceGateway
        testObj.dcsVbkSupplierOrderGateway = dcsVbkSupplierOrderGateway
        testObj.queryDriverService = queryDriverService
        testObj.orderQueryService = orderQueryService
        testObj.queryTransportGroupService = queryTransportGroupService
    }

    @Unroll
    def "buildResLogVOTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.buildResLogVO(saaSBusinessVO, command)

        then: "验证返回结果里属性值是否符合预期"
        result == null == expectedResult
        where: "表格方式验证多种分支调用场景"
        saaSBusinessVO  | command || expectedResult
        null |new SaaSOperateDriverCarCommand(newProcess: true) || true
        null |new SaaSOperateDriverCarCommand(newProcess: false) || true
        new SaaSBusinessVO() |new SaaSOperateDriverCarCommand(newProcess: Boolean.TRUE) || true
    }
}
