package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.CreateOrderWayPointTagCommand
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.dsporder.entity.UserBookInfo
import com.ctrip.dcs.domain.dsporder.entity.UserOrderDetail
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway
import com.ctrip.dcs.infrastructure.adapter.trocks.TRocksProviderAdapter
import com.ctrip.igt.framework.common.exception.BizException
import org.mockito.Mockito
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class CreateOrderWayPointTagExeCmdTest extends Specification {
    @Mock
    IGTOrderQueryServiceGateway igtOrderQueryServiceGateway
    @Mock
    TRocksProviderAdapter rocksProviderAdapter
    @InjectMocks
    CreateOrderWayPointTagExeCmd createOrderWayPointTagExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        CreateOrderWayPointTagCommand command = new CreateOrderWayPointTagCommand("userOrderId", "dspOrderId")
        when(igtOrderQueryServiceGateway.queryUserOrderDetail(anyString())).thenReturn(new UserOrderDetail(bookInfo: new UserBookInfo(useTime: "useTime", wayPointTag: 0)))
        when(rocksProviderAdapter.setex(anyString(), anyString(), anyInt())).thenReturn(Boolean.TRUE).thenReturn(Boolean.TRUE)

        when:
        createOrderWayPointTagExeCmd.execute(command)

        then:
        command.getDspOrderId() == "dspOrderId"
    }

    def "test execute exception"() {
        given:
        CreateOrderWayPointTagCommand command = new CreateOrderWayPointTagCommand("userOrderId", "dspOrderId")
        when(igtOrderQueryServiceGateway.queryUserOrderDetail(anyString())).thenReturn(null)
        when(rocksProviderAdapter.setex(anyString(), anyString(), anyInt())).thenReturn(Boolean.TRUE).thenReturn(Boolean.TRUE)

        when:
        createOrderWayPointTagExeCmd.execute(command)

        then:
        def e = thrown(BizException)
        e.getCode() == ErrorCode.QUERY_USER_ORDER_EXCEPTION.getCode()
    }

    def "test execute exception1"() {
        given:
        CreateOrderWayPointTagCommand command = new CreateOrderWayPointTagCommand("userOrderId", "dspOrderId")
        when(igtOrderQueryServiceGateway.queryUserOrderDetail(anyString())).thenReturn(new UserOrderDetail())
        when(rocksProviderAdapter.setex(anyString(), anyString(), anyInt())).thenReturn(Boolean.TRUE).thenReturn(Boolean.TRUE)

        when:
        createOrderWayPointTagExeCmd.execute(command)

        then:
        def e = thrown(BizException)
        e.getCode() == ErrorCode.QUERY_USER_ORDER_EXCEPTION.getCode()
    }

    def "test execute exception2"() {
        given:
        CreateOrderWayPointTagCommand command = new CreateOrderWayPointTagCommand("userOrderId", "dspOrderId")
        when(igtOrderQueryServiceGateway.queryUserOrderDetail(anyString())).thenReturn(new UserOrderDetail(bookInfo: new UserBookInfo()))
        when(rocksProviderAdapter.setex(anyString(), anyString(), anyInt())).thenReturn(Boolean.TRUE).thenReturn(Boolean.TRUE)

        when:
        createOrderWayPointTagExeCmd.execute(command)

        then:
        def e = thrown(BizException)
        e.getCode() == ErrorCode.QUERY_USER_ORDER_EXCEPTION.getCode()
    }

    def "test get Seconds"() {
        given:
        Date date = DateUtil.addDays(new Date(), 1)
        when:
        int result1 = createOrderWayPointTagExeCmd.getSeconds(new UserOrderDetail(bookInfo: new UserBookInfo(useTime: null)))
        int result2 = createOrderWayPointTagExeCmd.getSeconds(new UserOrderDetail(bookInfo: new UserBookInfo(useTime: "null")))
        int result3 = createOrderWayPointTagExeCmd.getSeconds(new UserOrderDetail(bookInfo: new UserBookInfo(useTime: DateUtil.formatDate(date, "yyyy-MM-dd HH:mm:ss"))))

        then:
        result1 == 15 * 24 * 60 * 60
        result2 == 15 * 24 * 60 * 60
        result3 > 15 * 24 * 60 * 60
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme