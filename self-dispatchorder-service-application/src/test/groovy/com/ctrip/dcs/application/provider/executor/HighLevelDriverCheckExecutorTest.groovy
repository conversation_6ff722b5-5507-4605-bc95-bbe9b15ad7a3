package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.domain.common.service.HighLevelCheckService
import com.ctrip.dcs.self.dispatchorder.interfaces.HighLevelDriverRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.HighLevelDriverResponseType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class HighLevelDriverCheckExecutorTest extends Specification {
    @Mock
    HighLevelCheckService highLevelCheckService
    @InjectMocks
    HighLevelDriverCheckExecutor highLevelDriverCheckExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(highLevelCheckService.checkHighLevelDriver(any())).thenReturn([null])

        when:
        HighLevelDriverResponseType result = highLevelDriverCheckExecutor.execute(new  HighLevelDriverRequestType())

        then:
        result != null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme