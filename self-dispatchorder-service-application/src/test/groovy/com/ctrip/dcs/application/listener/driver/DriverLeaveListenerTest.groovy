package com.ctrip.dcs.application.listener.driver

import com.ctrip.dcs.application.event.IDriverLeaveEventHandler
import com.ctrip.dcs.infrastructure.common.config.SysSwitchConfig
import com.ctrip.igt.framework.common.clogging.Logger
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DriverLeaveListenerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    IDriverLeaveEventHandler handler
    @Mock
    SysSwitchConfig sysSwitchConfig
    @InjectMocks
    DriverLeaveListener driverLeaveListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test handle 1"() {
        given:
        Message message = new BaseMessage()
        when(sysSwitchConfig.getLeaveContinueSwitch()).thenReturn(false)

        when:
        driverLeaveListener.handle(message)

        then:
        message.getTags().size() == 0
    }

    def "test handle 2"() {
        given:
        Message message = new BaseMessage()
        when(sysSwitchConfig.getLeaveContinueSwitch()).thenReturn(true)

        when:
        driverLeaveListener.handle(message)

        then:
        message.getTags().size() == 0
    }

    def "test handle 3"() {
        given:
        Message message = new BaseMessage()
        message.addTag("tag_driverleave_add")
        when(sysSwitchConfig.getLeaveContinueSwitch()).thenReturn(true)

        when:
        driverLeaveListener.handle(message)

        then:
        message.getTags().size() == 1
    }

    def "test handle 4"() {
        given:
        Message message = new BaseMessage()
        message.addTag("tag_driverleave_add")
        message.setProperty("driverId", "1")
        when(sysSwitchConfig.getLeaveContinueSwitch()).thenReturn(true)

        when:
        driverLeaveListener.handle(message)

        then:
        message.getTags().size() == 1
    }

    def "test handle 5"() {
        given:
        Message message = new BaseMessage()
        message.addTag("tag_driverleave_add")
        message.setProperty("driverId", "1")
        message.setProperty("startTime", "1")
        message.setProperty("endTime", "1")
        when(sysSwitchConfig.getLeaveContinueSwitch()).thenReturn(true)

        when:
        driverLeaveListener.handle(message)

        then:
        message.getTags().size() == 1
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme