package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.converter.SupplierInfoConverter
import com.ctrip.dcs.application.command.tool.BatchUpdateSupplierExeCmd
import com.ctrip.dcs.domain.dsporder.entity.tool.BatchUpdateSupplierExecContext
import com.ctrip.dcs.domain.dsporder.entity.tool.SupplierInfoDO
import com.ctrip.dcs.domain.dsporder.gateway.ScmMerchantServiceGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderPO
import com.ctrip.dcs.infrastructure.adapter.soa.TmsTransportServiceProxy
import com.ctrip.dcs.scm.merchant.interfaces.message.QueryContractByServiceProviderIdResponseType
import com.ctrip.dcs.scm.sdk.domain.ContractRepository
import com.ctrip.dcs.scm.sdk.domain.contract.Contract
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchUpdateSupplierRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchUpdateSupplierResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.SupplierInfo
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupsForDspInfo
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupsForDspResponseType
import com.ctrip.dcs.tms.transport.api.model.TransportGroupDetailSOAType
import com.google.common.util.concurrent.Futures
import com.google.common.util.concurrent.SettableFuture
import spock.lang.Specification
import spock.lang.Unroll

class BatchUpdateSupplierExeCmdTest extends Specification {

    def contractRepository = Mock(ContractRepository)
    def tmsTransportServiceProxy = Mock(TmsTransportServiceProxy)
    def dspOrderRepository = Mock(DspOrderRepository)
    def scmMerchantServiceGateway = Mock(ScmMerchantServiceGateway)
    def supplierInfoConverter = Mock(SupplierInfoConverter)
    def batchUpdateSupplierExeCmd = new BatchUpdateSupplierExeCmd(
            contractRepository: contractRepository,
            tmsTransportServiceProxy: tmsTransportServiceProxy,
            dspOrderRepository: dspOrderRepository,
            scmMerchantServiceGateway: scmMerchantServiceGateway
    )

    def "test execute method with valid input"() {
        given: "A valid BatchUpdateSupplierRequestType"
        def supplierInfo = new SupplierInfo(userOrderId: "123", spId: 1L, supplierId: 1L, skuId: 1, transportGroupId: 1L)
        def request = new BatchUpdateSupplierRequestType(supplierInfoList: [supplierInfo])

        and: "Mocked dependencies return expected values"
        contractRepository.findManyByServiceProviderIds(_, _) >> [1L: []]
        dspOrderRepository.batchQueryDspOrdersByUserOrderIds(_) >> []
        tmsTransportServiceProxy.queryTransportGroupsForDsp(_) >> null
        supplierInfoConverter.convert(_, _) >> [new SupplierInfoDO(userOrderId: "123", spId: 1L, supplierId: 1L, skuId: 1, transportGroupId: 1L)]

        when: "The execute method is called"
        BatchUpdateSupplierResponseType response = batchUpdateSupplierExeCmd.execute(request)

        then: "The response should indicate failure due to missing contracts"
        response != null
        response.bizErrorDetailList.size() > 0
    }

    def "test execute method with duplicate userOrderId"() {
        given: "A BatchUpdateSupplierRequestType with duplicate userOrderId"
        def supplierInfo1 = new SupplierInfo(userOrderId: "123", spId: 1L, supplierId: 1L, skuId: 1, transportGroupId: 1L)
        def supplierInfo2 = new SupplierInfo(userOrderId: "123", spId: 2L, supplierId: 2L, skuId: 2, transportGroupId: 2L)
        def request = new BatchUpdateSupplierRequestType(supplierInfoList: [supplierInfo1, supplierInfo2])
        contractRepository.findManyByServiceProviderIds(_, _) >> [1L: []]
        when: "The execute method is called"
        BatchUpdateSupplierResponseType response = batchUpdateSupplierExeCmd.execute(request)

        then: "The response should indicate failure due to duplicate userOrderId"
        response != null
        response.bizErrorDetailList.size() > 0
    }
    def "test convert method with valid SupplierInfo list"() {
        given: "A valid list of SupplierInfo and a context"
        def supplierInfo = new SupplierInfo(userOrderId: "123", spId: 1L, supplierId: 1L, skuId: 1, transportGroupId: 1L)
        def context = new BatchUpdateSupplierExecContext()
        def persistedSupplierInfoDO = new SupplierInfoDO(id: 1L, cityId: 100)
        context.getPersistSupplierInfoDOMap().put("123", persistedSupplierInfoDO)

        when: "The convert method is called"
        List<SupplierInfoDO> supplierInfoDOList = SupplierInfoConverter.convert([supplierInfo], context)

        then: "The result should be a list with one SupplierInfoDO object"
        supplierInfoDOList.size() == 1
        with(supplierInfoDOList[0]) {
            userOrderId == "123"
            spId == 1L
            supplierId == 1L
            skuId == 1
            transportGroupId == 1L
            id == 1L
            cityId == 100
        }
    }

    def "test convert method with empty SupplierInfo list"() {
        given: "An empty list of SupplierInfo and a context"
        def context = new BatchUpdateSupplierExecContext()

        when: "The convert method is called"
        List<SupplierInfoDO> supplierInfoDOList = SupplierInfoConverter.convert([], context)

        then: "The result should be an empty list"
        supplierInfoDOList.isEmpty()
    }

    def "test convert method with TransportGroupDetailSOAType"() {
        given: "A TransportGroupDetailSOAType object"
        def tmsGroup = new TransportGroupDetailSOAType(
                dispatcherEmail: "<EMAIL>",
                transportGroupName: "Test Group",
                dispatcherLanguage: "EN",
                dispatcherPhone: "*********",
                igtCode: "001"
        )

        when: "The convert method is called"
        DspOrderConfirmRecordVO.DispatcherRecord dispatcherRecord = SupplierInfoConverter.convert(tmsGroup)

        then: "The DispatcherRecord should have the correct values"
        with(dispatcherRecord) {
            dispatcherEmail == "<EMAIL>"
            dispatcherName == "Test Group"
            dispatcherLanguage == "EN"
            dispatcherPhone == "*********"
            dispatcherPhoneCode == "001"
            dispatcherContactAccount == "wechat"
        }
    }

    def "test convert method with DspOrderPO list"() {
        given: "A list of DspOrderPO and SupplierInfo"
        def orderPO = new DspOrderPO(userOrderId: "123")
        def supplierInfo = new SupplierInfo(userOrderId: "123", spId: 1L, supplierId: 1L)
        def dspOrderPOList = [orderPO]

        when: "The convert method is called"
        List<DspOrderPO> result = SupplierInfoConverter.convert(dspOrderPOList, [supplierInfo])

        then: "The DspOrderPO should be updated with supplier info"
        result.size() == 1
        with(result[0]) {
            supplierId == 1
            spId == 1
        }
    }

    def "test execute method with valid input 2"() {
        given: "A valid BatchUpdateSupplierRequestType"
        def supplierInfo = new SupplierInfo(userOrderId: "123", spId: 1L, supplierId: 1L, skuId: 1, transportGroupId: 1L)
        def request = new BatchUpdateSupplierRequestType(supplierInfoList: [supplierInfo])
        and: "Mocked dependencies return expected values"
        contractRepository.findManyByServiceProviderIds(_, _) >> [1L: [Contract.newBuilder().withSupplierId(1L).build()]]
        dspOrderRepository.batchQueryDspOrdersByUserOrderIds(_) >>
                [new SupplierInfoDO(userOrderId: "123", orderStatus: 220, connectMode: 1)]
        tmsTransportServiceProxy.queryTransportGroupsForDsp(_) >> new QueryTransportGroupsForDspResponseType(data: [new QueryTransportGroupsForDspInfo(skuId: 1L, transportGroupList: [new TransportGroupDetailSOAType(transportGroupId: 1L, transportGroupMode: 4, supplierId: 1L)])])
        scmMerchantServiceGateway.queryContractByServiceProviderIdAsync(_, _, _) >> Futures.immediateFuture(
                new QueryContractByServiceProviderIdResponseType())

        when: "The execute method is called"
        BatchUpdateSupplierResponseType response = batchUpdateSupplierExeCmd.execute(request)

        then: "The response should indicate success"
        response != null
    }

    def "test execute method with invalid orderId"() {
        given: "A BatchUpdateSupplierRequestType with a non-existent orderId"
        def supplierInfo = new SupplierInfo(userOrderId: "999", spId: 1L, supplierId: 1L, skuId: 1, transportGroupId: 1L)
        def request = new BatchUpdateSupplierRequestType(supplierInfoList: [supplierInfo])

        and: "Mocked dependencies return expected values"
        dspOrderRepository.batchQueryDspOrdersByUserOrderIds(_) >> []
        contractRepository.findManyByServiceProviderIds(_, _) >> [1L: [Contract.newBuilder().withSupplierId(1L).build()]]

        when: "The execute method is called"
        BatchUpdateSupplierResponseType response = batchUpdateSupplierExeCmd.execute(request)

        then: "The response should indicate failure due to non-existent orderId"
        response != null
        response.bizErrorDetailList.size() > 0
    }

    def "test execute method with unmatched skuId and tmsId"() {
        given: "A BatchUpdateSupplierRequestType with unmatched skuId and tmsId"
        def supplierInfo = new SupplierInfo(userOrderId: "123", spId: 1L, supplierId: 1L, skuId: 1, transportGroupId: 2L)
        def request = new BatchUpdateSupplierRequestType(supplierInfoList: [supplierInfo])
        contractRepository.findManyByServiceProviderIds(_, _) >> [1L: [Contract.newBuilder().withSupplierId(1L).build()]]

        and: "Mocked dependencies return expected values"
        dspOrderRepository.batchQueryDspOrdersByUserOrderIds(_) >>
                [new SupplierInfoDO(userOrderId: "123", orderStatus: 220, connectMode: 1)]
        tmsTransportServiceProxy.queryTransportGroupsForDsp(_) >> new QueryTransportGroupsForDspResponseType(data: [new QueryTransportGroupsForDspInfo(skuId: 1L, transportGroupList: [new TransportGroupDetailSOAType(transportGroupId: 1L, transportGroupMode: 4, supplierId: 1L)])])

        when: "The execute method is called"
        BatchUpdateSupplierResponseType response = batchUpdateSupplierExeCmd.execute(request)

        then: "The response should indicate failure due to unmatched skuId and tmsId"
        response != null
        response.bizErrorDetailList.size() > 0
    }

    @Unroll
    def "test populateContractInfo"() {
        given: "A list of SupplierInfoDO and mocked dependencies"
        def supplierInfoDO = new SupplierInfoDO(spId: 1L, cityId: 100, supplierId: 1L)
        def supplierInfoDOList = [supplierInfoDO]
        def response = new QueryContractByServiceProviderIdResponseType()
        def future = SettableFuture.create()
        future.set(response)
        scmMerchantServiceGateway.queryContractByServiceProviderIdAsync(_, _, _) >> future

        when: "populateContractInfo method is called"
        batchUpdateSupplierExeCmd.populateContractInfo(supplierInfoDOList)

        then: "The SupplierInfoDO should be populated with contract info"
        supplierInfoDO.getSpContractInfo() != null
    }
}
