package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.CalculateDriverMileageProfitCommand
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum
import com.ctrip.dcs.domain.common.service.LocationBasedService
import com.ctrip.dcs.domain.common.service.OrderFeePriorityService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DriverWorkTimeVO
import com.ctrip.dcs.domain.common.value.DspOrderFeeQuantileVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.LocationRouteVO
import com.ctrip.dcs.domain.mileage.entity.DriverMileageProfitDO
import com.ctrip.dcs.domain.mileage.factory.DriverMileageProfitFactory
import com.ctrip.dcs.domain.mileage.repository.DriverMileageProfitRepository
import com.ctrip.dcs.domain.mileage.value.DriverMileageProfitTypeVO
import com.google.common.collect.Lists
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class CalculateDriverMileageProfitExeCmdTest extends Specification {
    @Mock
    DriverMileageProfitRepository completeDriverMileageProfitRepositoryImpl
    @Mock
    DriverMileageProfitRepository expectDriverMileageProfitRepositoryImpl
    @Mock
    DriverMileageProfitRepository todayDriverMileageProfitRepositoryImpl
    @Mock
    DriverMileageProfitFactory driverMileageProfitFactory
    @Mock
    LocationBasedService locationBasedService
    @Mock
    OrderFeePriorityService orderFeePriorityService
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverVO driverVO
    @Mock
    DriverWorkTimeVO driverWorkTimeVO
    @Mock
    DriverMileageProfitDO driverMileageProfitDO
    @InjectMocks
    CalculateDriverMileageProfitExeCmd calculateDriverMileageProfitExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        DspOrderFeeQuantileVO dspOrderFeeQuantileVO = new DspOrderFeeQuantileVO(1, 0, null, null)
        when(driverWorkTimeVO.getStart()).thenReturn(DateUtil.parseDateStr2Date("2023-12-18 12:00:00"))
        when(driverWorkTimeVO.getEnd()).thenReturn(DateUtil.parseDateStr2Date("2023-12-18 13:00:00"))
        when(driverMileageProfitFactory.create(any(), any())).thenReturn(driverMileageProfitDO)
        when(locationBasedService.query(any())).thenReturn([new LocationRouteVO(null, null, 0, 0)])
        when(orderFeePriorityService.queryOrderFeeQuantile(anyInt())).thenReturn([dspOrderFeeQuantileVO])
        when(queryDspOrderService.queryOrderListWithDriverAndTime(driverVO, DateUtil.parseDateStr2Date("2023-12-18 12:00:00"), DateUtil.parseDateStr2Date("2023-12-18 13:00:00"), Lists.newArrayList(OrderStatusEnum.DRIVER_CONFIRMED, OrderStatusEnum.DRIVER_CAR_CONFIRMED, OrderStatusEnum.ORDER_FINISH),true,true)).thenReturn([dspOrderVO])

        when:
        calculateDriverMileageProfitExeCmd.execute(new CalculateDriverMileageProfitCommand(driverVO, driverWorkTimeVO, [DriverMileageProfitTypeVO.COMPLETE], "", null))

        then:
        dspOrderFeeQuantileVO.getCityId() == 1
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
