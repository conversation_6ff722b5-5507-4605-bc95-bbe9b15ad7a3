package com.ctrip.dcs.application.listener

import com.ctrip.dcs.domain.dsporder.event.DriverCarConfirmSyncPurchaseEvent
import com.ctrip.dcs.domain.dsporder.gateway.PurchaseSupplyOrderGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.platform.dal.dao.helper.JsonUtils
import qunar.tc.qmq.Message
import qunar.tc.qmq.NeedRetryException
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification
import spock.lang.Unroll

class DriverCarConfirmSyncPurchaseListenerTest extends Specification {
    def testObj = new DriverCarConfirmSyncPurchaseListener()
    def purchaseSupplyOrderGateway = Mock(PurchaseSupplyOrderGateway)
    def dspOrderRepository = Mock(DspOrderRepository)
    def message = Mock(Message)

    def setup() {

        testObj.purchaseSupplyOrderGateway = purchaseSupplyOrderGateway
        testObj.dspOrderRepository = dspOrderRepository
    }

    @Unroll
    def "onMessageTest1"() {
        when:
        Message message = new BaseMessage();
        DspOrderConfirmRecordVO dspOrderConfirmRecordVO = new DspOrderConfirmRecordVO()
        dspOrderConfirmRecordVO.setCategoryCode("1")
        message.setProperty("dspOrderConfirmRecord", JsonUtils.toJson(dspOrderConfirmRecordVO));
        message.setProperty("dspOrderId", "1");
        testObj.onMessage(message)

        then:
        message.times() == 1
    }

    @Unroll
    def "onMessageTest2"() {
        when:
        testObj.onMessage(message)

        then:
        message.times() == 0
    }

    @Unroll
    def "onMessageTest3"() {
        when:
        Message message = new BaseMessage();
        Date date = new Date()
        message.setProperty("dspOrderConfirmRecord", JsonUtils.toJson(date));
        testObj.onMessage(message)

        then:
        def ex = thrown(NeedRetryException)
        ex.message == "DriverCarConfirmSyncPurchaseListener"
    }

    @Unroll
    def "DriverCarConfirmSyncPurchaseEvent"() {
        when:
        DriverCarConfirmSyncPurchaseEvent event = new DriverCarConfirmSyncPurchaseEvent("1", "")

        then:
        event.getDspOrderId() == "1"
    }
}
