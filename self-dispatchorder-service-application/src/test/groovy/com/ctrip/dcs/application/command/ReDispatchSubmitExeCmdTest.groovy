package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.ReDispatchSubmitCommand
import com.ctrip.dcs.application.query.ReasonsQuery
import com.ctrip.dcs.application.service.RedispatchRightAndPointService
import com.ctrip.dcs.application.service.reDispath.ReassignTaskManger
import com.ctrip.dcs.application.service.reDispath.RightAndPointService
import com.ctrip.dcs.domain.common.enums.*
import com.ctrip.dcs.domain.common.service.*
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf
import com.ctrip.dcs.domain.dsporder.entity.*
import com.ctrip.dcs.domain.dsporder.gateway.DateZoneConvertGateway
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway
import com.ctrip.dcs.domain.dsporder.gateway.WorkBenchLogGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderOperateRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.repository.ReDispatchRecordRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.DriverDomainServiceGateway
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.value.DriverRightsVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig
import com.ctrip.dcs.infrastructure.common.config.ReasonDetailConfig
import com.ctrip.dcs.infrastructure.common.config.RedispatchRightConfig
import com.ctrip.dcs.infrastructure.common.config.SysSwitchConfig
import com.ctrip.dcs.infrastructure.factory.VBKOperationRecordFactory
import com.ctrip.dcs.infrastructure.factory.WorkBenchLogMessageFactory
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.result.Result
import com.google.common.collect.Maps
import org.junit.platform.commons.util.StringUtils
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import org.powermock.api.mockito.PowerMockito
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp
import java.util.concurrent.ExecutorService

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class ReDispatchSubmitExeCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    QueryDriverService queryDriverService
    @Mock
    DriverOrderFactory driverOrderFactory
    @Mock
    ConfirmDspOrderService confirmDspOrderService
    @Mock
    MessageProviderService messageProducer
    @Mock
    ManualSubSkuConf manualSubSkuConf
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    SubSkuRepository subSkuRepository
    @Mock
    DistributedLockService distributedLockService
    @Mock
    QueryTransportGroupService queryTransportGroupService
    @Mock
    DspOrderRepository dspOrderRepository
    @Mock
    SelfOrderQueryGateway selfOrderQueryGateway
    @Mock
    MessageProviderService messageProvider
    @Mock
    ReasonsQuery reasonsQuery
    @Mock
    WorkBenchLogGateway workBenchLogGateway
    @Mock
    DspOrderOperateRepository dspOrderOperateRepository
    @Mock
    DriverOrderGateway driverOrderGateway
    @Mock
    WorkBenchLogMessageFactory workBenchLogMessageFactory
    @Mock
    RedispatchRightConfig rightConfig
    @Mock
    RedispatchRightAndPointService redispatchRightAndPointService
    @Mock
    ReasonDetailConfig reasonDetailConfig
    @Mock
    ReDispatchRecordRepository reDispatchRecordRepository
    @Mock
    DriverDomainServiceGateway driverDomainServiceGateway
    @Mock
    QueryFlightService queryFlightService
    @Mock
    QueryDriverLocationService queryDriverLocationService
    @Mock
    VBKOperationRecordGateway vbkOperationRecordGateway
    @Mock
    VBKOperationRecordFactory vbkOperationRecordFactory
    @Mock
    CancelDspOrderExeCmd cancelDspOrderExeCmd
    @Mock
    DateZoneConvertGateway dateZoneConvertGateway
    @Mock
    ExecutorService syncOrderThreadPool
    @Mock
    BusinessTemplateInfoConfig businessTemplateInfoConfig
    @Mock
    DistributedLockService.DistributedLock distributedLock
    @Mock
    RightAndPointService rightAndPointService;
    @Mock
    DriverRightsVO driverRightsVO;
    @Mock
    FlightVO flightVO;
    @Mock
    DriverToPointEstimateDataVO driverToPointEstimateDataVO;
    @Mock
    SysSwitchConfig sysSwitchConfig;
    @InjectMocks
    ReDispatchSubmitExeCmd executor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given: "Mock数据"

        when(distributedLockService.getLock(anyString())).thenReturn(distributedLock)
        when(distributedLock.tryLock()).thenReturn(lock)

        when(workBenchLogMessageFactory.reDispatchSubmitFailWorkBenchLog(any())).thenReturn(new WorkBenchLogMessage())
        when(selfOrderQueryGateway.queryOrderBaseDetail(any(), any())).thenReturn(detailVO)

        PowerMockito.when(rightAndPointService.convertAndCheckPrm(Mockito.any(), Mockito.any())).thenReturn(convertCheckprm)
        PowerMockito.when(rightAndPointService.queryRightAndPointDTO(Mockito.any(), Mockito.any())).thenReturn(point)

        when(dspOrderRepository.queryValidDspOrders(any())).thenReturn([getDspOrderDO()])
        when(workBenchLogMessageFactory.cancelDspOrderWorkBenchLog(any())).thenReturn(new WorkBenchLogMessage())

        when(rightConfig.getNightRangeTimeStart()).thenReturn("8:00")
        when(rightConfig.getNightRangeTimeEnd()).thenReturn("23:00")
        when(reasonDetailConfig.getReasonDetail(any())).thenReturn(new ReasonDetailVO())

        ReassignTaskManger.addAgent(ReassignTaskEnum.RoleEnum.ROLE_DRIVER_APP.getDesc(), rightAndPointService)

        when: "执行校验方法"
        def order = executor.execute(req)

        then: "验证校验结果"
        order.success == res

        where:
        req      | flowSwitch | lock  | detailVO           | convertCheckprm       | point       || res
        getReq() | false      | false | null               | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | false | null               | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | null               | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO0() | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO1() | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO2() | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO3() | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO4() | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO()  | getconvertCheckprm1() | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO()  | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO()  | getconvertCheckprm()  | getpoint1() || false
        getReq() | true       | true  | getBaseDetailVO()  | getconvertCheckprm()  | getpoint2() || true
    }

    def "test execute1"() {
        given: "Mock数据"

        when(distributedLockService.getLock(anyString())).thenReturn(distributedLock)
        when(distributedLock.tryLock()).thenReturn(lock)

        when(workBenchLogMessageFactory.reDispatchSubmitFailWorkBenchLog(any())).thenReturn(new WorkBenchLogMessage())
        when(selfOrderQueryGateway.queryOrderBaseDetail(any(), any())).thenReturn(detailVO)

        PowerMockito.when(rightAndPointService.convertAndCheckPrm(Mockito.any(), Mockito.any())).thenReturn(convertCheckprm)
        PowerMockito.when(rightAndPointService.queryRightAndPointDTO(Mockito.any(), Mockito.any())).thenReturn(point)

        when(dspOrderRepository.queryValidDspOrders(any())).thenReturn([getDspOrderDO()])
        when(workBenchLogMessageFactory.cancelDspOrderWorkBenchLog(any())).thenReturn(new WorkBenchLogMessage())

        when(rightConfig.getNightRangeTimeStart()).thenReturn("8:00")
        when(rightConfig.getNightRangeTimeEnd()).thenReturn("23:00")
        when(reasonDetailConfig.getReasonDetail(any())).thenReturn(null)

        ReassignTaskManger.addAgent(ReassignTaskEnum.RoleEnum.ROLE_DRIVER_APP.getDesc(), rightAndPointService)

        when: "执行校验方法"
        def order = executor.execute(req)

        then: "验证校验结果"
        order.success == res

        where:
        req      | flowSwitch | lock  | detailVO           | convertCheckprm       | point       || res
        getReq() | false      | false | null               | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | false | null               | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | null               | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO0() | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO1() | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO2() | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO3() | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO4() | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO()  | getconvertCheckprm1() | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO()  | getconvertCheckprm()  | getpoint()  || false
        getReq() | true       | true  | getBaseDetailVO()  | getconvertCheckprm()  | getpoint1() || false
        getReq() | true       | true  | getBaseDetailVO()  | getconvertCheckprm()  | getpoint2() || true
    }


    @Unroll
    def "test sendVBKOperationRecord123"() {

        given: "Mock数据"
        ReDispatchSubmitCommand command = new ReDispatchSubmitCommand()
        command.setReasonId("111")

        ReDispatchSubmitContext.Builder builder = ReDispatchSubmitContext.newContext();
        builder.withReasonDetailVO(null)
        ReDispatchSubmitContext context = builder.init()

        ReDispatchSubmitContext.getCurrent() >> context

        dateZoneConvertGateway.getLocalTime(_,_) >> new Date()
        vbkOperationRecordFactory.createReDispatchSubmitOperationRecord(_,_,_,_,_) >> null

        def req = getBaseDetailVO()
        req.setCityId(1)


        when: "执行校验方法"
        executor.sendVBKOperationRecord(getBaseDetailVO(),command)

        then: "验证校验结果"
        command != null == true



    }

    @Unroll
    def "test sendVBKOperationRecord456"() {

        given: "Mock数据"
        ReDispatchSubmitCommand command = new ReDispatchSubmitCommand()
        command.setReasonId("111")

        ReDispatchSubmitContext.Builder builder = ReDispatchSubmitContext.newContext();
        builder.withReasonDetailVO(new ReasonDetailVO(reasonDetail: "333"))
        ReDispatchSubmitContext context = builder.init()

        ReDispatchSubmitContext.getCurrent() >> context

        dateZoneConvertGateway.getLocalTime(_,_) >> new Date()
        vbkOperationRecordFactory.createReDispatchSubmitOperationRecord(_,_,_,_,_) >> null

        def req = getBaseDetailVO()
        req.setCityId(1)


        when: "执行校验方法"
        executor.sendVBKOperationRecord(getBaseDetailVO(),command)

        then: "验证校验结果"
        command != null == true



    }

    def "test initLogContentDTO"() {
        given: "Mock数据"

        ReDispatchSubmitCommand command = new ReDispatchSubmitCommand()
        BaseDetailVO baseDetailVO = new BaseDetailVO()
        RightAndPointVO rightAndPointVO = new RightAndPointVO(dispatchRightInfo: new DispatchRightVO(), punishInfo: new PunishVO(punishRoleId : ReassignTaskEnum.PunishRoleEnum.PUNISH_DRIVER.getCode()))
        ReDispatchSubmitContext.newContext().withReasonDetailVO(new ReasonDetailVO()).init()
        when(driverDomainServiceGateway.queryDriverRightsInfo(any())).thenReturn(driverRightsVO)
        when(driverRightsVO.getReassignUsedByWeek()).thenReturn(1)

        when: "执行校验方法"
        def dto = executor.initLogContentDTO(command, baseDetailVO, rightAndPointVO)

        RightAndPointVO rightAndPointVO1 = new RightAndPointVO(dispatchRightInfo: new DispatchRightVO(), punishInfo: new PunishVO(punishRoleId : ReassignTaskEnum.PunishRoleEnum.PUNISH_SUPPLIER.getCode()))
        command.setNewRights(1)
        def dto1 = executor.initLogContentDTO(command, baseDetailVO, rightAndPointVO1)

        then: "验证校验结果"

        then: "验证校验结果"
        dto != null
        dto1 != null
    }

    def "test sendNewWorkBenchLogMessage"() {
        given: "Mock数据"

        ReDispatchSubmitCommand command = new ReDispatchSubmitCommand(roleId: RoleReflectEnum.ROLE_DRIVER_APP.getCode(), reasonId: ReassignTaskEnum.DispatchReasonIdEnum.DRIVER_CAN_NOT_ARRIVE.getCode())
        BaseDetailVO baseDetailVO = new BaseDetailVO()
        RightAndPointVO rightAndPointVO = new RightAndPointVO(dispatchRightInfo: new DispatchRightVO(), punishInfo: new PunishVO(punishRoleId : ReassignTaskEnum.PunishRoleEnum.PUNISH_DRIVER.getCode()))
        ReDispatchSubmitContext.newContext().withReasonDetailVO(new ReasonDetailVO()).init()
        when(driverDomainServiceGateway.queryDriverRightsInfo(any())).thenReturn(driverRightsVO)
        when(driverRightsVO.getReassignUsedByWeek()).thenReturn(1)

        when: "执行校验方法"
        def dto = executor.sendNewWorkBenchLogMessage(command, baseDetailVO, rightAndPointVO)

        ReDispatchSubmitCommand command1 = new ReDispatchSubmitCommand(roleId: RoleReflectEnum.ROLE_CUSTOMER_CLIENT.getCode(), reasonId: ReassignTaskEnum.DispatchReasonIdEnum.DRIVER_CAN_NOT_ARRIVE.getCode())
        def dto1 = executor.sendNewWorkBenchLogMessage(command1, baseDetailVO, rightAndPointVO)

        then: "验证校验结果"

        then: "验证校验结果"
        dto == null
        dto1 == null
    }

    def "test rollBackOrder"() {
        given: "Mock数据"

        ReDispatchSubmitCommand command = new ReDispatchSubmitCommand(roleId: RoleReflectEnum.ROLE_DRIVER_APP.getCode(), reasonId: ReassignTaskEnum.DispatchReasonIdEnum.DRIVER_CAN_NOT_ARRIVE.getCode(), newRights: 1)
        BaseDetailVO baseDetailVO = new BaseDetailVO(estimatedUseTimeBj: "2023-01-01 00:00:00")
        RightAndPointVO rightAndPointVO = new RightAndPointVO(dispatchRightInfo: new DispatchRightVO(rightId: ReassignTaskEnum.RightTypeEnum.FREE_CHANGE_RIGHT.getCode()), punishInfo: new PunishVO(punishRoleId : ReassignTaskEnum.PunishRoleEnum.PUNISH_DRIVER.getCode()), responsable: false)
        ReDispatchSubmitContext.newContext().withReasonDetailVO(new ReasonDetailVO()).init()
        when(driverDomainServiceGateway.queryDriverRightsInfo(any())).thenReturn(driverRightsVO)
        when(driverRightsVO.getReassignUsedByWeek()).thenReturn(1)
        when: "执行校验方法"
        def dto = executor.rollBackOrder(command, baseDetailVO, rightAndPointVO)

        ReDispatchSubmitCommand command1 = new ReDispatchSubmitCommand(roleId: RoleReflectEnum.ROLE_DRIVER_APP.getCode(), reasonId: ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode(), newRights: 1, rightRecordId: 1)
        RightAndPointVO rightAndPointVO1 = new RightAndPointVO(dispatchRightInfo: new DispatchRightVO(rightId: ReassignTaskEnum.RightTypeEnum.LEAVE_RIGHT.getCode()), punishInfo: new PunishVO(punishRoleId : ReassignTaskEnum.PunishRoleEnum.PUNISH_DRIVER.getCode()), responsable: false)
        def dto1 = executor.rollBackOrder(command1, baseDetailVO, rightAndPointVO1)

        then: "验证校验结果"

        then: "验证校验结果"
        dto == null
        dto1 == null
    }

    def "test joinNewPunish"() {
        given: "Mock数据"

        ReDispatchSubmitCommand command = new ReDispatchSubmitCommand(drvId: 1L, roleId: RoleReflectEnum.ROLE_CUSTOMER_CONTINUE_USE_CAR.getCode(), reasonId: ReassignTaskEnum.DispatchReasonIdEnum.DRIVER_CAN_NOT_ARRIVE.getCode(), newRights: 1)
        BaseDetailVO baseDetailVO = new BaseDetailVO(estimatedUseTimeBj: "2023-01-01 00:00:00", supplierId: 1, drvId: 1L)
        RightAndPointVO rightAndPointVO = new RightAndPointVO(dispatchRightInfo: new DispatchRightVO(rightId: ReassignTaskEnum.RightTypeEnum.FREE_CHANGE_RIGHT.getCode()), punishInfo: new PunishVO(punishRoleId : ReassignTaskEnum.PunishRoleEnum.PUNISH_DRIVER.getCode()), responsable: true)
        ReDispatchSubmitContext.newContext().withReasonDetailVO(new ReasonDetailVO()).init()
        when(driverDomainServiceGateway.queryDriverRightsInfo(any())).thenReturn(driverRightsVO)
        when(driverRightsVO.getReassignUsedByWeek()).thenReturn(1)

        when: "执行校验方法"
        def dto = executor.joinNewPunish(command, baseDetailVO, rightAndPointVO)

        ReDispatchSubmitCommand command1 = new ReDispatchSubmitCommand(drvId: 1L, roleId: RoleReflectEnum.ROLE_CUSTOMER_CONTINUE_USE_CAR.getCode(), reasonId: ReassignTaskEnum.DispatchReasonIdEnum.CAR_FAULT.getCode(), newRights: 1)
        def dto1 = executor.joinNewPunish(command1, baseDetailVO, rightAndPointVO)

        then: "验证校验结果"

        then: "验证校验结果"
        dto == null
        dto1 == null
    }

    def "test joinNewPunish1"() {
        given: "Mock数据"

        ReDispatchSubmitCommand command = new ReDispatchSubmitCommand(drvId: null, roleId: RoleReflectEnum.ROLE_CUSTOMER_CONTINUE_USE_CAR.getCode(), reasonId: ReassignTaskEnum.DispatchReasonIdEnum.DRIVER_CAN_NOT_ARRIVE.getCode(), newRights: 1)
        BaseDetailVO baseDetailVO = new BaseDetailVO(estimatedUseTimeBj: "2023-01-01 00:00:00", supplierId: 1, drvId: 1L)
        RightAndPointVO rightAndPointVO = new RightAndPointVO(dispatchRightInfo: new DispatchRightVO(rightId: ReassignTaskEnum.RightTypeEnum.FREE_CHANGE_RIGHT.getCode()), punishInfo: new PunishVO(punishRoleId : ReassignTaskEnum.PunishRoleEnum.PUNISH_DRIVER.getCode()), responsable: true)
        ReDispatchSubmitContext.newContext().withReasonDetailVO(new ReasonDetailVO()).init()
        when(driverDomainServiceGateway.queryDriverRightsInfo(any())).thenReturn(driverRightsVO)
        when(driverRightsVO.getReassignUsedByWeek()).thenReturn(1)

        when: "执行校验方法"
        def dto = executor.joinNewPunish(command, baseDetailVO, rightAndPointVO)

        ReDispatchSubmitCommand command1 = new ReDispatchSubmitCommand(drvId: 1L, roleId: RoleReflectEnum.ROLE_CUSTOMER_CONTINUE_USE_CAR.getCode(), reasonId: ReassignTaskEnum.DispatchReasonIdEnum.CAR_FAULT.getCode(), newRights: 1)
        def dto1 = executor.joinNewPunish(command1, baseDetailVO, rightAndPointVO)

        then: "验证校验结果"

        then: "验证校验结果"
        dto == null
        dto1 == null
    }

    def "test buildReDispatchSubmitContext"() {
        given: "Mock数据"

        ReDispatchSubmitCommand command = new ReDispatchSubmitCommand(drvId: 1L, roleId: RoleReflectEnum.ROLE_CUSTOMER_CONTINUE_USE_CAR.getCode(), reasonId: ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode(), newRights: 1, reasonDetailId: ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode())
        BaseDetailVO baseDetailVO = new BaseDetailVO(estimatedUseTimeBj: "2023-01-01 00:00:00", supplierId: 1, drvId: 1L, fromPoiDTO: new FromPoiDTO())
        RightAndPointVO rightAndPointVO = new RightAndPointVO(dispatchRightInfo: new DispatchRightVO(rightId: ReassignTaskEnum.RightTypeEnum.FREE_CHANGE_RIGHT.getCode()), punishInfo: new PunishVO(punishRoleId : ReassignTaskEnum.PunishRoleEnum.PUNISH_DRIVER.getCode()), responsable: true)
        ReDispatchSubmitContext.newContext().withReasonDetailVO(new ReasonDetailVO()).init()
        when(driverDomainServiceGateway.queryDriverRightsInfo(any())).thenReturn(driverRightsVO)
        when(driverRightsVO.getReassignUsedByWeek()).thenReturn(1)
        when(reasonDetailConfig.getReasonDetail(any())).thenReturn(new ReasonDetailVO(reasonId: ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode()))

        when: "执行校验方法"
        def dto = executor.buildReDispatchSubmitContext(command, baseDetailVO)

        then: "验证校验结果"

        then: "验证校验结果"
        dto != null
    }

    def "test specialLogBuild"() {
        given: "Mock数据"

        ReDispatchSubmitCommand command = new ReDispatchSubmitCommand(drvId: 1L, roleId: RoleReflectEnum.ROLE_DRIVER_APP.getCode(), reasonId: ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode(), newRights: 1, reasonDetailId: ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode())
        BaseDetailVO baseDetailVO = new BaseDetailVO(estimatedUseTimeBj: "2023-01-01 00:00:00", supplierId: 1, drvId: 1L, fromPoiDTO: new FromPoiDTO(), categoryCode: CategoryCodeEnum.FROM_AIRPORT.getType())
        RightAndPointVO rightAndPointVO = new RightAndPointVO(dispatchRightInfo: new DispatchRightVO(rightId: ReassignTaskEnum.RightTypeEnum.FREE_CHANGE_RIGHT.getCode()), punishInfo: new PunishVO(punishRoleId : ReassignTaskEnum.PunishRoleEnum.PUNISH_DRIVER.getCode()), responsable: true)
        ReDispatchSubmitContext.newContext().withReasonDetailVO(new ReasonDetailVO()).withFlightVO(flightVO).withDriverToPointEstimateDataVO(driverToPointEstimateDataVO).init()
        LogContentDTO logContentDTO = new LogContentDTO()
        Map<String, String> keyMaps = Maps.newHashMap()
        when(driverDomainServiceGateway.queryDriverRightsInfo(any())).thenReturn(driverRightsVO)
        when(driverRightsVO.getReassignUsedByWeek()).thenReturn(1)
        when(reasonDetailConfig.getReasonDetail(any())).thenReturn(new ReasonDetailVO(reasonId: ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode()))
        when(driverToPointEstimateDataVO.getDriverPoint()).thenReturn(new DriverToPointEstimateDataVO.PointDTO(BigDecimal.ONE, BigDecimal.ONE, "", 1L))

        when: "执行校验方法"
        def dto = executor.specialLogBuild(command, baseDetailVO, logContentDTO, keyMaps)

        ReDispatchSubmitCommand command1 = new ReDispatchSubmitCommand(drvId: 1L, roleId: RoleReflectEnum.ROLE_DRIVER_APP.getCode(), reasonId: ReassignTaskEnum.DispatchReasonIdEnum.DRIVER_CAN_NOT_ARRIVE.getCode(), newRights: 1, reasonDetailId: ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode())
        def dto1 = executor.specialLogBuild(command1, baseDetailVO, logContentDTO, keyMaps)

        ReDispatchSubmitCommand command2 = new ReDispatchSubmitCommand(drvId: 1L, roleId: RoleReflectEnum.ROLE_DRIVER_APP.getCode(), reasonId: ReassignTaskEnum.DispatchReasonIdEnum.FLIGHT_DELAY_ONE_HOUR.getCode(), newRights: 1, reasonDetailId: ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode())
        def dto2 = executor.specialLogBuild(command2, baseDetailVO, logContentDTO, keyMaps)

        then: "验证校验结果"

        then: "验证校验结果"
        dto == null
        dto1 == null
        dto2 == null
    }

    def "test mappingRoles"() {
        given: "Mock数据"

        ReDispatchSubmitCommand command = new ReDispatchSubmitCommand(drvId: 1L, roleId: roleId, userName: userName, supplierId: supplierId, reasonId: ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode(), newRights: 1, reasonDetailId: ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode())

        when: "执行校验方法"
        def role = executor.mappingRoles(command)

        then: "验证校验结果"
        role == result

        where:
        roleId | userName | supplierId || result
        0      | ""       | 1          || "系统"
        1      | ""       | 1          || "司机{1}"
        2      | "test"   | 1          || "供应商{test}"
        2      | ""       | 1          || "供应商{1}"
        3      | ""       | 1          || "客服"
        4      | ""       | 1          || "供应商{1}"
        5      | ""       | 1          || "用户"
        6      | ""       | 1          || "运营"
        7      | ""       | 1          || "系统"
        8      | ""       | 1          || "客服"

    }

    def "test buildSnapshotInfo"() {
        given: "Mock数据"
        ReDispatchSubmitContext.newContext().withFlightVO(flightVO).withDriverToPointEstimateDataVO(new DriverToPointEstimateDataVO(pointDTO, 1L, distance, duration)).init()
        ReDispatchSubmitCommand command = new ReDispatchSubmitCommand(drvId: drvId, roleId: roleId, userName: "userName", supplierId: 1, reasonId: ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode(), newRights: 1, reasonDetailId: ReassignTaskEnum.DispatchReasonIdEnum.ORDER_CONFLICT.getCode())
        BaseDetailVO baseDetailVO = new BaseDetailVO(drvId: drvId, orderStatus: 300, bizAreaType: 32, categoryCode: "airport_dropoff", supplierId: 1105877, estimatedUseTimeBj: DateUtil.formatDate(DateUtil.addHours(new Date(), 72), DateUtil.DATETIME_FORMAT))
        when(flightVO.getEstimateArriveDate()).thenReturn(flight)

        when: "执行校验方法"
        def str = executor.buildSnapshotInfo(command, baseDetailVO)

        then: "验证校验结果"
        StringUtils.isNotBlank(str) == success

        where:
        flight | roleId | drvId | pointDTO                                                                         | distance | duration || success
        null   | 7      | 1L    | null                                                                             | 1        | 1         | true
        null   | 1      | null  | null                                                                             | 1        | 1         | true
        null   | 1      | 1     | null                                                                             | 1        | 1         | true
        null   | 1      | 1     | null                                                                             | null     | 1         | true
        null   | 1      | 1     | null                                                                             | 1        | null      | true
        "123"  | 1      | null  | null                                                                             | 1        | null      | true
        "123"  | 1      | 1L    | new DriverToPointEstimateDataVO.PointDTO(BigDecimal.ONE, BigDecimal.ONE, "", 1L) | 1        | 1         | true

    }


    ReDispatchSubmitCommand getReq1() {
        def cmd = getReq()
        return cmd

    }

    ReDispatchSubmitCommand getReq() {
        def request = new ReDispatchSubmitCommand(
                drvId: 3452787,
                roleId: 1,
                userOrderId: "36599208997",
                dspOrderId: "16211561979363356",
                reasonDetailId: 13,
                reasonId: "",
                bookTime: "",
                transportGroupId: 249,
                supplierId: 1105877

        )
        return request

    }

    Result getconvertCheckprm1() {
        return Result.Builder.newResult().fail().build()
    }

    Result getconvertCheckprm() {
        return Result.Builder.newResult().success().build()
    }

    Result<RightAndPointVO> getpoint2() {
        def detail = new RightAndPointVO(responsable: true, punishInfo: new PunishVO(punishRoleId: 1, punishPoint: BigDecimal.valueOf(100D), punishAmount: BigDecimal.valueOf(200D)))
        return Result.Builder.<ReasonDetailVO> newResult().success().withData(detail).build()
    }

    Result<RightAndPointVO> getpoint1() {
        def detail = new RightAndPointVO(reasonDetail: new ReasonDetailVO())
        return Result.Builder.<ReasonDetailVO> newResult().success().withData(detail).build()
    }

    Result<RightAndPointVO> getpoint() {
        return Result.Builder.<ReasonDetailVO> newResult().fail().build()
    }

    BaseDetailVO getBaseDetailVO4() {
        def base = getBaseDetailVO()
        base.setCategoryCode("day_rental")
        return base
    }

    BaseDetailVO getBaseDetailVO3() {
        def base = getBaseDetailVO()
        base.setBizAreaType(33)
        return base
    }

    BaseDetailVO getBaseDetailVO2() {
        def base = getBaseDetailVO()
        base.setOrderStatus(500)
        return base
    }

    BaseDetailVO getBaseDetailVO1() {
        def base = getBaseDetailVO()
        base.setDrvId(3452789)
        return base
    }

    BaseDetailVO getBaseDetailVO0() {
        def base = getBaseDetailVO()
        base.setSupplierId(1105878)
        return base
    }

    BaseDetailVO getBaseDetailVO() {
        def base = new BaseDetailVO(drvId: 3452787, orderStatus: 300, bizAreaType: 32, categoryCode: "airport_dropoff", supplierId: 1105877
                , estimatedUseTimeBj: DateUtil.formatDate(DateUtil.addHours(new Date(), 72), DateUtil.DATETIME_FORMAT))
        return base
    }

    DriverOrderVO getDriverOrderVO() {
        def order = new DriverOrderVO("D16211561979363356", "16211561979363356", 3452787)
        return order

    }


    CheckModel getCheckModel() {
        def check = new CheckModel(checkCode: CheckCode.PASS)
        return check
    }

    CheckModel getCheckModelEx1() {
        def check = new CheckModel(checkCode: CheckCode.NULL)
        return check
    }

    VehicleVO getVehicleVO() {

        def vehicle = new VehicleVO(1l, "carLicense", 1l, "carColor", 1l, "carBrandName", 1l, "carTypeName", 1l, "carSeriesName", 0,1, 1, "1",1)
        return vehicle
    }


    SubSkuVO getsubsku() {
        def subSku = SubSkuVO.builder().subSkuId(1).subSkuName("1").dspType(DspType.SYSTEM_ASSIGN).takenType(TakenType.ASSISTANT).retrySecond(30).build()
        return subSku
    }

    TransportGroupVO getTransportGroupVO() {
        def group = new TransportGroupVO(transportGroupId: 249L, transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN, transportGroupName: "运力组名称", informSwitch: 1, informEmail: "<EMAIL>", orderLimitConfigs: null,shortDisSwitch: 0)
        return group
    }

    TransportGroupVO getTransportGroupVOEx1() {
        def group = new TransportGroupVO(transportGroupId: 2491L, transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN, transportGroupName: "运力组名称", informSwitch: 1, informEmail: "<EMAIL>", orderLimitConfigs: null,shortDisSwitch: 0)
        return group
    }


    DspOrderConfirmRecordVO getDspOrderConfirmRecordVO() {
        def driverInfo = new DspOrderConfirmRecordVO.DriverRecord(driverId: 3452788, transportGroupId: 249L)
        def supplierInfo = new DspOrderConfirmRecordVO.SupplierRecord(1105877, 1, "xxx", "en")

        def confirm = new DspOrderConfirmRecordVO(driverInfo: driverInfo, supplierInfo: supplierInfo, confirmType: "20")
        return confirm
    }

    DspOrderConfirmRecordVO getDspOrderConfirmRecordVOEx1() {
        def driverInfo = new DspOrderConfirmRecordVO.DriverRecord(driverId: 3452788, transportGroupId: 249L)
        def supplierInfo = new DspOrderConfirmRecordVO.SupplierRecord(1105877, 1, "xxx", "en")

        def confirm = new DspOrderConfirmRecordVO(driverInfo: driverInfo, supplierInfo: supplierInfo, confirmType: "30")
        return confirm
    }

    DspOrderConfirmRecordVO getDspOrderConfirmRecordVOEx2() {
        def driverInfo = new DspOrderConfirmRecordVO.DriverRecord(driverId: 3452788, transportGroupId: 249L)
        def supplierInfo = new DspOrderConfirmRecordVO.SupplierRecord(7, 1, "xxx", "en")

        def confirm = new DspOrderConfirmRecordVO(driverInfo: driverInfo, supplierInfo: supplierInfo, confirmType: "20")
        return confirm
    }

    DspOrderConfirmRecordVO getDspOrderConfirmRecordVOEx3() {
        def driverInfo = new DspOrderConfirmRecordVO.DriverRecord(driverId: 3452787, transportGroupId: 249L)
        def supplierInfo = new DspOrderConfirmRecordVO.SupplierRecord(1105877, 1, "xxx", "en")

        def confirm = new DspOrderConfirmRecordVO(driverInfo: driverInfo, supplierInfo: supplierInfo, confirmType: "20")
        return confirm
    }


    DriverVO getDriverVO() {
        def supplier = new SupplierVO(1105877L, [1105877L])
        def car = new CarVO(carId: 1L, carLicense: "京xxxx", carColorId: 1L, carColor: "red", carBrandId: 1L, carBrandName: "carBrandName", carTypeId: 119L, carTypeName: "carTypeName", carSeriesId: 1L, carSeriesName: "carSeriesName", isEnergy: 0)

        def drv = new DriverVO(3452787, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [new TransportGroupVO(249, "transportGroupName", com.ctrip.dcs.domain.common.enums.TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1105877, 0, "informPhone", "informEmail", null)], car, supplier, 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", 0, 1l, null, com.ctrip.dcs.domain.common.enums.YesOrNo.NO, false,0)
        return drv
    }

    DriverVO getDriverVO1() {
        def supplier = new SupplierVO(1105877L, [1105877L])
        def car = new CarVO(carId: 1L, carLicense: "", carColorId: 1L, carColor: "red", carBrandId: 1L, carBrandName: "carBrandName", carTypeId: 119L, carTypeName: "carTypeName", carSeriesId: 1L, carSeriesName: "carSeriesName", isEnergy: 0)

        def drv = new DriverVO(3452787, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [new TransportGroupVO(249, "transportGroupName", com.ctrip.dcs.domain.common.enums.TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1105877, 0, "informPhone", "informEmail", null)], car, supplier, 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", 0, 1l, null, com.ctrip.dcs.domain.common.enums.YesOrNo.NO, false,0)
        return drv
    }


    DspOrderVO getDspOrderVO() {
        def order = new DspOrderVO(dspOrderId: "16211561979363356", productType: 0, countryId: 1L, cityId: 1, categoryCode: CategoryCodeEnum.TO_AIRPORT, spId: 1)
        return order
    }

    DspOrderVO getDspOrderVOEx1() {
        def order = new DspOrderVO(dspOrderId: "16211561979363356", productType: 1, countryId: 1L, cityId: 1, categoryCode: CategoryCodeEnum.TO_AIRPORT, spId: 1)
        return order
    }

    DspOrderVO getDspOrderVOEx2() {
        def order = new DspOrderVO(dspOrderId: "16211561979363356", productType: 0, countryId: 1L, cityId: 1, categoryCode: CategoryCodeEnum.DAY_RENTAL, spId: 1)
        return order
    }


    DspOrderDO getDspOrderDO() {
        def useDays = new UseDays(BigDecimal.valueOf(1))
        def dspOrderDO = new DspOrderDO(userOrderId: "36599208997", dspOrderId: "16211561979363356", categoryCode: "airport_dropoff", cityId: 2, fromCityId: 2, toCityId: 2, vehicleGroupId: 117
                , estimatedUseTime: new Timestamp(System.currentTimeMillis()), estimatedUseTimeBj: new Timestamp(System.currentTimeMillis())
                , predicServiceStopTime: new Timestamp(System.currentTimeMillis()), predicServiceStopTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTime: new Timestamp(System.currentTimeMillis()), lastConfirmTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTimeBj: new Timestamp(System.currentTimeMillis()), lastConfirmTime: new Timestamp(System.currentTimeMillis())
                , useDays: useDays, estimatedKm: BigDecimal.valueOf(100), estimatedMin: BigDecimal.valueOf(200), supplierId: 1105877, spId: 1000, confirmRecordId: 123
                , driverOrderId: "D16211561979363356")

        return dspOrderDO
    }

    DspOrderDO getDspOrderDOEx1() {
        def useDays = new UseDays(BigDecimal.valueOf(1))
        def dspOrderDO = new DspOrderDO(userOrderId: "36599208997", dspOrderId: "16211561979363356", categoryCode: "day_rental", cityId: 2, fromCityId: 2, toCityId: 2, vehicleGroupId: 117
                , estimatedUseTime: new Timestamp(System.currentTimeMillis()), estimatedUseTimeBj: new Timestamp(System.currentTimeMillis())
                , predicServiceStopTime: new Timestamp(System.currentTimeMillis()), predicServiceStopTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTime: new Timestamp(System.currentTimeMillis()), lastConfirmTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTimeBj: new Timestamp(System.currentTimeMillis()), lastConfirmTime: new Timestamp(System.currentTimeMillis())
                , useDays: useDays, estimatedKm: BigDecimal.valueOf(100), estimatedMin: BigDecimal.valueOf(200), supplierId: 1105877, spId: 1000, confirmRecordId: 123)

        return dspOrderDO
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme