package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.service.IGrabOrderPushRuleService
import com.ctrip.dcs.application.service.dto.GrabOrderPushRuleRecordDTO
import com.ctrip.dcs.application.service.dto.QueryGrabOrderPushRuleRecordParamDTO
import com.ctrip.dcs.application.service.dto.QueryGrabOrderPushRuleRecordResultDTO
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleRecordRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryGrabOrderPushRuleRecordResponseType
import com.ctrip.igt.PaginatorDTO
import spock.lang.Specification

import java.sql.Timestamp

/**
 * <AUTHOR>
 */
class QueryGrabOrderPushRuleRecordExecutorTest extends Specification {

    private IGrabOrderPushRuleService grabOrderPushRuleService = Mock(IGrabOrderPushRuleService);

    QueryGrabOrderPushRuleRecordExecutor executor = new QueryGrabOrderPushRuleRecordExecutor(grabOrderPushRuleService: grabOrderPushRuleService)

    def "Execute"() {
        given:
        QueryGrabOrderPushRuleRecordRequestType request = new QueryGrabOrderPushRuleRecordRequestType()
        request.setPaginator(new PaginatorDTO(1, 10))
        request.setRuleId(1L)
        GrabOrderPushRuleRecordDTO dto = new GrabOrderPushRuleRecordDTO()
        dto.setId(1L)
        dto.setRuleId(1L)
        dto.setOperatorName("operatorName")
        dto.setOperatorTime(new Timestamp(DateUtil.parseDateStr2Date("2024-12-18 12:00:00").getTime()))
        dto.setOperatorType(1)
        dto.setBeforeChange("a")
        dto.setAfterChange("b")
        dto.setCreateTime(new Timestamp(DateUtil.parseDateStr2Date("2024-12-18 12:00:00").getTime()))
        dto.setUpdateTime(new Timestamp(DateUtil.parseDateStr2Date("2024-12-18 12:00:00").getTime()))
        QueryGrabOrderPushRuleRecordResultDTO result = new QueryGrabOrderPushRuleRecordResultDTO()
        result.setRecordDTOList([dto])
        result.setPageSize(10)
        result.setPageNo(1)
        result.setTotalPages(1)
        result.setTotalSize(1)
        grabOrderPushRuleService.queryRuleRecord(_) >> result
        when:
        QueryGrabOrderPushRuleRecordResponseType response = executor.execute(request)
        then:
        with(response.getRuleRecords().getFirst()) {
            id == 1L
            ruleId == 1L
            operatorName == "operatorName"
            operatorTime == "2024-12-18 12:00:00"
            operatorType == 1
            beforeChange == "a"
            afterChange == "b"
        }
    }
}
