package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.DriverOrderConfirmFailCommand
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DriverOrderConfirmFailExeCmdTest extends Specification {
    @Mock
    DriverOrderGateway driverOrderGateway
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    DspOrderVO dspOrderVO
    @InjectMocks
    DriverOrderConfirmFailExeCmd driverOrderConfirmFailExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        DriverOrderConfirmFailCommand command = new DriverOrderConfirmFailCommand("driverOrderId", "dspOrderId", 1l)
        when:
        driverOrderConfirmFailExeCmd.execute(command)

        then:
        command.getDriverOrderId() == "driverOrderId"
    }

    def "test execute same"() {
        given:
        DriverOrderConfirmFailCommand command = new DriverOrderConfirmFailCommand("driverOrderId", "dspOrderId", 1l)
        Mockito.when(queryDspOrderService.query(Mockito.anyString())).thenReturn(dspOrderVO)
        Mockito.when(dspOrderVO.getDriverOrderId()).thenReturn("driverOrderId")

        when:
        driverOrderConfirmFailExeCmd.execute(command)

        then:
        command.getDriverOrderId() == "driverOrderId"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme