package com.ctrip.dcs.application.listener.grab

import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository
import com.ctrip.igt.framework.common.clogging.Logger
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class VBKGrabFinishTaskListenerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository
    @Mock
    MessageProviderService messageProducer
    @Mock
    BaseMessage message
    @InjectMocks
    VBKGrabFinishTaskListener vBKGrabFinishTaskListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message"() {
        given:
        when(message.times()).thenReturn(times)
        when(message.getStringProperty("taskIdStr")).thenReturn(taskId)
        when(message.getIntProperty("grabStatus")).thenReturn(grabStatus)
        when(vbkDriverGrabTaskRepository.finishTaskBatch(any(), anyInt())).thenReturn(0)

        when:
        def result = vBKGrabFinishTaskListener.onMessage(message)

        then:
        result == code

        where:
        times | taskId | grabStatus || code
        21    | ""     | 0          || null
        0     | ""     | 0          || null
        0     | "123"  | 0          || null
        0     | "123"  | 1          || null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme