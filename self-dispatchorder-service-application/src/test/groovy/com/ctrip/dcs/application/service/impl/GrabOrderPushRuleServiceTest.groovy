package com.ctrip.dcs.application.service.impl

import com.ctrip.dcs.application.service.dto.GrabOrderPushRuleDTO
import com.ctrip.dcs.application.service.dto.GrabOrderPushRuleRecordDTO
import com.ctrip.dcs.application.service.dto.QueryGrabOrderPushRuleParamDTO
import com.ctrip.dcs.application.service.dto.QueryGrabOrderPushRuleResultDTO
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.dsporder.gateway.ScmMerchantServiceGateway
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.GrabOrderPushRuleDao
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.GrabOrderPushRuleRecordDao
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabOrderPushRulePO
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabOrderPushRuleRecordPO
import com.ctrip.dcs.infrastructure.adapter.soa.RhbSettlementRuleProxy
import com.ctrip.dcs.infrastructure.adapter.soa.ScmMerchantServiceProxy
import com.ctrip.dcs.scm.merchant.interfaces.message.QueryCurrencyPaymentForDriverResponseType
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.dcs.settlement.interfaces.dto.QuerySettleRuleVBKResponseType
import com.ctrip.dcs.settlement.interfaces.dto.SettleRuleInfo
import org.checkerframework.checker.units.qual.C
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class GrabOrderPushRuleServiceTest extends Specification {

    DistributedLockService.DistributedLock lock = Mock(DistributedLockService.DistributedLock)

    private GrabOrderPushRuleDao grabOrderPushRuleDao = Mock(GrabOrderPushRuleDao);

    private GrabOrderPushRuleRecordDao recordDao = Mock(GrabOrderPushRuleRecordDao);

    private MessageProviderService messageProviderService = Mock(MessageProviderService);

    protected DistributedLockService distributedLockService = Mock(DistributedLockService);

    private ScmMerchantServiceProxy scmMerchantServiceProxy = Mock(ScmMerchantServiceProxy);

    private RhbSettlementRuleProxy rhbSettlementRuleProxy = Mock(RhbSettlementRuleProxy);

    private ScmMerchantServiceGateway scmMerchantServiceGateway = Mock(ScmMerchantServiceGateway);

    GrabOrderPushRuleService grabOrderPushRuleService = new GrabOrderPushRuleService(
        grabOrderPushRuleDao : grabOrderPushRuleDao,
        recordDao : recordDao,
        messageProviderService : messageProviderService,
        scmMerchantServiceProxy : scmMerchantServiceProxy,
        rhbSettlementRuleProxy : rhbSettlementRuleProxy,
        distributedLockService : distributedLockService,
        scmMerchantServiceGateway : scmMerchantServiceGateway
    )

    def "Add"() {
        given:
        GrabOrderPushRuleDTO dto = new GrabOrderPushRuleDTO()
        dto.setId(1L)
        dto.setSupplierId(1L)
        dto.setCityIds([1L])
        dto.setCategoryCode(ParentCategoryEnum.JNT.getCode())
        dto.setSupplierName("a")
        dto.setVehicleGroupIdList([117L])
        dto.setRuleType(1)
        dto.setImmediatePushTime(1)
        dto.setFixedPushTime(null)
        dto.setStartBookTime("08:00")
        dto.setEndBookTime("08:00")
        dto.setBookTime("08:00")
        dto.setOperateUser("")
        dto.setDeleteFlag(1)

        GrabOrderPushRulePO po = new GrabOrderPushRulePO()
        po.setId(1L)
        po.setSupplierId(1L)
        po.setCityId(1L)
        po.setCategoryCode(ParentCategoryEnum.JNT.getCode())
        po.setSupplierName("a")
        po.setVehicleGroupIdList("117")
        po.setRuleType(1)
        po.setImmediatePushTime(1)
        po.setFixedPushTime(null)
        po.setStartBookTime("08:00")
        po.setEndBookTime("08:00")
        po.setBookTime("08:00")

        QueryGrabOrderPushRuleParamDTO param = new QueryGrabOrderPushRuleParamDTO()
        param.setSupplierId(1L)
        param.setCityId(1L)
        param.setVehicleGroupId(117L)
        param.setPageNo(1)
        param.setPageSize(10)
        distributedLockService.getLock("grabRule1") >> lock
        grabOrderPushRuleDao.queryRules(1L, [1L], ParentCategoryEnum.JNT.getCode()) >> []
        lock.tryLock() >> tryLock
        grabOrderPushRuleDao.add(_) >> add
        scmMerchantServiceProxy.queryCurrencyPaymentForDriver(_) >> new QueryCurrencyPaymentForDriverResponseType(currency: "CNY")
        rhbSettlementRuleProxy.querySettleRules(_) >> new QuerySettleRuleVBKResponseType(ruleList: [new SettleRuleInfo(carType: 117, categoryCode: CategoryCodeEnum.FROM_AIRPORT.getType(), supplierId: 0)])
        scmMerchantServiceGateway.isPayForDriver(1L, CategoryCodeEnum.getByParentType(ParentCategoryEnum.JNT.getCode())) >> true

        when:
        boolean result = grabOrderPushRuleService.add(dto)
        then:
        result == flag
        where:
        tryLock | add   || flag
        true    | false || false
        true    | true  || true

    }

    def "DeleteById"() {
        given:
        GrabOrderPushRuleDTO dto = new GrabOrderPushRuleDTO()
        dto.setId(1L)
        dto.setSupplierId(1L)
        dto.setCityIds([1L])
        dto.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        dto.setSupplierName("a")
        dto.setVehicleGroupIdList([117L])
        dto.setRuleType(1)
        dto.setImmediatePushTime(1)
        dto.setFixedPushTime(null)
        dto.setStartBookTime("08:00")
        dto.setEndBookTime("08:00")
        dto.setBookTime("08:00")
        dto.setOperateUser("")
        dto.setDeleteFlag(1)

        GrabOrderPushRulePO po = new GrabOrderPushRulePO()
        po.setId(1L)
        po.setSupplierId(1L)
        po.setCityId(1L)
        po.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        po.setSupplierName("a")
        po.setVehicleGroupIdList("117")
        po.setRuleType(1)
        po.setImmediatePushTime(1)
        po.setFixedPushTime(null)
        po.setStartBookTime("08:00")
        po.setEndBookTime("08:00")
        po.setBookTime("08:00")

        QueryGrabOrderPushRuleParamDTO param = new QueryGrabOrderPushRuleParamDTO()
        param.setSupplierId(1L)
        param.setCityId(1L)
        param.setVehicleGroupId(117L)
        param.setPageNo(1)
        param.setPageSize(10)
        distributedLockService.getLock("grabRule1") >> lock
        grabOrderPushRuleDao.queryById(1L) >> po
        lock.tryLock() >> tryLock
        grabOrderPushRuleDao.deleteById(1L) >> del

        when:
        boolean result = grabOrderPushRuleService.deleteById(1L, "", 1L)
        then:
        result == flag
        where:
        tryLock | del   || flag
        false   | false || false
        true    | false || false
        true    | true  || true

    }

    def "Update"() {
        given:
        GrabOrderPushRuleDTO dto = new GrabOrderPushRuleDTO()
        dto.setId(1L)
        dto.setSupplierId(1L)
        dto.setCityId(1L)
        dto.setCityIds([1L])
        dto.setCategoryCode(ParentCategoryEnum.JNT.getCode())
        dto.setSupplierName("a")
        dto.setVehicleGroupIdList([117L])
        dto.setRuleType(1)
        dto.setImmediatePushTime(1)
        dto.setFixedPushTime(null)
        dto.setStartBookTime("08:00")
        dto.setEndBookTime("08:00")
        dto.setBookTime("08:00")
        dto.setOperateUser("")
        dto.setDeleteFlag(1)

        GrabOrderPushRulePO po = new GrabOrderPushRulePO()
        po.setId(1L)
        po.setSupplierId(1L)
        po.setCityId(1L)
        po.setCategoryCode(ParentCategoryEnum.JNT.getCode())
        po.setSupplierName("a")
        po.setVehicleGroupIdList("117")
        po.setRuleType(1)
        po.setImmediatePushTime(1)
        po.setFixedPushTime(null)
        po.setStartBookTime("08:00")
        po.setEndBookTime("08:00")
        po.setBookTime("08:00")

        QueryGrabOrderPushRuleParamDTO param = new QueryGrabOrderPushRuleParamDTO()
        param.setSupplierId(1L)
        param.setCityId(1L)
        param.setVehicleGroupId(117L)
        param.setPageNo(1)
        param.setPageSize(10)
        distributedLockService.getLock("grabRule1") >> lock
        grabOrderPushRuleDao.queryById(1L) >> po
        lock.tryLock() >> tryLock
        grabOrderPushRuleDao.update(_) >> update
        scmMerchantServiceProxy.queryCurrencyPaymentForDriver(_) >> new QueryCurrencyPaymentForDriverResponseType(currency: "CNY")
        rhbSettlementRuleProxy.querySettleRules(_) >> new QuerySettleRuleVBKResponseType(ruleList: [new SettleRuleInfo(carType: 117, categoryCode: CategoryCodeEnum.FROM_AIRPORT.getType(), supplierId: 1L)])
        grabOrderPushRuleDao.queryRules(1L, [1L], ParentCategoryEnum.JNT.getCode()) >> []
        scmMerchantServiceGateway.isPayForDriver(1L, CategoryCodeEnum.getByParentType(ParentCategoryEnum.JNT.getCode())) >> true

        when:
        boolean result = grabOrderPushRuleService.update(dto)
        then:
        result == flag
        where:
        tryLock | update   || flag
        false   | false || false
        true    | false || false
        true    | true  || true
    }

    def "Query"() {
        given:
        GrabOrderPushRulePO dto = new GrabOrderPushRulePO()
        dto.setId(1L)
        dto.setSupplierId(1L)
        dto.setCityId(1L)
        dto.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        dto.setSupplierName("a")
        dto.setVehicleGroupIdList("117")
        dto.setRuleType(1)
        dto.setImmediatePushTime(1)
        dto.setFixedPushTime(null)
        dto.setStartBookTime("08:00")
        dto.setEndBookTime("08:00")
        dto.setBookTime("08:00")
        QueryGrabOrderPushRuleParamDTO param = new QueryGrabOrderPushRuleParamDTO()
        param.setSupplierId(1L)
        param.setCityId(1L)
        param.setVehicleGroupId(117L)
        param.setPageNo(1)
        param.setPageSize(10)

        grabOrderPushRuleDao.count(1L, 1L, 117L) >> 1
        grabOrderPushRuleDao.queryByPage(1L, 1L, 117L, 1, 10) >> [dto]
        when:
        QueryGrabOrderPushRuleResultDTO result = grabOrderPushRuleService.query(param)
        then:
        with(result.getRuleDTOList().getFirst()) {
            id == 1L
            supplierId == 1L
            cityIds == [1L]
            categoryCode == CategoryCodeEnum.FROM_AIRPORT.getType()
            supplierName == "a"
            vehicleGroupIdList == [117L]
            ruleType == 1
            immediatePushTime == 1
            startBookTime == "08:00"
            endBookTime == "08:00"
            fixedPushTime == null
        }
    }

    def "ConvertPo"() {
        given:
        GrabOrderPushRuleDTO dto = new GrabOrderPushRuleDTO()
        dto.setId(1L)
        dto.setSupplierId(1L)
        dto.setCityId(1L)
        dto.setCityIds([1L])
        dto.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        dto.setSupplierName("a")
        dto.setVehicleGroupIdList([117L])
        dto.setRuleType(1)
        dto.setImmediatePushTime(1)
        dto.setFixedPushTime(null)
        dto.setStartBookTime("08:00")
        dto.setEndBookTime("08:00")
        dto.setBookTime("08:00")
        dto.setOperateUser("")
        dto.setDeleteFlag(1)

        when:
        GrabOrderPushRulePO result = grabOrderPushRuleService.convertPo(dto)
        then:
        with(result) {
            id == 1L
            supplierId == 1L
            cityId == 1L
            categoryCode == CategoryCodeEnum.FROM_AIRPORT.getType()
            supplierName == "a"
            vehicleGroupIdList == ",117,"
            ruleType == 1
            immediatePushTime == 1
            startBookTime == ""
            endBookTime == ""
            fixedPushTime == null
        }
    }

    def "ConvertDTOList"() {
        given:
        GrabOrderPushRulePO dto = new GrabOrderPushRulePO()
        dto.setId(1L)
        dto.setSupplierId(1L)
        dto.setCityId(1L)
        dto.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        dto.setSupplierName("a")
        dto.setVehicleGroupIdList("117")
        dto.setRuleType(1)
        dto.setImmediatePushTime(1)
        dto.setFixedPushTime(null)
        dto.setStartBookTime("08:00")
        dto.setEndBookTime("08:00")
        dto.setBookTime("08:00")

        when:
        List<GrabOrderPushRuleDTO> result = grabOrderPushRuleService.convertDTOList([dto])
        then:
        with(result.getFirst()) {
            id == 1L
            supplierId == 1L
            cityIds == [1L]
            categoryCode == CategoryCodeEnum.FROM_AIRPORT.getType()
            supplierName == "a"
            vehicleGroupIdList == [117L]
            ruleType == 1
            immediatePushTime == 1
            startBookTime == "08:00"
            endBookTime == "08:00"
            fixedPushTime == null
        }
    }

    def "ConvertToDTO"() {
        given:
        GrabOrderPushRulePO dto = new GrabOrderPushRulePO()
        dto.setId(1L)
        dto.setSupplierId(1L)
        dto.setCityId(1L)
        dto.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        dto.setSupplierName("a")
        dto.setVehicleGroupIdList("117")
        dto.setRuleType(1)
        dto.setImmediatePushTime(1)
        dto.setFixedPushTime(null)
        dto.setStartBookTime("08:00")
        dto.setEndBookTime("08:00")
        dto.setBookTime("08:00")

        when:
        GrabOrderPushRuleDTO result = grabOrderPushRuleService.convertToDTO(dto)
        then:
        with(result) {
            id == 1L
            supplierId == 1L
            cityIds == [1L]
            categoryCode == CategoryCodeEnum.FROM_AIRPORT.getType()
            supplierName == "a"
            vehicleGroupIdList == [117L]
            ruleType == 1
            immediatePushTime == 1
            startBookTime == "08:00"
            endBookTime == "08:00"
            fixedPushTime == null
        }
    }


    def "ConvertToRecordDTO"() {
        given:
        GrabOrderPushRuleRecordPO dto = new GrabOrderPushRuleRecordPO()
        dto.setId(1L)
        dto.setRuleId(1L)
        dto.setOperatorName("a")
        dto.setOperatorType(1)
        dto.setBeforeChange("b")
        dto.setAfterChange("c")

        when:
        GrabOrderPushRuleRecordDTO result = grabOrderPushRuleService.convertToRecordDTO(dto)
        then:
        with(result) {
            ruleId == 1L
            operatorName == "a"
            operatorType == 1
            beforeChange == "b"
            afterChange == "c"
        }
    }

    def "EqualVehicleGroupId"() {
        given:
        when:
        boolean result = grabOrderPushRuleService.equalVehicleGroupId("118,119", [118L])
        then:
        result == true
    }

    def "ConvertToRecordPoForAdd"() {
        given:
        GrabOrderPushRulePO po = new GrabOrderPushRulePO()
        po.setId(1L)
        po.setSupplierId(1L)
        po.setCityId(1L)
        po.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        po.setSupplierName("a")
        po.setVehicleGroupIdList("")
        po.setRuleType(1)
        po.setImmediatePushTime(1)
        po.setStartBookTime("08:00")
        po.setEndBookTime("08:00")
        po.setFixedPushTime(null)
        po.setIdDel(1)
        when:
        List<GrabOrderPushRuleRecordPO> result = grabOrderPushRuleService.convertToRecordPoForAdd([po],  "a")
        then:
        with(result.getFirst()) {
            ruleId == 1L
            operatorType == 0
        }
    }

    def "ConvertToRecordPoForUpdate"() {
        given:
        GrabOrderPushRulePO po = new GrabOrderPushRulePO()
        po.setId(1L)
        po.setSupplierId(1L)
        po.setCityId(1L)
        po.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        po.setSupplierName("a")
        po.setVehicleGroupIdList("")
        po.setRuleType(1)
        po.setImmediatePushTime(1)
        po.setStartBookTime("08:00")
        po.setEndBookTime("08:00")
        po.setFixedPushTime(null)
        po.setIdDel(1)
        when:
        List<GrabOrderPushRuleRecordPO> result = grabOrderPushRuleService.convertToRecordPoForUpdate(po, po, "a")
        then:
        with(result.getFirst()) {
            ruleId == 1L
            operatorType == 1
        }
    }

    def "ConvertToRecordPoForDelete"() {
        given:
        GrabOrderPushRulePO po = new GrabOrderPushRulePO()
        po.setId(1L)
        po.setSupplierId(1L)
        po.setCityId(1L)
        po.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        po.setSupplierName("a")
        po.setVehicleGroupIdList("")
        po.setRuleType(1)
        po.setImmediatePushTime(1)
        po.setStartBookTime("08:00")
        po.setEndBookTime("08:00")
        po.setFixedPushTime(null)
        po.setIdDel(1)
        when:
        List<GrabOrderPushRuleRecordPO> result = grabOrderPushRuleService.convertToRecordPoForDelete(po, "a")
        then:
        with(result.getFirst()) {
            ruleId == 1L
            operatorType == 2
        }
    }
}
