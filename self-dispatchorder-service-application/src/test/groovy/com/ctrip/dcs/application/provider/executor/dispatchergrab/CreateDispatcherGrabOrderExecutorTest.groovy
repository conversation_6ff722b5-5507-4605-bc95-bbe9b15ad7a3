package com.ctrip.dcs.application.provider.executor.dispatchergrab

import com.ctrip.dcs.application.command.dispatchergrab.CreateDispatcherGrabOrderExeCmd
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDispatcherGrabOrdersResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDispatcherGrabOrdersRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDispatcherGrabOrdersResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.DispatcherGrabOrderDTO
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class CreateDispatcherGrabOrderExecutorTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CreateDispatcherGrabOrderExeCmd cmd
    @Mock
    CreateDispatcherGrabOrdersRequestType request
    @InjectMocks
    CreateDispatcherGrabOrderExecutor createDispatcherGrabOrderExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(request.getOrders()).thenReturn([new DispatcherGrabOrderDTO()])
        when:
        CreateDispatcherGrabOrdersResponseType result = createDispatcherGrabOrderExecutor.execute(request)

        then:
        result.getResponseResult().isSuccess()

    }

    def "test execute fail"() {
        given:
        when(request.getOrders()).thenReturn(order)
        when(cmd.execute(any())).thenThrow(error)
        when:
        CreateDispatcherGrabOrdersResponseType result = createDispatcherGrabOrderExecutor.execute(request)

        then:
        result.getResponseResult().getReturnCode() == code

        where:
        order                          | error                                                    || code
        []                             | ErrorCode.ERROR_PARAMS.getBizException()                 || ErrorCode.ERROR_PARAMS.getCode()
        [new DispatcherGrabOrderDTO()] | ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getBizException() || ErrorCode.NO_DISPATCH_GRAB_ORDER_ERROR.getCode()
        [new DispatcherGrabOrderDTO()] | new RuntimeException()                                   || "500"

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme