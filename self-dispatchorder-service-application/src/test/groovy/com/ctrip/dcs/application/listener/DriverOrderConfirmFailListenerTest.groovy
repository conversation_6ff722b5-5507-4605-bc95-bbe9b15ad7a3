package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.command.DriverOrderConfirmFailExeCmd
import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import qunar.tc.qmq.NeedRetryException
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DriverOrderConfirmFailListenerTest extends Specification {

    DriverOrderConfirmFailExeCmd cmd = Mock(DriverOrderConfirmFailExeCmd)

    DriverOrderConfirmFailListener listener = new DriverOrderConfirmFailListener()

    def "OnMessageCancel"() {
        when:
        listener.onMessageCancel(new BaseMessage())
        then:
        def e = thrown(NeedRetryException)
        e.getNext() > 0
    }
}
