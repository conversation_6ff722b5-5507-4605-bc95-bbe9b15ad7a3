package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.dto.ModifyAddressDTO
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService
import com.ctrip.dcs.domain.common.service.SendEmailService
import com.ctrip.dcs.domain.common.value.ModifyCharteredLineOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderFeeDO
import com.ctrip.dcs.domain.dsporder.entity.EmailPushModifyCharteredLineOrderRemindRecord
import com.ctrip.dcs.domain.dsporder.entity.UserOrderDetail
import com.ctrip.dcs.domain.dsporder.entity.extend.AmountItemDO
import com.ctrip.dcs.domain.dsporder.entity.extend.DspOrderFeeExtendInfoDO
import com.ctrip.dcs.domain.dsporder.entity.extend.LineOrderExtendDO
import com.ctrip.dcs.domain.dsporder.entity.line.DayModifyHistoryInnerDTO
import com.ctrip.dcs.domain.dsporder.entity.line.DayModifyInfoInnerDTO
import com.ctrip.dcs.domain.dsporder.factory.PushOrderRemindRecordFactory
import com.ctrip.dcs.domain.dsporder.gateway.DCSOrderPaymentGateway
import com.ctrip.dcs.domain.dsporder.gateway.DateZoneConvertGateway
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway
import com.ctrip.dcs.domain.dsporder.gateway.VBKOperationRecordGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderExtendAttributeRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderFeeRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderUseTimeAndStopTimeAndAddressVO
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.DspOrderConfirmRecordDao
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.DspOrderConfirmRecordPO
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig
import com.ctrip.dcs.self.dispatchorder.interfaces.AddressInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.AmountInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.AmountItem
import com.ctrip.dcs.self.dispatchorder.interfaces.ModifyCharteredLineOrderRequestType
import com.ctrip.igt.RequestHeader
import com.ctrip.platform.dal.dao.helper.JsonUtils
import credis.java.client.CacheProvider
import org.junit.Assert
import spock.lang.*
import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*


class ModifyCharteredLineOrderExeCmdTest extends Specification {
    def testObj = new ModifyCharteredLineOrderExeCmd()
    def dspOrderRepository = Mock(DspOrderRepository)
    def extendAttributeRepository = Mock(DspOrderExtendAttributeRepository)
    def distributedLockService = Mock(DistributedLockService)
    def orderFeeRepository = Mock(DspOrderFeeRepository)
    def messageProvider = Mock(MessageProviderService)
    def vbkOperationRecordGateway = Mock(VBKOperationRecordGateway)
    def dateZoneConvertGateway = Mock(DateZoneConvertGateway)
    def igtOrderQueryServiceGateway = Mock(IGTOrderQueryServiceGateway)
    def sendEmailService = Mock(SendEmailService)
    def pushOrderRemindRecordFactory = Mock(PushOrderRemindRecordFactory)
    def queryTransportGroupService = Mock(QueryTransportGroupService)
    def confirmRecordDao = Mock(DspOrderConfirmRecordDao)
    def businessTemplateInfoConfig = Mock(BusinessTemplateInfoConfig)
    def dcsOrderPaymentGateway = Mock(DCSOrderPaymentGateway)

    def setup() {

        testObj.dspOrderRepository = dspOrderRepository
        testObj.extendAttributeRepository = extendAttributeRepository
        testObj.distributedLockService = distributedLockService
        testObj.orderFeeRepository = orderFeeRepository
        testObj.messageProvider = messageProvider
        testObj.vbkOperationRecordGateway = vbkOperationRecordGateway
        testObj.dateZoneConvertGateway = dateZoneConvertGateway
        testObj.igtOrderQueryServiceGateway = igtOrderQueryServiceGateway
        testObj.sendEmailService = sendEmailService
        testObj.pushOrderRemindRecordFactory = pushOrderRemindRecordFactory
        testObj.queryTransportGroupService = queryTransportGroupService
        testObj.confirmRecordDao = confirmRecordDao
        testObj.businessTemplateInfoConfig = businessTemplateInfoConfig
        testObj.dcsOrderPaymentGateway = dcsOrderPaymentGateway
    }


    @Unroll
    def "modifyCharteredLineOrderTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        and: "Mock相关接口返回"
        dspOrderRepository.queryValidDspOrders(_) >> queryValidDspOrders
        dspOrderRepository.updateDspEstimateUseTimeAndStopTimeAndAddressInfo(_) >> 1
        dspOrderRepository.updateDspEstimateUseTimeAndStopTimeAndAddressInfoAndLastConfirmCarTime(_) >> 0
        extendAttributeRepository.updateOrderExtendAttributeByDspOrderIdAndAttributeCode(_, _, _) >> updateOrderExtendAttributeByDspOrderIdAndAttributeCode
        orderFeeRepository.find(_) >> find
        orderFeeRepository.updateDspOrderFeeForExtendFeeInfo(_, _) >> 1
        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> null

        when:
        def result = spy.modifyCharteredLineOrder(requestType)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                 | queryValidDspOrders           | find                     | updateOrderExtendAttributeByDspOrderIdAndAttributeCode                              || expectedResult
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", uniqueKey: "modifyDay-****************-2") | [] | null | 0 || false
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", uniqueKey: "modifyDay-****************-2") | [new DspOrderDO(dspOrderId: "1", userOrderId: "1", orderStatus: 0)] |null | 0|| false
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", uniqueKey: "modifyDay-****************-2") | [new DspOrderDO(dspOrderId: "1", userOrderId: "1", orderStatus: 0)] |new DspOrderFeeDO(extendFeeInfo: "{\"lineOrderExtend\":[{\"cTime\":\"2025-01-02 13:30:56\",\"upTime\":\"2025-01-02 13:30:56\",\"amount\":-117,\"currency\":\"CNY\",\"status\":0,\"itemList\":[{\"code\":\"areaDiffAmount\",\"amount\":0},{\"code\":\"nightDiffAmount\",\"amount\":-117}],\"uniqueKey\":\"modifyDay-****************-2\"}]}") | 1 || true
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", uniqueKey: "modifyDay-****************-1",lastConfirmCarTime: "2024-12-12 12:12:12",lastConfirmCarTimeBJ:  "2024-12-12 12:12:12",fromAddressInfo: new AddressInfo(latitude: BigDecimal.ONE,longitude: BigDecimal.ONE),toAddressInfo: new AddressInfo(latitude: BigDecimal.ONE,longitude: BigDecimal.ONE)) | [new DspOrderDO(dspOrderId: "1", userOrderId: "1", orderStatus: 0)] |new DspOrderFeeDO(extendFeeInfo: "{\"lineOrderExtend\":[{\"cTime\":\"2025-01-02 13:30:56\",\"upTime\":\"2025-01-02 13:30:56\",\"amount\":-117,\"currency\":\"CNY\",\"status\":0,\"itemList\":[{\"code\":\"areaDiffAmount\",\"amount\":0},{\"code\":\"nightDiffAmount\",\"amount\":-117}],\"uniqueKey\":\"modifyDay-****************-2\"}]}") | 1 || false
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", uniqueKey: "modifyDay-****************-1",lastConfirmCarTime: "2024-12-12 12:12:12",fromAddressInfo: new AddressInfo(latitude: BigDecimal.ONE,longitude: BigDecimal.ONE),toAddressInfo: new AddressInfo(latitude: BigDecimal.ONE,longitude: BigDecimal.ONE),amountInfo: new AmountInfo(amount: BigDecimal.ONE,itemList: [new AmountItem(itemCode: "1",itemAmount: BigDecimal.ONE)])) | [new DspOrderDO(dspOrderId: "1", userOrderId: "1", orderStatus: 0)] |new DspOrderFeeDO(extendFeeInfo: "{\"lineOrderExtend\":[{\"cTime\":\"2025-01-02 13:30:56\",\"upTime\":\"2025-01-02 13:30:56\",\"amount\":-117,\"currency\":\"CNY\",\"status\":0,\"itemList\":[{\"code\":\"areaDiffAmount\",\"amount\":0},{\"code\":\"nightDiffAmount\",\"amount\":-117}],\"uniqueKey\":\"modifyDay-****************-2\"}]}")| 1 || true
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", uniqueKey: "modifyDay-****************-1",lastConfirmCarTime: "2024-12-12 12:12:12",fromAddressInfo: new AddressInfo(latitude: BigDecimal.ONE,longitude: BigDecimal.ONE),toAddressInfo: new AddressInfo(latitude: BigDecimal.ONE,longitude: BigDecimal.ONE),amountInfo: new AmountInfo(amount: BigDecimal.ONE,itemList: [new AmountItem(itemCode: "1",itemAmount: BigDecimal.ONE)])) | [new DspOrderDO(dspOrderId: "1", userOrderId: "1", orderStatus: 0)] |new DspOrderFeeDO(extendFeeInfo: "{\"lineOrderExtend\":[{\"cTime\":\"2025-01-02 13:30:56\",\"upTime\":\"2025-01-02 13:30:56\",\"amount\":-117,\"currency\":\"CNY\",\"status\":0,\"itemList\":[{\"code\":\"areaDiffAmount\",\"amount\":0},{\"code\":\"nightDiffAmount\",\"amount\":-117}],\"uniqueKey\":\"modifyDay-****************-2\"}]}")| 0 || false
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", uniqueKey: "modifyDay-****************-1",lastConfirmCarTime: "2024-12-12 12:12:12",fromAddressInfo: new AddressInfo(latitude: BigDecimal.ONE,longitude: BigDecimal.ONE),toAddressInfo: new AddressInfo(latitude: BigDecimal.ONE,longitude: BigDecimal.ONE),amountInfo: new AmountInfo(amount: BigDecimal.ONE,itemList: [new AmountItem(itemCode: "1",itemAmount: BigDecimal.ONE)])) | [new DspOrderDO(dspOrderId: "1", userOrderId: "1", orderStatus: 220)] |new DspOrderFeeDO(extendFeeInfo: "{\"lineOrderExtend\":[{\"cTime\":\"2025-01-02 13:30:56\",\"upTime\":\"2025-01-02 13:30:56\",\"amount\":-117,\"currency\":\"CNY\",\"status\":0,\"itemList\":[{\"code\":\"areaDiffAmount\",\"amount\":0},{\"code\":\"nightDiffAmount\",\"amount\":-117}],\"uniqueKey\":\"modifyDay-****************-2\"}]}")| 1 || true
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", uniqueKey: "modifyDay-****************-1",lastConfirmCarTime: "2024-12-12 12:12:12",fromAddressInfo: new AddressInfo(latitude: BigDecimal.ONE,longitude: BigDecimal.ONE),toAddressInfo: new AddressInfo(latitude: BigDecimal.ONE,longitude: BigDecimal.ONE),amountInfo: new AmountInfo(amount: BigDecimal.ONE,supplierAmount: BigDecimal.ONE,itemList: [new AmountItem(itemCode: "1",itemAmount: BigDecimal.ONE)])) | [new DspOrderDO(dspOrderId: "1", userOrderId: "1", orderStatus: 220)] |new DspOrderFeeDO(extendFeeInfo: "{\"lineOrderExtend\":[{\"cTime\":\"2025-01-02 13:30:56\",\"upTime\":\"2025-01-02 13:30:56\",\"amount\":-117,\"currency\":\"CNY\",\"status\":0,\"itemList\":[{\"code\":\"areaDiffAmount\",\"amount\":0},{\"code\":\"nightDiffAmount\",\"amount\":-117}],\"uniqueKey\":\"modifyDay-****************-2\"}]}")| 1 || true
        new ModifyCharteredLineOrderRequestType(userOrderId: "1", uniqueKey: "modifyDay-****************-1",lastConfirmCarTime: "2024-12-12 12:12:12",fromAddressInfo: new AddressInfo(latitude: BigDecimal.ONE,longitude: BigDecimal.ONE),toAddressInfo: new AddressInfo(latitude: BigDecimal.ONE,longitude: BigDecimal.ONE),amountInfo: new AmountInfo(amount: BigDecimal.ONE,supplierAmount: new BigDecimal("-1"),supplierCurrency:"CNY",itemList: [new AmountItem(itemCode: "1",itemAmount: BigDecimal.ONE)])) | [new DspOrderDO(dspOrderId: "1", userOrderId: "1", orderStatus: 220)] |new DspOrderFeeDO(extendFeeInfo: "{\"lineOrderExtend\":[{\"cTime\":\"2025-01-02 13:30:56\",\"upTime\":\"2025-01-02 13:30:56\",\"amount\":-117,\"currency\":\"CNY\",\"status\":0,\"itemList\":[{\"code\":\"areaDiffAmount\",\"amount\":0},{\"code\":\"nightDiffAmount\",\"amount\":-117}],\"uniqueKey\":\"modifyDay-****************-2\"}]}")| 1 || true
    }


    @Unroll
    def "judgeUniqueKeyTest"() {

        when:
        def result = testObj.judgeUniqueKey(dspOrderFeeDO, uniqueKey)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        uniqueKey   | dspOrderFeeDO                                     || expectedResult
        "modifyDay-****************-2" | new DspOrderFeeDO(extendFeeInfo: "{\"lineOrderExtend\":[{\"cTime\":\"2025-01-02 13:30:56\",\"upTime\":\"2025-01-02 13:30:56\",\"amount\":-117,\"currency\":\"CNY\",\"status\":0,\"itemList\":[{\"code\":\"areaDiffAmount\",\"amount\":0},{\"code\":\"nightDiffAmount\",\"amount\":-117}],\"uniqueKey\":\"modifyDay-****************-2\"}]}") || false
        "modifyDay-****************-1" | new DspOrderFeeDO(extendFeeInfo: "{\"lineOrderExtend\":[{\"cTime\":\"2025-01-02 13:30:56\",\"upTime\":\"2025-01-02 13:30:56\",\"amount\":-117,\"currency\":\"CNY\",\"status\":0,\"itemList\":[{\"code\":\"areaDiffAmount\",\"amount\":0},{\"code\":\"nightDiffAmount\",\"amount\":-117}],\"uniqueKey\":\"modifyDay-****************-2\"}]}") || true
        "modifyDay-****************-1" | new DspOrderFeeDO(extendFeeInfo: "{\"lineOrderExtend\":[]}") || true
        "modifyDay-****************-1" | new DspOrderFeeDO(dspOrderId: "111") || true
    }

    @Unroll
    def "buildModifyCharteredLineOrderVOTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        queryTransportGroupService.queryTransportGroup(_) >> new TransportGroupVO(transportGroupName: "1", informEmail: "1")
        confirmRecordDao.query(_) >> query


        when:
        def result = testObj.buildModifyCharteredLineOrderVO(dspOrderDO, requestType, buildModifyVO)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                                                                                                                    | buildModifyVO                                                                                                      | dspOrderDO                         |query || expectedResult
        new ModifyCharteredLineOrderRequestType(userOrderId: "userOrderId", amountInfo: new AmountInfo(amount: 0 as BigDecimal, currency: "currency")) | new DspOrderUseTimeAndStopTimeAndAddressVO(estimateUseTime: "estimateUseTime", fromAddressInfo: JsonUtils.toJson(new ModifyAddressDTO(actualFromCoordsys: "1"))) | new DspOrderDO(supplyOrderId: "1") | new DspOrderConfirmRecordPO(transportGroupId: 1L) || null
        new ModifyCharteredLineOrderRequestType(userOrderId: "userOrderId", amountInfo: new AmountInfo(amount: 0 as BigDecimal, currency: "currency")) | new DspOrderUseTimeAndStopTimeAndAddressVO(estimateUseTime: "estimateUseTime", fromAddressInfo: JsonUtils.toJson(new ModifyAddressDTO(actualFromCoordsys: "1"))) | new DspOrderDO(confirmRecordId: 1L) |null || null
    }

    @Unroll
    def "buildModifyCharteredLineOrderVOTest1"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        queryTransportGroupService.queryTransportGroup(_) >> new TransportGroupVO(transportGroupName: "1", informEmail: "1")
        confirmRecordDao.query(_) >> new DspOrderConfirmRecordPO(transportGroupId: 1L)
        ModifyCharteredLineOrderRequestType requestType =  new ModifyCharteredLineOrderRequestType(userOrderId: "userOrderId", amountInfo: new AmountInfo(amount: 0 as BigDecimal, currency: "currency"))

        DspOrderUseTimeAndStopTimeAndAddressVO buildModifyVO =  new DspOrderUseTimeAndStopTimeAndAddressVO(estimateUseTime: "estimateUseTime", fromAddressInfo: JsonUtils.toJson(new ModifyAddressDTO(actualFromCoordsys: "1")))
        DspOrderDO dspOrderDO = new DspOrderDO(confirmRecordId: 1L)

        when:
        ModifyCharteredLineOrderVO result = testObj.buildModifyCharteredLineOrderVO(dspOrderDO, requestType, buildModifyVO)

        then: "验证返回结果里属性值是否符合预期"
        Assert.assertTrue(result!= null)
    }


    @Unroll
    def "buildVBKWorkbenchLogForModifyTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        and: "Mock相关接口返回"
        dateZoneConvertGateway.getLocalTime(_, _) >> new Date()
        igtOrderQueryServiceGateway.queryUserOrderDetail(_) >> new UserOrderDetail(dayModifyInfoInnerDTO: new DayModifyInfoInnerDTO(modifyHistory: [new DayModifyHistoryInnerDTO(index: 0, beforAddress: "beforAddress", afterAddress: "afterAddress", beforTime: "2024-12-12 12:12:12", afterTime: "2024-12-12 12:12:12")]))
        businessTemplateInfoConfig.getValueByKey(_) >> "getValueByKeyResponse"

        and: "Spy相关接口"
        spy.transformTime(_) >> "transformTimeResponse"

        when:
        def result = spy.buildVBKWorkbenchLogForModify(dspOrderDO, requestType)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                                                                                        | dspOrderDO                                                                                                                                                    || expectedResult
        new ModifyCharteredLineOrderRequestType(amountInfo: new AmountInfo(amount: 0 as BigDecimal, currency: "currency")) | new DspOrderDO(dspOrderId: "1", userOrderId: "1", orderStatus: 0, cityId: 0, supplierId: 0, timeZone: 0 as BigDecimal, orderSourceCode: 0) || null
        new ModifyCharteredLineOrderRequestType(amountInfo: new AmountInfo(amount: 0 as BigDecimal, currency: "currency"), requestHeader: new RequestHeader(uid: "VBK_sysadmin")) | new DspOrderDO(dspOrderId: "1", userOrderId: "1", orderStatus: 0, cityId: 0, supplierId: 0, timeZone: 0 as BigDecimal, orderSourceCode: 0) || null
        new ModifyCharteredLineOrderRequestType(amountInfo: new AmountInfo(amount: 0 as BigDecimal, currency: "currency"), requestHeader: new RequestHeader(uid: "Account_ctripUser")) | new DspOrderDO(dspOrderId: "1", userOrderId: "1", orderStatus: 0, cityId: 0, supplierId: 0, timeZone: 0 as BigDecimal, orderSourceCode: 0) || null
    }

    @Unroll
    def "modifyCharteredLineOrderPriceStatusTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        and: "Mock相关接口返回"
        dspOrderRepository.queryValidDspOrders(_) >> queryValidDspOrders
        orderFeeRepository.find(_) >> new DspOrderFeeDO(extendFeeInfo: "{\"lineOrderExtend\":[{\"cTime\":\"2025-01-02 13:30:56\",\"upTime\":\"2025-01-02 13:30:56\",\"amount\":-117,\"currency\":\"CNY\",\"supplierAmount\":-117,\"supplierCurrency\":\"CNY\",\"status\":0,\"itemList\":[{\"code\":\"areaDiffAmount\",\"amount\":0},{\"code\":\"nightDiffAmount\",\"amount\":-117}],\"uniqueKey\":\"modifyDay-****************-2\"}]}")
        orderFeeRepository.updateDspOrderFeeForExtendFeeInfo(_,_) >> 1
        dcsOrderPaymentGateway.getOrderActualAmount(_) >> BigDecimal.ONE
        businessTemplateInfoConfig.getValueByKey(_) >> "111%s%s%s%s"
        dateZoneConvertGateway.getLocalTime(_,_) >> new Date()

        when:
        def result = spy.modifyCharteredLineOrderPriceStatus("1", 1)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
         queryValidDspOrders || expectedResult
          []                   || false
          [new DspOrderDO(dspOrderId: "111",orderStatus: 1,timeZone: BigDecimal.ONE)]                   || true
    }
}

