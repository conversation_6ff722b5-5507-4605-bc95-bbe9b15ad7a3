package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.*
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent
import com.ctrip.dcs.domain.dsporder.repository.PlatformPriceStrategyRepository
import com.ctrip.dcs.domain.dsporder.value.ConfirmType
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.factory.DriverOrderRewardFactory
import com.ctrip.dcs.domain.schedule.gateway.DriverOrderGateway
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.dcs.self.dispatchorder.interfaces.MockConfirmDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.MockConfirmDspOrderResponseType
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class MockConfirmDspOrderExecutorTest extends Specification {
    @Mock
    Logger logger
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    QueryDriverService queryDriverService
    @Mock
    QueryTransportGroupService queryTransportGroupService
    @Mock
    ConfirmDspOrderService confirmDspOrderService
    @Mock
    DriverOrderGateway driverOrderGateway
    @Mock
    QueryVehicleService queryVehicleService
    @Mock
    PlatformPriceStrategyRepository platformPriceStrategyRepository;
    @Mock
    BusinessTemplateInfoConfig businessTemplateInfoConfig;
    @Mock
    DriverOrderRewardFactory driverOrderRewardFactory;
    @InjectMocks
    MockConfirmDspOrderExecutor mockConfirmDspOrderExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(queryDspOrderService.query(anyString())).thenReturn(new DspOrderVO("supplyOrderId", "uid", "dspOrderId", "userOrderId", 0, "productName", CategoryCodeEnum.ALL, 0, 0, 0, "productCode", 0, new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), 0, 0, new UseDays(0 as BigDecimal), 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, "priceMark", 0, "spName", 0, "driverOrderId", 1l, 0, "targetId", "terminalId", 0, "depPoiCode", "depPoiSourceType", 0 as BigDecimal, 0 as BigDecimal, "actualFromCoordsys", "fromAddress", "fromName", "arrivePoiCode", "arrivePoiSourceType", 0 as BigDecimal, 0 as BigDecimal, "actualToCoordsys", "toAddress", "toName", 0, 0 as BigDecimal, "locale", "serviceLanguageCodes", 0, "cancelRule", "waitingRule", "priceResultCode", "detailSnapShotid", 0, [new XproductVO("xCategoryCode", 0, 0, 0 as BigDecimal, "supplierCurrency", 0, 0 as BigDecimal, 0 as BigDecimal, "userCurrency", "xProductName", "supplierCategoryCode")], "spContractInfo", "dspStrategyStr", 0, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, "supplierBornePrice", new SupplierBorneVO([new RenaissanceActivityVO(1l, "activityName", 0, "activityCode", 0 as BigDecimal, "activityDesc", 0 as BigDecimal)], [new TradeCouponVO("couponCode", 0 as BigDecimal)]), "premiumPrice", "userCurrency", "supplierCurrency", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0,1,1,1L))
        when(queryDriverService.queryDriver(anyLong(),any(), anyLong())).thenReturn(new DriverVO(1l, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [new TransportGroupVO(1l, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1l, 0, 0,"informPhone", "informEmail", null,"",null)], null, new SupplierVO(1L), 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", null, 0, 1l, null, YesOrNo.NO,false,0,1, "", "","","","",null))
        when(queryTransportGroupService.queryTransportGroup(anyLong())).thenReturn(new TransportGroupVO(1l, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1l, 0,0, "informPhone", "informEmail", null,"",null))
        when(driverOrderGateway.create(any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(),anyInt(),any(),any(),any(),any())).thenReturn("createResponse")
        when(queryVehicleService.query(anyLong(), Mockito.any())).thenReturn(new VehicleVO(1l, "carLicense", 1l, "carColor", 1l, "carBrandName", 1l, "carTypeName", 1l, "carSeriesName", 0,1,1,"desc",1))

        when:
        MockConfirmDspOrderResponseType result = mockConfirmDspOrderExecutor.execute(new MockConfirmDspOrderRequestType(dspOrderId:"1", driverId: 1L, transportGroupId: 1L, type: ConfirmType.SERVICE_PROVIDER_CONFIRMED.getStatus(), event: OrderStatusEvent.SYSTEM_ASSIGN.getCode()))

        then:
        result != null
    }

    def "test execute 1"() {
        given:
        when(queryDspOrderService.query(anyString())).thenReturn(new DspOrderVO("supplyOrderId", "uid", "dspOrderId", "userOrderId", 0, "productName", CategoryCodeEnum.ALL, 0, 0, 0, "productCode", 0, new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), 0, 0, new UseDays(0 as BigDecimal), 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, "priceMark", 0, "spName", 0, "driverOrderId", 1l, 0, "targetId", "terminalId", 0, "depPoiCode", "depPoiSourceType", 0 as BigDecimal, 0 as BigDecimal, "actualFromCoordsys", "fromAddress", "fromName", "arrivePoiCode", "arrivePoiSourceType", 0 as BigDecimal, 0 as BigDecimal, "actualToCoordsys", "toAddress", "toName", 0, 0 as BigDecimal, "locale", "serviceLanguageCodes", 0, "cancelRule", "waitingRule", "priceResultCode", "detailSnapShotid", 0, [new XproductVO("xCategoryCode", 0, 0, 0 as BigDecimal, "supplierCurrency", 0, 0 as BigDecimal, 0 as BigDecimal, "userCurrency", "xProductName", "supplierCategoryCode")], "spContractInfo", "dspStrategyStr", 0, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, "supplierBornePrice", new SupplierBorneVO([new RenaissanceActivityVO(1l, "activityName", 0, "activityCode", 0 as BigDecimal, "activityDesc", 0 as BigDecimal)], [new TradeCouponVO("couponCode", 0 as BigDecimal)]), "premiumPrice", "userCurrency", "supplierCurrency", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0,1,1,1L))
        when(queryDriverService.queryDriver(anyLong(),any(), anyLong())).thenReturn(new DriverVO(1l, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [new TransportGroupVO(1l, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1l, 0,0, "informPhone", "informEmail", null,"",null)], null, new SupplierVO(1L), 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", null,0, 1l, null, YesOrNo.NO,false,0,1, "", "","","","",null))
        when(queryTransportGroupService.queryTransportGroup(anyLong())).thenReturn(new TransportGroupVO(1l, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1l, 0,0, "informPhone", "informEmail", null,"",null))
        when(driverOrderGateway.create(any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(),anyInt(),any(),any(),any(),any())).thenReturn("createResponse")
        when(queryVehicleService.query(anyLong(),any())).thenReturn(new VehicleVO(1l, "carLicense", 1l, "carColor", 1l, "carBrandName", 1l, "carTypeName", 1l, "carSeriesName", 0,1,1,"desc",0))

        when:
        MockConfirmDspOrderResponseType result = mockConfirmDspOrderExecutor.execute(new MockConfirmDspOrderRequestType(dspOrderId:"1", driverId: 1L, transportGroupId: 1L, type: ConfirmType.DISPATCH_CONFIRMED.getStatus(), event: OrderStatusEvent.SYSTEM_ASSIGN.getCode()))

        then:
        result != null
    }

    def "test execute 2"() {
        given:
        when(queryDspOrderService.query(anyString())).thenReturn(new DspOrderVO("supplyOrderId", "uid", "dspOrderId", "userOrderId", 0, "productName", CategoryCodeEnum.ALL, 0, 0, 0, "productCode", 0, new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), 0, 0, new UseDays(0 as BigDecimal), 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, "priceMark", 0, "spName", 0, "driverOrderId", 1l, 0, "targetId", "terminalId", 0, "depPoiCode", "depPoiSourceType", 0 as BigDecimal, 0 as BigDecimal, "actualFromCoordsys", "fromAddress", "fromName", "arrivePoiCode", "arrivePoiSourceType", 0 as BigDecimal, 0 as BigDecimal, "actualToCoordsys", "toAddress", "toName", 0, 0 as BigDecimal, "locale", "serviceLanguageCodes", 0, "cancelRule", "waitingRule", "priceResultCode", "detailSnapShotid", 0, [new XproductVO("xCategoryCode", 0, 0, 0 as BigDecimal, "supplierCurrency", 0, 0 as BigDecimal, 0 as BigDecimal, "userCurrency", "xProductName", "supplierCategoryCode")], "spContractInfo", "dspStrategyStr", 0, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, "supplierBornePrice", new SupplierBorneVO([new RenaissanceActivityVO(1l, "activityName", 0, "activityCode", 0 as BigDecimal, "activityDesc", 0 as BigDecimal)], [new TradeCouponVO("couponCode", 0 as BigDecimal)]), "premiumPrice", "userCurrency", "supplierCurrency", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0,1,1,1L))
        when(queryDriverService.queryDriver(anyLong(),any(), anyLong())).thenReturn(new DriverVO(1l, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [new TransportGroupVO(1l, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1l, 0,0, "informPhone", "informEmail", null,"",null)], null, new SupplierVO(1L), 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", null,0, 1l, null, YesOrNo.NO,false,1,1, "", "","","","",null))
        when(queryTransportGroupService.queryTransportGroup(anyLong())).thenReturn(new TransportGroupVO(1l, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1l, 0, 0,"informPhone", "informEmail", null,"",null))
        when(driverOrderGateway.create(any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(),anyInt(),any(),any(),any(),any())).thenReturn("createResponse")
        when(queryVehicleService.query(anyLong(),any())).thenReturn(new VehicleVO(1l, "carLicense", 1l, "carColor", 1l, "carBrandName", 1l, "carTypeName", 1l, "carSeriesName", 0,1,1,"desc",0))

        when:
        MockConfirmDspOrderResponseType result = mockConfirmDspOrderExecutor.execute(new MockConfirmDspOrderRequestType(dspOrderId:"1", driverId: 1L, transportGroupId: 1L, type: ConfirmType.DRIVER_CONFIRMED.getStatus(), event: OrderStatusEvent.SYSTEM_ASSIGN.getCode()))

        then:
        result != null
    }

    def "test execute 3"() {
        given:
        when(queryDspOrderService.query(anyString())).thenReturn(new DspOrderVO("supplyOrderId", "uid", "dspOrderId", "userOrderId", 0, "productName", CategoryCodeEnum.ALL, 0, 0, 0, "productCode", 0, new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), 0, 0, new UseDays(0 as BigDecimal), 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, "priceMark", 0, "spName", 0, "driverOrderId", 1l, 0, "targetId", "terminalId", 0, "depPoiCode", "depPoiSourceType", 0 as BigDecimal, 0 as BigDecimal, "actualFromCoordsys", "fromAddress", "fromName", "arrivePoiCode", "arrivePoiSourceType", 0 as BigDecimal, 0 as BigDecimal, "actualToCoordsys", "toAddress", "toName", 0, 0 as BigDecimal, "locale", "serviceLanguageCodes", 0, "cancelRule", "waitingRule", "priceResultCode", "detailSnapShotid", 0, [new XproductVO("xCategoryCode", 0, 0, 0 as BigDecimal, "supplierCurrency", 0, 0 as BigDecimal, 0 as BigDecimal, "userCurrency", "xProductName", "supplierCategoryCode")], "spContractInfo", "dspStrategyStr", 0, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, "supplierBornePrice", new SupplierBorneVO([new RenaissanceActivityVO(1l, "activityName", 0, "activityCode", 0 as BigDecimal, "activityDesc", 0 as BigDecimal)], [new TradeCouponVO("couponCode", 0 as BigDecimal)]), "premiumPrice", "userCurrency", "supplierCurrency", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0,1,1,1L))
        when(queryDriverService.queryDriver(anyLong(),any(), anyLong())).thenReturn(new DriverVO(1l, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [new TransportGroupVO(1l, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1l, 0,0, "informPhone", "informEmail", null,"",null)], new CarVO(), new SupplierVO(1L), 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", null,0, 1l, null, YesOrNo.NO,false,0,1, "", "","","","",null))
        when(queryTransportGroupService.queryTransportGroup(anyLong())).thenReturn(new TransportGroupVO(1l, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1l, 0, 0,"informPhone", "informEmail", null,"",null))
        when(driverOrderGateway.create(any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(),anyInt(),any(),any(),any(),any())).thenReturn("createResponse")
        when(queryVehicleService.query(any(),any())).thenReturn(new VehicleVO(1l, "carLicense", 1l, "carColor", 1l, "carBrandName", 1l, "carTypeName", 1l, "carSeriesName", 0,1,1,"desc",0))

        when:
        MockConfirmDspOrderResponseType result = mockConfirmDspOrderExecutor.execute(new MockConfirmDspOrderRequestType(dspOrderId:"1", driverId: 1L, transportGroupId: 1L, type: ConfirmType.DRIVER_CAR_CONFIRMED.getStatus(), event: OrderStatusEvent.SYSTEM_ASSIGN.getCode()))

        then:
        result != null
    }

    def "test execute 4"() {
        given:
        when(queryDspOrderService.query(anyString())).thenReturn(new DspOrderVO("supplyOrderId", "uid", "dspOrderId", "userOrderId", 0, "productName", CategoryCodeEnum.ALL, 0, 0, 0, "productCode", 0, new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 15, 16, 51).getTime(), 0, 0, new UseDays(0 as BigDecimal), 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, "priceMark", 0, "spName", 0, "driverOrderId", 1l, 0, "targetId", "terminalId", 0, "depPoiCode", "depPoiSourceType", 0 as BigDecimal, 0 as BigDecimal, "actualFromCoordsys", "fromAddress", "fromName", "arrivePoiCode", "arrivePoiSourceType", 0 as BigDecimal, 0 as BigDecimal, "actualToCoordsys", "toAddress", "toName", 0, 0 as BigDecimal, "locale", "serviceLanguageCodes", 0, "cancelRule", "waitingRule", "priceResultCode", "detailSnapShotid", 0, [new XproductVO("xCategoryCode", 0, 0, 0 as BigDecimal, "supplierCurrency", 0, 0 as BigDecimal, 0 as BigDecimal, "userCurrency", "xProductName", "supplierCategoryCode")], "spContractInfo", "dspStrategyStr", 0, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, "supplierBornePrice", new SupplierBorneVO([new RenaissanceActivityVO(1l, "activityName", 0, "activityCode", 0 as BigDecimal, "activityDesc", 0 as BigDecimal)], [new TradeCouponVO("couponCode", 0 as BigDecimal)]), "premiumPrice", "userCurrency", "supplierCurrency", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0,1,1,1L))
        when(queryDriverService.queryDriver(anyLong(),any(), anyLong())).thenReturn(new DriverVO(1l, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [new TransportGroupVO(1l, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1l, 0,0, "informPhone", "informEmail", null,"", null)], null, new SupplierVO(1L), 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId",0, 0, 1l, null, YesOrNo.NO,false,0,1, "", "","","","",null))
        when(queryTransportGroupService.queryTransportGroup(anyLong())).thenReturn(new TransportGroupVO(1l, "transportGroupName", TransportGroupMode.FULL_TIME_ASSIGN, "dispatcherLanguage", "dispatcherPhone", "dispatcherEmail", "igtCode", 1l, 0, 0,"informPhone", "informEmail", null,"", null))
        when(driverOrderGateway.create(any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(),anyInt(),any(),any(),any(),any())).thenReturn("createResponse")
        when(queryVehicleService.query(anyLong(),any())).thenReturn(new VehicleVO(1l, "carLicense", 1l, "carColor", 1l, "carBrandName", 1l, "carTypeName", 1l, "carSeriesName", 0,1,1,"desc",0))

        when:
        MockConfirmDspOrderResponseType result = mockConfirmDspOrderExecutor.execute(new MockConfirmDspOrderRequestType(dspOrderId:"1", driverId: 1L, transportGroupId: 1L, type: 0, event: OrderStatusEvent.SYSTEM_ASSIGN.getCode()))

        then:
        result != null
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
