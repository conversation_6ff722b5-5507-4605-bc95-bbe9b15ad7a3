package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand
import com.ctrip.dcs.application.command.validator.SaaSOtherAssignDriverValidator
import com.ctrip.dcs.application.provider.converter.SaaSOperateDriverCarConverter
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.service.*
import com.ctrip.dcs.domain.common.value.DriverOrderVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.VehicleVO
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.DcsVbkSupplierOrderGateway
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.infrastructure.service.ConfirmSaasDspOrderServiceImpl
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasAssignDriverRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.*
import com.ctrip.igt.framework.common.exception.BizException
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

class SaaSOtherAssignDriverExeCmdTest extends Specification {

    def saaSOtherAssignDriverValidator = Mock(SaaSOtherAssignDriverValidator)
    def queryDriverService = Mock(QueryDriverService)
    def queryVehicleService = Mock(QueryVehicleService)
    def orderQueryService = Mock(QueryDspOrderService)

    def checkService = Mock(CheckService)
    def driverOrderFactory = Mock(DriverOrderFactory)
    def confirmDspOrderService = Mock(ConfirmDspOrderService)
    def confirmSaasDspOrderService = Mock(ConfirmSaasDspOrderServiceImpl)


    def messageProducer = Mock(MessageProviderService)
    def manualSubSkuConf = Mock(ManualSubSkuConf)
    def subSkuRepository = Mock(SubSkuRepository)
    def distributedLockService = Mock(DistributedLockService)

    def queryTransportGroupService = Mock(QueryTransportGroupService)
    def dcsVbkSupplierOrderGateway = Mock(DcsVbkSupplierOrderGateway)
    def executor = new SaaSOtherAssignDriverExeCmd(
            saaSOtherAssignDriverValidator: saaSOtherAssignDriverValidator,
            queryDriverService: queryDriverService,
            queryVehicleService: queryVehicleService,
            orderQueryService: orderQueryService,

            checkService: checkService,
            driverOrderFactory: driverOrderFactory,
            confirmDspOrderService: confirmDspOrderService,
            confirmSaasDspOrderService: confirmSaasDspOrderService,

            messageProducer: messageProducer,
            manualSubSkuConf: manualSubSkuConf,
            subSkuRepository: subSkuRepository,
            distributedLockService: distributedLockService,

            queryTransportGroupService: queryTransportGroupService,
            dcsVbkSupplierOrderGateway: dcsVbkSupplierOrderGateway
    )

    @Unroll
    def "test execute"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        orderQueryService.query(_) >> dspOrderVO
        queryVehicleService.query(_, _) >> vehicleVO
        queryDriverService.queryDriver(_ as Long, _ as ParentCategoryEnum, _ as Long) >> driverVO
        checkService.check(_) >> checkModel
        driverOrderFactory.createForSaaS(_, _, _, _, _,_) >> driverOrder

        when: "执行校验方法"
        def order = executor.execute(req)

        then: "验证校验结果"
        order.equals(res)

        where:
        req           | lock | dspOrderVO      | vehicleVO      | driverVO      | checkModel      | isIsNew | driverOrder      || res
        getOtherReq() | true | getDspOrderVO() | getVehicleVO() | getDriverVO() | getCheckModel() | true    | getDriverOrder() || "16211561979363356"

    }

    @Unroll
    def "test execute newProcess"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        orderQueryService.query(_) >> dspOrderVO
        queryVehicleService.query(_, _) >> vehicleVO
        queryDriverService.queryDriver(_ as Long, _ as ParentCategoryEnum, _ as Long) >> driverVO
        checkService.check(_) >> checkModel
        driverOrderFactory.createForSaaS(_, _, _, _, _,_) >> driverOrder

        when: "执行校验方法"
        def order = executor.execute(req)

        then: "验证校验结果"
        order.equals(res)

        where:
        req                     | lock | dspOrderVO      | vehicleVO      | driverVO      | checkModel      | isIsNew | driverOrder      || res
        getOtherReqNewProcess() | true | getDspOrderVO() | getVehicleVO() | getDriverVO() | getCheckModel() | true    | getDriverOrder() || "16211561979363356"

    }

    @Unroll
    def "test exception"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        orderQueryService.query(_) >> dspOrderVO
        queryVehicleService.query(_, _) >> vehicleVO
        queryDriverService.queryDriver(_ as Long, _ as ParentCategoryEnum, _ as Long) >> driverVO
        checkService.check(_) >> checkModel
        driverOrderFactory.createForSaaS(_, _, _, _, _,_) >> driverOrder

        when: "执行校验方法"
        def order = executor.execute(req)
        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == res


        where:
        req           | lock  | dspOrderVO      | vehicleVO      | driverVO      | checkModel      | isIsNew | driverOrder      || res
        getOtherReq() | false | getDspOrderVO() | getVehicleVO() | getDriverVO() | getCheckModel() | true    | getDriverOrder() || "10033"
    }

    @Unroll
    def "test buildBusinessVO"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        orderQueryService.query(_) >> dspOrderVO
        queryVehicleService.query(_, _) >> vehicleVO
        queryDriverService.queryDriver(_ as Long, _ as ParentCategoryEnum, _ as Long) >> driverVO
        checkService.check(_) >> checkModel
        driverOrderFactory.createForSaaS(_, _, _, _, _,_) >> driverOrder

        when: "执行校验方法"
        def order = executor.buildSaaSBusinessVO(req)
        then: "验证校验结果"
        order.vehicleVO != null == res


        where:
        req                  | lock  | dspOrderVO      | vehicleVO      | driverVO      | checkModel      | isIsNew | driverOrder      || res
        getOtherReqNew(1, 1) | false | getDspOrderVO() | getVehicleVO() | getDriverVO() | getCheckModel() | true    | getDriverOrder() || true
        getOtherReqNew(0, 1) | false | getDspOrderVO() | getVehicleVO() | getDriverVO() | getCheckModel() | true    | getDriverOrder() || true
        getOtherReqNew(0, 0) | false | getDspOrderVO() | getVehicleVO() | getDriverVO() | getCheckModel() | true    | getDriverOrder() || true
    }

    @Unroll
    def "test isNeedUpdateSupplierRemark"() {
        given:
        when:
        boolean result = executor.isNeedUpdateSupplierRemark(notUpdateSupplierRemark, batchAssign);

        then:
        Assert.assertTrue(Objects.equals(result, expect))

        where:
        notUpdateSupplierRemark | batchAssign || expect
        true                    | true        || false
        false                   | true         | true
        false                   | false        | false
    }

    @Unroll
    def "test converter"() {
        given:
        SaasAssignDriverRequestType request = new SaasAssignDriverRequestType();
        request.setNewProcess(Boolean.TRUE);
        SaaSOperatorInfo operator = new SaaSOperatorInfo();
        operator.setNotUpdateDriverVisibleRemark(notUpdateDriverVisibleRemark);
        operator.setNotUpdateSupplierRemark(notUpdateSupplierRemark);
        request.setOperator(operator)
        SaaSOrderInfo order = new SaaSOrderInfo()
        order.setOrderSourceCode(1)
        request.setOrder(order)
        request.setDriver(new SaaSDriverInfo())
        request.setCar(new SaaSCarInfo())
        request.setSupplier(new SaaSSupplierInfo())
        when:
        SaaSOperateDriverCarCommand command = SaaSOperateDriverCarConverter.converter(request);
        then:
        Assert.assertTrue(Objects.equals(command.isNotUpdateDriverVisibleRemark(), expect))
        Assert.assertTrue(Objects.equals(command.isNotUpdateSupplierRemark(), expect))
        where:
        notUpdateDriverVisibleRemark | notUpdateSupplierRemark || expect
        Boolean.TRUE                 | Boolean.TRUE            || Boolean.TRUE
        Boolean.FALSE                | Boolean.FALSE           || Boolean.FALSE
    }



    SaaSOperateDriverCarCommand getOtherReqNew(isSelfOrder, carId) {
        def saasAssignDriverRequestType = new SaasAssignDriverRequestType()
        saasAssignDriverRequestType.setOrder(buildSaaSOtherOrderInfo())
        saasAssignDriverRequestType.setOperator(buildSaaSOperatorInfo())
        saasAssignDriverRequestType.setSupplier(buildSaaSSupplierInfo())
        saasAssignDriverRequestType.setCar(buildSaaSOtherCarInfo())
        saasAssignDriverRequestType.setDriver(buildSaaSOtherDriverInfo())
        saasAssignDriverRequestType.setNewProcess(Boolean.TRUE)
        saasAssignDriverRequestType.getDriver().setIsSelfDriver(isSelfOrder)
        saasAssignDriverRequestType.getCar().setCarId(carId)
        def cmd = SaaSOperateDriverCarConverter.converter(saasAssignDriverRequestType)
        return cmd
    }

    SaaSOperateDriverCarCommand getOtherReq() {
        def saasAssignDriverRequestType = new SaasAssignDriverRequestType()
        saasAssignDriverRequestType.setOrder(buildSaaSOtherOrderInfo())
        saasAssignDriverRequestType.setOperator(buildSaaSOperatorInfo())
        saasAssignDriverRequestType.setSupplier(buildSaaSSupplierInfo())
        saasAssignDriverRequestType.setCar(buildSaaSOtherCarInfo())
        saasAssignDriverRequestType.setDriver(buildSaaSOtherDriverInfo())
        def cmd = SaaSOperateDriverCarConverter.converter(saasAssignDriverRequestType)
        return cmd
    }

    SaaSOperateDriverCarCommand getOtherReqNewProcess() {
        def saasAssignDriverRequestType = new SaasAssignDriverRequestType()
        saasAssignDriverRequestType.setOrder(buildSaaSOtherOrderInfo())
        saasAssignDriverRequestType.setOperator(buildSaaSOperatorInfo())
        saasAssignDriverRequestType.setSupplier(buildSaaSSupplierInfo())
        saasAssignDriverRequestType.setCar(buildSaaSOtherCarInfo())
        saasAssignDriverRequestType.setDriver(buildSaaSOtherDriverInfo())
        saasAssignDriverRequestType.getDriver().setDriverSettlePrice(BigDecimal.TEN)
        saasAssignDriverRequestType.getDriver().setDriverSettleCurrency("Exce")
        saasAssignDriverRequestType.getDriver().setDriverVisibleRemark("beizhu'")
        saasAssignDriverRequestType.setNewProcess(Boolean.TRUE)
        saasAssignDriverRequestType.setCheckCode(1)
        def cmd = SaaSOperateDriverCarConverter.converter(saasAssignDriverRequestType)
        return cmd
    }

    SaaSOperateDriverCarCommand getSelfReq() {
        def saasAssignDriverRequestType = new SaasAssignDriverRequestType()
        saasAssignDriverRequestType.setOrder(buildSaaSOrderInfo())
        saasAssignDriverRequestType.setOperator(buildSaaSOperatorInfo())
        saasAssignDriverRequestType.setSupplier(buildSaaSSupplierInfo())
        saasAssignDriverRequestType.setCar(buildSaaSSelfCarInfo())
        saasAssignDriverRequestType.setDriver(buildSaaSSelfDriverInfo())
        def cmd = SaaSOperateDriverCarConverter.converter(saasAssignDriverRequestType)
        return cmd
    }


    DspOrderVO getDspOrderVO() {
        def order = new DspOrderVO()
        order.setDspOrderId("16211561979363356")
        order.setSupplierId(111)
        return order
    }

    VehicleVO getVehicleVO() {
        def order = new VehicleVO()
        order.setCarId(16211561979363356L)
        return order
    }

    DriverVO getDriverVO() {
        def order = new DriverVO()
        order.setDriverId(16211561979363356L)
        return order
    }


    CheckModel getCheckModel() {
        DspModelVO dspModelVO = new DspModelVO()
        def order = new CheckModel(dspModelVO)
        return order
    }

    DriverOrderVO getDriverOrder() {
        def order = new DriverOrderVO()
        order.setDriverOrderId("111")
        order.setDriverId(16211561979363356L)
        order.setDspOrderId("16211561979363356")
        return order
    }

    SaaSOrderInfo buildSaaSOrderInfo() {
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.TRIP.getCode())
        return saaSOrderInfo
    }

    SaaSOrderInfo buildSaaSOtherOrderInfo() {
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.DISTRIBUTOR.getCode())
        return saaSOrderInfo
    }

    SaaSDriverInfo buildSaaSSelfDriverInfo() {
        SaaSDriverInfo saaSDriverInfo = new SaaSDriverInfo()
        saaSDriverInfo.setDriverId("111")
        saaSDriverInfo.setIsSelfDriver(1)
        return saaSDriverInfo
    }

    SaaSCarInfo buildSaaSSelfCarInfo() {
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarId(111)
        return saaSCarInfo
    }

    SaaSDriverInfo buildSaaSOtherDriverInfo() {
        SaaSDriverInfo saaSDriverInfo = new SaaSDriverInfo()
        saaSDriverInfo.setIsSelfDriver(0)
        saaSDriverInfo.setDriverLanguage("yy")
        saaSDriverInfo.setDriverMobile("15555555555")
        saaSDriverInfo.setDriverMobileCountryCode("1111")
        saaSDriverInfo.setDriverName("xiao_ming")
        saaSDriverInfo.setDriverOtherContract("other")
        saaSDriverInfo.setDriverId("1")
        return saaSDriverInfo
    }

    SaaSCarInfo buildSaaSOtherCarInfo() {
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarBrandId(1)
        saaSCarInfo.setCarColorId(2)
        saaSCarInfo.setCarLicense("京ACD933")
        saaSCarInfo.setCarSeriesId(3)
        saaSCarInfo.setCarTypeId(4)
        saaSCarInfo.setCarDesc("desc")
        return saaSCarInfo
    }

    SaaSSupplierInfo buildSaaSSupplierInfo() {
        SaaSSupplierInfo saaSSupplierInfo = new SaaSSupplierInfo()
        saaSSupplierInfo.setSupplierId(111L)
        saaSSupplierInfo.setSupplierName("供应商")
        return saaSSupplierInfo
    }

    SaaSOperatorInfo buildSaaSOperatorInfo() {
        SaaSOperatorInfo saaSOperatorInfo = new SaaSOperatorInfo()
        saaSOperatorInfo.setOperatorUserAccount("1111")
        saaSOperatorInfo.setOperatorUserName("操作人")
        saaSOperatorInfo.setOperatorUserType("systemUser")
        return saaSOperatorInfo
    }


}
