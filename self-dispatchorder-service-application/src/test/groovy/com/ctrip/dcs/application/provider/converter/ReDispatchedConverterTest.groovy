package com.ctrip.dcs.application.provider.converter

import com.ctrip.dcs.domain.common.enums.ReassignTaskEnum
import com.ctrip.dcs.domain.common.value.BaseDetailVO
import com.ctrip.dcs.domain.common.value.DispatchRightVO
import com.ctrip.dcs.domain.common.value.DriverPunishVO
import com.ctrip.dcs.domain.common.value.PunishRuleVO
import com.ctrip.dcs.domain.common.value.PunishVO
import com.ctrip.dcs.domain.common.value.ReasonDetailVO
import spock.lang.Specification

/**
 * <AUTHOR> ZhangZhen
 * @create 2023/7/21 19:26
 */
class ReDispatchedConverterTest extends Specification {

    def "test build DispatchPunishInfo"() {
        when:
        def res = ReDispatchedConverter.build(new PunishVO())
        then:
        res != null
    }

    def "test build PunishRuleInfo"() {
        when:
        def res = ReDispatchedConverter.build(new PunishRuleVO())
        then:
        res != null
    }

    def "test build DriverPunishVO"() {
        when:
        def res = ReDispatchedConverter.build(new DriverPunishVO())
        then:
        res != null
    }

    def "test build ReasonDetailVO"() {
        when:
        def res = ReDispatchedConverter.build(new ReasonDetailVO())
        then:
        res != null
    }

    def "test build DispatchRightVO"() {
        when:
        def res = ReDispatchedConverter.build(new DispatchRightVO())
        then:
        res != null
    }

    def "test build buildQueryPunishReason"() {
        when:
        def res = ReDispatchedConverter.buildQueryPunishReason(ReassignTaskEnum.PunishOperateType.CHECK,new BaseDetailVO(userOrderId: "1"), new ReasonDetailVO(), ReassignTaskEnum.ChangeDriveTypeEnum.AFTER_CONFIRM,1)
        then:
        res != null
    }

}