package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.ResetScheduleTaskCommand
import com.ctrip.dcs.application.service.DspOrderRewardStrategyService
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderWayPointVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SopRecord
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.factory.ScheduleFactory
import com.ctrip.dcs.domain.schedule.factory.ScheduleRecordFactory
import com.ctrip.dcs.domain.schedule.factory.ScheduleTaskFactory
import com.ctrip.dcs.domain.schedule.repository.ScheduleRecordRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleStrategyRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.infrastructure.adapter.carconfig.VbkDriverGrabDspStrategyConfig
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ResetScheduleTaskExeCmdTest extends Specification {
    @Mock
    ScheduleFactory scheduleFactory
    @Mock
    QueryDspOrderService dspOrderService
    @Mock
    ScheduleRepository scheduleRepository
    @Mock
    ScheduleRecordRepository scheduleRecordRepository
    @Mock
    ScheduleRecordFactory scheduleRecordFactory
    @Mock
    MessageProviderService messageProducer
    @Mock
    ScheduleTaskFactory taskFactory
    @Mock
    VbkDriverGrabDspStrategyConfig vbkDriverGrabDspStrategyConfig
    @Mock
    ScheduleTaskRepository taskRepository
    @Mock
    ScheduleStrategyRepository strategyRepository
    @Mock
    ScheduleDO schedule
    @Mock
    DspOrderVO dspOrder
    @Mock
    ScheduleTaskDO task
    @Mock
    private DspOrderRewardStrategyService dspOrderRewardStrategyService;
    @InjectMocks
    ResetScheduleTaskExeCmd resetScheduleTaskExeCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(dspOrderService.queryOrderDetail(anyString())).thenReturn(dspOrder)
        when(scheduleRepository.find(anyLong())).thenReturn(schedule)
        when(schedule.getRound()).thenReturn(0)
        when(schedule.getDspOrderId()).thenReturn("1")
        when(schedule.getScheduleId()).thenReturn(1L)
        when(taskRepository.query(anyLong(), anyString())).thenReturn([task])

        when:
        def result = resetScheduleTaskExeCmd.execute(new ResetScheduleTaskCommand(1l))

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme