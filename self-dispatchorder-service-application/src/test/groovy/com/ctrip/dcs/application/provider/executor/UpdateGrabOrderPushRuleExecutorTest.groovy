package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.service.IGrabOrderPushRuleService
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabOrderPushRuleRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.UpdateGrabOrderPushRuleResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.GrabOrderPushRuleSoaDTO
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class UpdateGrabOrderPushRuleExecutorTest extends Specification {

    private IGrabOrderPushRuleService grabOrderPushRuleService = Mock(IGrabOrderPushRuleService);

    UpdateGrabOrderPushRuleExecutor executor = new UpdateGrabOrderPushRuleExecutor(grabOrderPushRuleService: grabOrderPushRuleService)

    def "Execute"() {
        given:
        GrabOrderPushRuleSoaDTO dto = new GrabOrderPushRuleSoaDTO()
        dto.setId(1L)
        dto.setSupplierId(1l)
        dto.setCityIds([1L])
        dto.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        dto.setSupplierName("supplierName")
        dto.setVehicleGroupIdList([117])
        dto.setRuleType(1)
        dto.setImmediatePushTime(1)
        dto.setFixedPushTime(null)
        dto.setStartBookTime("08:00")
        dto.setEndBookTime("08:00")
        dto.setBookTime("08:00")

        UpdateGrabOrderPushRuleRequestType request = new UpdateGrabOrderPushRuleRequestType()
        request.setRule(dto)

        grabOrderPushRuleService.update(_) >> true
        when:
        UpdateGrabOrderPushRuleResponseType response = executor.execute(request)
        then:
        response.getResponseResult().getReturnCode() == "200"
    }
}
