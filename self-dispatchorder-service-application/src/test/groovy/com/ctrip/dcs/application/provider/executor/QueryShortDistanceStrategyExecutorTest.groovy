package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.command.ShortDistanceStrategyExeCmd
import com.ctrip.dcs.application.command.dto.QueryShortDistanceStrategyResDTO
import com.ctrip.dcs.application.provider.converter.ShortDistanceStrategyConverter
import com.ctrip.dcs.domain.dsporder.entity.ShortDistanceStrategyDO
import com.ctrip.dcs.domain.schedule.value.QueryShortDistanceStrategyCondition
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryShortDistanceStrategyRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryShortDistanceStrategyResponseType
import com.ctrip.igt.PaginationDTO
import spock.lang.Specification

class QueryShortDistanceStrategyExecutorTest extends Specification {
    def strategyExeCmd = Mock(ShortDistanceStrategyExeCmd)
    def shortDistanceStrategyConverter = Mock(ShortDistanceStrategyConverter)
    def executor = new QueryShortDistanceStrategyExecutor(
            strategyExeCmd: strategyExeCmd,
            shortDistanceStrategyConverter: shortDistanceStrategyConverter
    )


    def "test execute"() {
        given:
        strategyExeCmd.query(_ as QueryShortDistanceStrategyCondition) >> new QueryShortDistanceStrategyResDTO([new ShortDistanceStrategyDO()], new PaginationDTO())
        shortDistanceStrategyConverter.convert(_ as QueryShortDistanceStrategyRequestType) >> new QueryShortDistanceStrategyCondition()
        shortDistanceStrategyConverter.convert(_ as QueryShortDistanceStrategyResDTO) >> new QueryShortDistanceStrategyResponseType()

        when:
        def result = executor.execute(new QueryShortDistanceStrategyRequestType())

        then:
        result.responseResult.returnCode == "200"
    }

    def "test execute exp"() {
        given:
        strategyExeCmd.query(_ as QueryShortDistanceStrategyCondition) >> { throw new Exception("error") }
        shortDistanceStrategyConverter.convert(_ as QueryShortDistanceStrategyRequestType) >> new QueryShortDistanceStrategyCondition()

        when:
        def result = executor.execute(new QueryShortDistanceStrategyRequestType())

        then:
        result.responseResult.returnCode == "500"
    }
}
