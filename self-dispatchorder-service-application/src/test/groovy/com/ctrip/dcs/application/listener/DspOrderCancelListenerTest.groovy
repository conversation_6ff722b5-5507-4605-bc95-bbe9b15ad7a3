package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.command.CalculateDriverMileageProfitExeCmd
import com.ctrip.dcs.application.command.dispatchergrab.CancelDispatcherGrabOrderExeCmd
import com.ctrip.dcs.application.service.ShutdownScheduleService
import com.ctrip.dcs.domain.common.constants.EventConstants
import com.ctrip.dcs.domain.common.enums.IvrBizTypeEnum
import com.ctrip.dcs.domain.common.enums.ScheduleEventType
import com.ctrip.dcs.domain.common.service.*
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DriverWorkTimeVO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.gateway.DspDrvOrderLimitTakenRecordGateway
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway
import com.ctrip.dcs.domain.dsporder.gateway.PlatformGeoServiceGateway
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderFeeRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository
import com.ctrip.dcs.infrastructure.common.config.BusinessTemplateInfoConfig
import qunar.tc.qmq.Message
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp
/**
 * DspOrderCancelListener 单元测试
 *
 * <AUTHOR> Assistant
 */
class DspOrderCancelListenerTest extends Specification {

    DspOrderCancelListener listener
    DspOrderRepository dspOrderRepository
    QueryTransportGroupService queryTransportGroupService
    SendSmsService sendSmsService
    SendEmailService sendEmailService
    BusinessTemplateInfoConfig businessTemplateInfoConfig
    PlatformGeoServiceGateway platformGeoServiceGateway
    DspOrderConfirmRecordRepository dspOrderConfirmRecordRepository
    DecryptPhoneService decryptPhoneService
    IvrCallService ivrCallService
    VbkAppPushService vbkAppPushService
    DspOrderFeeRepository dspOrderFeeRepository
    CancelDispatcherGrabOrderExeCmd cancelDispatcherGrabOrderExeCmd
    GrabCentreRepository grabCentreRepository
    GrabOrderDetailRepository grabOrderDetailRepository
    CalculateDriverMileageProfitExeCmd calculateDriverMileageProfitExeCmd
    QueryDriverService queryDriverService
    QueryDspOrderService queryDspOrderService
    ShutdownScheduleService shutdownScheduleService
    IGTOrderQueryServiceGateway igtOrderQueryServiceGateway
    DspDrvOrderLimitTakenRecordGateway dspDrvOrderLimitTakenRecordGateway
    MessageProviderService messageProviderService
    ConfigService commonConfConfig

    def setup() {
        // 创建所有依赖的 Mock
        dspOrderRepository = Mock(DspOrderRepository)
        queryTransportGroupService = Mock(QueryTransportGroupService)
        sendSmsService = Mock(SendSmsService)
        sendEmailService = Mock(SendEmailService)
        businessTemplateInfoConfig = Mock(BusinessTemplateInfoConfig)
        platformGeoServiceGateway = Mock(PlatformGeoServiceGateway)
        dspOrderConfirmRecordRepository = Mock(DspOrderConfirmRecordRepository)
        decryptPhoneService = Mock(DecryptPhoneService)
        ivrCallService = Mock(IvrCallService)
        vbkAppPushService = Mock(VbkAppPushService)
        dspOrderFeeRepository = Mock(DspOrderFeeRepository)
        cancelDispatcherGrabOrderExeCmd = Mock(CancelDispatcherGrabOrderExeCmd)
        grabCentreRepository = Mock(GrabCentreRepository)
        grabOrderDetailRepository = Mock(GrabOrderDetailRepository)
        calculateDriverMileageProfitExeCmd = Mock(CalculateDriverMileageProfitExeCmd)
        queryDriverService = Mock(QueryDriverService)
        queryDspOrderService = Mock(QueryDspOrderService)
        shutdownScheduleService = Mock(ShutdownScheduleService)
        igtOrderQueryServiceGateway = Mock(IGTOrderQueryServiceGateway)
        dspDrvOrderLimitTakenRecordGateway = Mock(DspDrvOrderLimitTakenRecordGateway)
        messageProviderService = Mock(MessageProviderService)
        commonConfConfig = Mock(ConfigService)

        // 创建被测试的实例并注入依赖
        listener = new DspOrderCancelListener()
        listener.dspOrderRepository = dspOrderRepository
        listener.queryTransportGroupService = queryTransportGroupService
        listener.sendSmsService = sendSmsService
        listener.sendEmailService = sendEmailService
        listener.businessTemplateInfoConfig = businessTemplateInfoConfig
        listener.platformGeoServiceGateway = platformGeoServiceGateway
        listener.dspOrderConfirmRecordRepository = dspOrderConfirmRecordRepository
        listener.decryptPhoneService = decryptPhoneService
        listener.ivrCallService = ivrCallService
        listener.vbkAppPushService = vbkAppPushService
        listener.dspOrderFeeRepository = dspOrderFeeRepository
        listener.cancelDispatcherGrabOrderExeCmd = cancelDispatcherGrabOrderExeCmd
        listener.grabCentreRepository = grabCentreRepository
        listener.grabOrderDetailRepository = grabOrderDetailRepository
        listener.calculateDriverMileageProfitExeCmd = calculateDriverMileageProfitExeCmd
        listener.queryDriverService = queryDriverService
        listener.queryDspOrderService = queryDspOrderService
        listener.shutdownScheduleService = shutdownScheduleService
        listener.igtOrderQueryServiceGateway = igtOrderQueryServiceGateway
        listener.dspDrvOrderLimitTakenRecordGateway = dspDrvOrderLimitTakenRecordGateway
        listener.messageProviderService = messageProviderService
        listener.commonConfConfig = commonConfConfig
    }

    def "test onMessage with valid message and dspOrder exists"() {
        given: "准备有效的消息和订单数据"
        Message message = Mock(Message)
        message.getStringProperty("driverOrderId") >> "DRV_001"
        message.getStringProperty("dspOrderId") >> "DSP_001"
        message.getStringProperty("cancelReasonId") >> "REASON_001"
        message.times() >> 1

        DspOrderDO dspOrderDO = new DspOrderDO(
                dspOrderId: "DSP_001",
                userOrderId: "USER_001",
                categoryCode: "airport_dropoff",
                cityId: 1,
                estimatedUseTime: new Timestamp(System.currentTimeMillis()),
                bizAreaType: 33
        )

        DspOrderConfirmRecordVO confirmRecordDO = new DspOrderConfirmRecordVO(
                dspOrderId: "DSP_001",
                driverOrderId: "DRV_001",
                driverInfo: new DspOrderConfirmRecordVO.DriverRecord(
                        driverId: 123L,
                        driverName: "张三",
                        driverPhone: "13800138000",
                        transportGroupId: 456L
                ),
                supplierInfo: new DspOrderConfirmRecordVO.SupplierRecord(
                        supplierId: 789L
                )
        )

        DriverVO driverVO = new DriverVO(driverId: 123L)
        DriverWorkTimeVO driverWorkTimeVO = new DriverWorkTimeVO()

        when: "执行 onMessage 方法"
        listener.onMessage(message)

        then: "验证所有相关方法被调用"
        1 * dspOrderRepository.find("DSP_001") >> dspOrderDO
        1 * dspOrderConfirmRecordRepository.findByDspOrderId("DSP_001", "DRV_001") >> confirmRecordDO
        1 * cancelDispatcherGrabOrderExeCmd.execute(_)
        1 * grabCentreRepository.deleteAll("DSP_001")
        1 * grabOrderDetailRepository.deleteAll("DSP_001")
        1 * shutdownScheduleService.shutdown("DSP_001", ScheduleEventType.DSP_ORDER_NOT_DISPATCHING)
        1 * ivrCallService.cancelIvrRecord("DSP_001", IvrBizTypeEnum.CREATE_IVR_RECORD)
        1 * ivrCallService.cancelIvrRecord("DSP_001", IvrBizTypeEnum.URGENT_ORDER_CREATE_IVR_RECORD)
        1 * ivrCallService.cancelIvrRecord("DSP_001", IvrBizTypeEnum.ORI_ORDER_MODIFY_CREATE_IVR_RECORD)
    }

    def "test onMessage with blank dspOrderId should return early"() {
        given: "准备 dspOrderId 为空的消息"
        Message message = Mock(Message)
        message.getStringProperty("driverOrderId") >> "DRV_001"
        message.getStringProperty("dspOrderId") >> ""
        message.getStringProperty("cancelReasonId") >> "REASON_001"
        message.times() >> 1

        when: "执行 onMessage 方法"
        listener.onMessage(message)

        then: "验证没有调用任何业务方法"
        0 * dspOrderRepository.find(_)
        0 * dspOrderConfirmRecordRepository.findByDspOrderId(_, _)
    }

    def "test onMessage with null dspOrderId should return early"() {
        given: "准备 dspOrderId 为 null 的消息"
        Message message = Mock(Message)
        message.getStringProperty("driverOrderId") >> "DRV_001"
        message.getStringProperty("dspOrderId") >> null
        message.getStringProperty("cancelReasonId") >> "REASON_001"
        message.times() >> 1

        when: "执行 onMessage 方法"
        listener.onMessage(message)

        then: "验证没有调用任何业务方法"
        0 * dspOrderRepository.find(_)
        0 * dspOrderConfirmRecordRepository.findByDspOrderId(_, _)
    }

    def "test onMessage with retry times exceeds limit should return early"() {
        given: "准备重试次数超过限制的消息"
        Message message = Mock(Message)
        message.getStringProperty("driverOrderId") >> "DRV_001"
        message.getStringProperty("dspOrderId") >> "DSP_001"
        message.getStringProperty("cancelReasonId") >> "REASON_001"
        message.times() >> EventConstants.QMQ_RETRY_TIMES + 1

        when: "执行 onMessage 方法"
        listener.onMessage(message)

        then: "验证没有调用任何业务方法"
        0 * dspOrderRepository.find(_)
        0 * dspOrderConfirmRecordRepository.findByDspOrderId(_, _)
    }

    def "test onMessage with dspOrder not found should return early"() {
        given: "准备订单不存在的情况"
        Message message = Mock(Message)
        message.getStringProperty("driverOrderId") >> "DRV_001"
        message.getStringProperty("dspOrderId") >> "DSP_001"
        message.getStringProperty("cancelReasonId") >> "REASON_001"
        message.times() >> 1

        dspOrderRepository.find(_) >> null

        when: "执行 onMessage 方法"
        listener.onMessage(message)

        then: "验证只调用了查询方法，没有调用其他业务方法"
        1 * dspOrderRepository.find(_)
        1 * dspOrderConfirmRecordRepository.findByDspOrderId(_, _)
        0 * cancelDispatcherGrabOrderExeCmd.execute(_)
    }

    def "test onMessage with exception should rethrow"() {
        given: "准备抛出异常的情况"
        Message message = Mock(Message)
        message.getStringProperty("driverOrderId") >> "DRV_001"
        message.getStringProperty("dspOrderId") >> "DSP_001"
        message.getStringProperty("cancelReasonId") >> "REASON_001"
        message.times() >> 1

        dspOrderRepository.find("DSP_001") >> { throw new RuntimeException("Database error") }

        when: "执行 onMessage 方法"
        listener.onMessage(message)

        then: "验证异常被重新抛出"
        thrown(RuntimeException)
    }

    @Unroll
    def "test onMessage with different retry times: #retryTimes"() {
        given: "准备不同重试次数的消息"
        Message message = Mock(Message)
        message.getStringProperty("driverOrderId") >> "DRV_001"
        message.getStringProperty("dspOrderId") >> "DSP_001"
        message.getStringProperty("cancelReasonId") >> "REASON_001"
        message.times() >> retryTimes

        DspOrderDO dspOrderDO = new DspOrderDO(
                dspOrderId: "DSP_001",
                userOrderId: "USER_001",
                categoryCode: "airport_dropoff",
                cityId: 1,
                estimatedUseTime: new Timestamp(System.currentTimeMillis()),
                bizAreaType: 33
        )

        DspOrderConfirmRecordVO confirmRecordDO = new DspOrderConfirmRecordVO(
                dspOrderId: "DSP_001",
                driverOrderId: "DRV_001"
        )

        when: "执行 onMessage 方法"
        listener.onMessage(message)

        then: "验证根据重试次数决定是否执行业务逻辑"
        if (retryTimes <= EventConstants.QMQ_RETRY_TIMES) {
            1 * dspOrderRepository.find("DSP_001") >> dspOrderDO
            1 * dspOrderConfirmRecordRepository.findByDspOrderId("DSP_001", "DRV_001") >> confirmRecordDO
        } else {
            0 * dspOrderRepository.find(_)
            0 * dspOrderConfirmRecordRepository.findByDspOrderId(_, _)
        }

        where:
        retryTimes << [0, 1, 2, 3, 4, 5]
    }
}
