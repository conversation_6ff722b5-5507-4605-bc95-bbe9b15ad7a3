package com.ctrip.dcs.application.service

import com.ctrip.dcs.application.command.ShutdownScheduleExeCmd
import com.ctrip.dcs.domain.common.enums.ScheduleEventType
import com.ctrip.dcs.domain.common.enums.ScheduleStatus
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ShutdownScheduleServiceTest extends Specification {
    @Mock
    ShutdownScheduleExeCmd shutdownScheduleExeCmd
    @Mock
    ScheduleRepository scheduleRepository
    @Mock
    ScheduleDO scheduleDO
    @InjectMocks
    ShutdownScheduleService shutdownScheduleService

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test shutdown"() {
        given:
        when(scheduleRepository.query(anyString())).thenReturn([scheduleDO])

        when:
        def result = shutdownScheduleService.shutdown("dspOrderId", ScheduleEventType.UNDEFINED)

        then:
        result == ScheduleEventType.UNDEFINED
    }

    def "test shutdown 2"() {
        given:
        when(scheduleRepository.find(anyLong())).thenReturn(scheduleDO)

        when:
        def result = shutdownScheduleService.shutdown([1l], ScheduleEventType.UNDEFINED)

        then:
        result == ScheduleEventType.UNDEFINED
    }

    def "test shutdown 3"() {
        when:
        def result = shutdownScheduleService.shutdown(scheduleDO, ScheduleEventType.UNDEFINED)

        then:
        result == ScheduleEventType.UNDEFINED
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme