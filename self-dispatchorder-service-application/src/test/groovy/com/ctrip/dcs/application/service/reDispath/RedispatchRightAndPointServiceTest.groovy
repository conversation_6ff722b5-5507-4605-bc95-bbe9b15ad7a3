package com.ctrip.dcs.application.service.reDispath

import com.ctrip.dcs.application.service.RedispatchRightAndPointService
import com.ctrip.dcs.domain.common.service.QueryFlightService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.BaseDetailVO
import com.ctrip.dcs.domain.schedule.gateway.CarDriverComplaintServiceGateway
import com.ctrip.dcs.infrastructure.common.config.ReasonDetailConfig
import com.ctrip.dcs.infrastructure.common.config.RedispatchRightConfig
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class RedispatchRightAndPointServiceTest extends Specification {

    def rightConfig = Mock(RedispatchRightConfig)
    def reasonDetailConfig = Mock(ReasonDetailConfig)
    def queryFlightService = Mock(QueryFlightService)
    def complaintService = Mock(CarDriverComplaintServiceGateway)


    def executor = new RedispatchRightAndPointService(
            rightConfig: rightConfig,
            reasonDetailConfig: reasonDetailConfig,
            queryFlightService: queryFlightService,
            complaintService: complaintService


    )

    @Unroll
    def "test execute1"() {

        given: "Mock数据"
        rightConfig.getChangeTimeLimit() >> 120


        when: "执行校验方法"
        def order = executor.getChangTimeDesc(req)

        then: "验证校验结果"
        order == res

        where:
        req                                                                             || res
        DateUtil.formatDate(DateUtil.addHours(new Date(), 3), DateUtil.DATETIME_FORMAT) || "用车前2小时外"
        DateUtil.formatDate(DateUtil.addHours(new Date(), 1), DateUtil.DATETIME_FORMAT) || "用车前2小时内"


    }

    def "test execute0"() {

        given: "Mock数据"
        rightConfig.getChangeTimeLimit() >> 120


        when: "执行校验方法"
        def order = executor.getChangTimeDesc("2023-07-22 10:00:00")

        then: "验证校验结果"
        order == "已过用车时间"


    }

    @Unroll
    def "test execute3"() {

        given: "Mock数据"
        rightConfig.getGapBookTimeHoursA() >> 24
        rightConfig.getGapBookTimeHoursB() >> 24
        rightConfig.getGapBookTimeHoursC() >> 24
        rightConfig.getGapBookTimeHoursD() >> 24
        rightConfig.getGapOrderTimeMinutesA() >> 30
        rightConfig.getGapOrderTimeMinutesB() >> 10
        rightConfig.getGapOrderTimeMinutesC() >> 30
        rightConfig.getGapOrderTimeMinutesD() >> 10

        when: "执行校验方法"
        def order = executor.confirmDispatchType(req)

        then: "验证校验结果"
        Objects.isNull(order) == false

        where:
        req            || res
        getdetailVO()  || false
        getdetailVO1() || false
        getdetailVO2() || false
        getdetailVO3() || false
        getdetailVO4() || false


    }

    BaseDetailVO getdetailVO() {
        def order = new BaseDetailVO()
        order.setEstimatedUseTimeBj(DateUtil.formatDate(DateUtil.addHours(new Date(), 25), DateUtil.DATETIME_FORMAT))
        order.setOrderStatus(200)
        order.setOldOrderStatusDetail(0)
        return order
    }

    BaseDetailVO getdetailVO1() {
        def order = getdetailVO()
        order.setEstimatedUseTimeBj(DateUtil.formatDate(DateUtil.addHours(new Date(), 20), DateUtil.DATETIME_FORMAT))
        return order
    }

    BaseDetailVO getdetailVO2() {
        def order = getdetailVO()
        order.setEstimatedUseTimeBj(DateUtil.formatDate(DateUtil.addHours(new Date(), 25), DateUtil.DATETIME_FORMAT))
        return order
    }

    BaseDetailVO getdetailVO3() {
        def order = getdetailVO()
        order.setEstimatedUseTimeBj(DateUtil.formatDate(DateUtil.addHours(new Date(), 15), DateUtil.DATETIME_FORMAT))
        return order
    }

    BaseDetailVO getdetailVO4() {
        def order = getdetailVO()
        order.setEstimatedUseTimeBj(DateUtil.formatDate(DateUtil.addHours(new Date(), 15), DateUtil.DATETIME_FORMAT))
        order.setOrderStatus(400)
        order.setOldOrderStatusDetail(0)
        return order
    }

}
