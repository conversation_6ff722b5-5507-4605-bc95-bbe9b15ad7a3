package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.command.ExecuteScheduleExeCmd
import com.ctrip.dcs.domain.common.enums.ScheduleTaskStatus
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.igt.framework.common.clogging.Logger
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class CompleteTaskListenerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    ExecuteScheduleExeCmd executeScheduleExeCmd
    @Mock
    ScheduleRepository scheduleRepository
    @Mock
    BaseMessage message
    @Mock
    ScheduleTaskDO scheduleTask
    @Mock
    ScheduleTaskRepository taskRepository
    @InjectMocks
    CompleteTaskListener completeTaskListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message"() {
        given:
        when(taskRepository.query(any(), any())).thenReturn([scheduleTask])
        when(message.getIntProperty("confirm")).thenReturn(confirm)
        when(scheduleTask.isWaitExecute()).thenReturn(isWaitExecute)
        when(scheduleTask.getPriority()).thenReturn(1)

        when:
        completeTaskListener.onMessage(message)

        then:
        message.getIntProperty("confirm") == result

        where:
        confirm | isWaitExecute || result
        1       | false         || 1
        0       | false         || 0
        0       | true          || 0
    }

    def "test on Message 1"() {
        given:
        when(taskRepository.query(any(), any())).thenReturn([scheduleTask])
        when(message.getIntProperty("confirm")).thenReturn(0)
        when(scheduleTask.isExecute()).thenReturn(true)
        when(scheduleTask.getPriority()).thenReturn(1)

        when:
        completeTaskListener.onMessage(message)

        then:
        message.getIntProperty("confirm") == 0
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme