package com.ctrip.dcs.application.service

import com.ctrip.dcs.application.command.ShortDistanceStrategyExeCmd
import com.ctrip.dcs.application.query.api.CheckOrderDelayedDspDTO
import com.ctrip.dcs.application.service.dto.DelayedDspCheckResultDTO
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.dsporder.repository.OrderPreConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.value.ConfirmType
import com.ctrip.dcs.domain.schedule.check.CheckChain
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.factory.CheckChainFactory
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.infrastructure.adapter.qconfig.BaseConfigService
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.PoiDTO
import org.assertj.core.util.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * ${description}
 *
 * <AUTHOR>
 * @date 2024/3/10 10:19:09
 * @version 1.0
 */
class OrderDelayedDspCheckServiceTest extends Specification {
    def testObj = new OrderDelayedDspCheckService()
    def checkChainFactory = Mock(CheckChainFactory)
    def dspContextService = Mock(DspContextService)
    def checkConfig = Mock(ConfigService)
    def commonConfig = Mock(BaseConfigService)
    def checkChain = Mock(CheckChain)
    def orderPreConfirmRecordRepository = Mock(OrderPreConfirmRecordRepository)
    def shortDistanceStrategyExeCmd = Mock(ShortDistanceStrategyExeCmd)

    def setup() {
        testObj.checkChainFactory = checkChainFactory
        testObj.dspContextService = dspContextService
        testObj.checkConfig = checkConfig
        testObj.orderPreConfirmRecordRepository = orderPreConfirmRecordRepository
        testObj.shortDistanceStrategyExeCmd = shortDistanceStrategyExeCmd
        testObj.commonConfig = commonConfig
    }

    @Unroll
    def "checkManualSchedullingTest1"() {
        given: "设定相关方法入参"
        commonConfig.getString(_) >> "1,2,3,all"
        shortDistanceStrategyExeCmd.checkShortDisOrder(_, _, _, _, _, _) >> true
        DspOrderVO dspOrder = new DspOrderVO(userOrderId: "123", categoryCode: CategoryCodeEnum.FROM_AIRPORT, cityId: 1L, estimatedUseTimeBj: new Date(), carTypeId: 117, estimatedKm: BigDecimal.TEN, distributionChannelId: 11)

        when:
        def b = testObj.isShortDisOrder(dspOrder)
        then:
        b == false
    }

    @Unroll
    def "checkManualSchedullingTest2"() {
        given: "设定相关方法入参"
        commonConfig.getString(_,_) >> "1,2,3"
        shortDistanceStrategyExeCmd.checkShortDisOrder(_, _, _, _, _, _) >> true
        DspOrderVO dspOrder = new DspOrderVO(userOrderId: "123", categoryCode: CategoryCodeEnum.FROM_AIRPORT, cityId: 1L, estimatedUseTimeBj: new Date(), carTypeId: 117, estimatedKm: BigDecimal.TEN, distributionChannelId: 11)

        when:
        def b = testObj.isShortDisOrder(dspOrder)
        then:
        b == true
    }

    @Unroll
    def "checkManualSchedullingTest3"() {
        given: "设定相关方法入参"
        commonConfig.getString(_,_) >> "2,3"
        shortDistanceStrategyExeCmd.checkShortDisOrder(_, _, _, _, _, _) >> true
        DspOrderVO dspOrder = new DspOrderVO(userOrderId: "123", categoryCode: CategoryCodeEnum.FROM_AIRPORT, cityId: 1L, estimatedUseTimeBj: new Date(), carTypeId: 117, estimatedKm: BigDecimal.TEN, distributionChannelId: 11)

        when:
        def b = testObj.isShortDisOrder(dspOrder)
        then:
        b == false
    }

    @Unroll
    def "checkManualSchedullingTest4"() {
        given: "设定相关方法入参"
        commonConfig.getString(_,_) >> "1,2,3"
        shortDistanceStrategyExeCmd.checkShortDisOrder(_, _, _, _, _, _) >> false
        DspOrderVO dspOrder = new DspOrderVO(userOrderId: "123", categoryCode: CategoryCodeEnum.FROM_AIRPORT, cityId: 1L, estimatedUseTimeBj: new Date(), carTypeId: 117, estimatedKm: BigDecimal.TEN, distributionChannelId: 11)

        when:
        def b = testObj.isShortDisOrder(dspOrder)
        then:
        b == false
    }

    @Unroll
    def "checkManualSchedullingTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        checkChainFactory.create(_) >> new CheckChain(DspStage.DSP)
        dspContextService.queryTransports(_) >> transportGroups
        checkChain.check(new CheckContext(), Lists.newArrayList()) >> checkResults
        orderPreConfirmRecordRepository.save(_) >> null
        commonConfig.getString(_,_) >> "all"
        shortDistanceStrategyExeCmd.checkShortDisOrder(_, _, _, _, _, _) >> shortDis

        when:
        def result = testObj.checkManualSchedulling(checkReq)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult

        where: "表格方式验证多种分支调用场景"
        shortDis || checkReq                                                                                                                                                                                                                                                                                                                                                                                      || transportGroups                    || checkResults         || expectedResult
        false    || new CheckOrderDelayedDspDTO(userOrderId: "123", categoryCode: "airport_pickup", cityId: 1L, estimatedUseTime: "2024-12-31 12:00", estimatedUseTimeBj: "2024-12-31 12:00", fromLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), toLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), skuId: 111, urgentOrder: true, highGradeOrder: false, vehicleGroupid: 117)  || getTransportGroups("empty")        || Lists.newArrayList() || new DelayedDspCheckResultDTO(delayedDispatch: false, orderConfirmType: null)
//        false    || new CheckOrderDelayedDspDTO(userOrderId: "123", categoryCode: "airport_pickup", cityId: 1L, estimatedUseTime: "2024-12-31 12:00", estimatedUseTimeBj: "2024-12-31 12:00", fromLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), toLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), skuId: 111, urgentOrder: false, highGradeOrder: false, vehicleGroupid: 117) || getTransportGroups("empty")        || Lists.newArrayList() || new DelayedDspCheckResultDTO(delayedDispatch: false, orderConfirmType: null)
        false    || new CheckOrderDelayedDspDTO(userOrderId: "123", categoryCode: "airport_pickup", cityId: 1L, estimatedUseTime: "2024-12-31 12:00", estimatedUseTimeBj: "2024-12-31 12:00", fromLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), toLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), skuId: 111, urgentOrder: false, highGradeOrder: false, vehicleGroupid: 117) || getTransportGroups("notAllManual") || Lists.newArrayList() || new DelayedDspCheckResultDTO(delayedDispatch: false, orderConfirmType: null)
        false    || new CheckOrderDelayedDspDTO(userOrderId: "123", categoryCode: "airport_pickup", cityId: 1L, estimatedUseTime: "2024-12-31 12:00", estimatedUseTimeBj: "2024-12-31 12:00", fromLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), toLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), skuId: 111, urgentOrder: false, highGradeOrder: false, vehicleGroupid: 117) || getTransportGroups("allManual")    || Lists.newArrayList() || new DelayedDspCheckResultDTO(delayedDispatch: true, orderConfirmType: ConfirmType.DISPATCH_CONFIRMED)

        true     || new CheckOrderDelayedDspDTO(userOrderId: "123", categoryCode: "airport_pickup", cityId: 1L, estimatedUseTime: "2024-12-31 12:00", estimatedUseTimeBj: "2024-12-31 12:00", fromLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), toLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), skuId: 111, urgentOrder: true, highGradeOrder: false, vehicleGroupid: 117)  || getTransportGroups("empty")        || Lists.newArrayList() || new DelayedDspCheckResultDTO(delayedDispatch: false, orderConfirmType: null)
//        true    || new CheckOrderDelayedDspDTO(userOrderId: "123", categoryCode: "airport_pickup", cityId: 1L, estimatedUseTime: "2024-12-31 12:00", estimatedUseTimeBj: "2024-12-31 12:00", fromLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), toLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), skuId: 111, urgentOrder: false, highGradeOrder: false, vehicleGroupid: 117) || getTransportGroups("empty")        || Lists.newArrayList() || new DelayedDspCheckResultDTO(delayedDispatch: false, orderConfirmType: null)
        true     || new CheckOrderDelayedDspDTO(userOrderId: "123", categoryCode: "airport_pickup", cityId: 1L, estimatedUseTime: "2024-12-31 12:00", estimatedUseTimeBj: "2024-12-31 12:00", fromLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), toLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), skuId: 111, urgentOrder: false, highGradeOrder: false, vehicleGroupid: 117) || getTransportGroups("notAllManual") || Lists.newArrayList() || new DelayedDspCheckResultDTO(delayedDispatch: false, orderConfirmType: null)
        true     || new CheckOrderDelayedDspDTO(userOrderId: "123", categoryCode: "airport_pickup", cityId: 1L, estimatedUseTime: "2024-12-31 12:00", estimatedUseTimeBj: "2024-12-31 12:00", fromLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), toLocation: new PoiDTO(cityId: 1L, latitude: 1.1, longitude: 2.2), skuId: 111, urgentOrder: false, highGradeOrder: false, vehicleGroupid: 117) || getTransportGroups("allManual")    || Lists.newArrayList() || new DelayedDspCheckResultDTO(delayedDispatch: true, orderConfirmType: ConfirmType.DISPATCH_CONFIRMED)
    }

    def getTransportGroups(String type) {
        if ("null".equalsIgnoreCase(type)) {
            return null
        }
        if ("null".equalsIgnoreCase(type)) {
            return Lists.newArrayList();
        }
        if ("notAllManual".equalsIgnoreCase(type)) {
            List<TransportGroupVO> transportGroups = new ArrayList<TransportGroupVO>();
            TransportGroupVO transportGroup = new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST, shortDisSwitch: 1)
            TransportGroupVO transportGroup1 = new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST, shortDisSwitch: 0)

            transportGroups.add(transportGroup)
            transportGroups.add(transportGroup1)

            return transportGroups;
        }
        if ("allManual".equalsIgnoreCase(type)) {
            List<TransportGroupVO> transportGroups = new ArrayList<TransportGroupVO>();
            TransportGroupVO transportGroup = new TransportGroupVO(transportGroupMode: TransportGroupMode.MANUAL_DISPATCH, shortDisSwitch: 1)
            TransportGroupVO transportGroup1 = new TransportGroupVO(transportGroupMode: TransportGroupMode.MANUAL_DISPATCH, shortDisSwitch: 0)
            transportGroups.add(transportGroup)
            transportGroups.add(transportGroup1)

            return transportGroups;
        }

    }

    @Unroll
    def "checkSysDelayedDispatchTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.checkSysDelayedDispatch(checkReq)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        checkReq                      || expectedResult
        new CheckOrderDelayedDspDTO() || new DelayedDspCheckResultDTO()
    }
}
