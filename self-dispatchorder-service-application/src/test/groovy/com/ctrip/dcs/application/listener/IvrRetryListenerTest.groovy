package com.ctrip.dcs.application.listener

import com.ctrip.dcs.domain.common.service.IvrCallService
import org.junit.Assert
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2025/1/1 17:21
 */
class IvrRetryListenerTest extends Specification {
    def testObj = new IvrRetryListener()
    def ivrCallService = Mock(IvrCallService)

    def setup() {
        testObj.ivrCallService = ivrCallService
    }

    @Unroll
    def "test ivrRetry"() {
        given:
        BaseMessage message = new BaseMessage();
        message.setSubject("4132131");
        message.setMessageId("31313")
        when:
        testObj.ivrRetry(message)
        then:
        Assert.assertTrue(Objects.nonNull(message))
    }
}
