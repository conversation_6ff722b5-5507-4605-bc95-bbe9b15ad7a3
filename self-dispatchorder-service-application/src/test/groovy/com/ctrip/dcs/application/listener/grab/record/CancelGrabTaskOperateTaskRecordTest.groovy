package com.ctrip.dcs.application.listener.grab.record

import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabTaskDO
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKGrabTaskRecordRepository
import com.ctrip.dcs.infrastructure.common.util.DateZoneConvertUtil
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class CancelGrabTaskOperateTaskRecordTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DateZoneConvertUtil dateZoneConvertUtil
    @Mock
    VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository
    @Mock
    VBKGrabTaskRecordRepository vbkGrabTaskRecordRepository
    @Mock
    VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository
    @Mock
    ConfigService map
    @InjectMocks
    CancelGrabTaskOperateTaskRecord cancelGrabTaskOperateTaskRecord

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test assemble Param"() {
        given:
        VBKGrabTaskOperateDTO dto = new VBKGrabTaskOperateDTO(taskId: "taskId")
        when(vbkDriverGrabTaskRepository.queryByVBKGrabTaskId(anyString())).thenReturn(new VBKDriverGrabTaskDO(cityId: 1))

        when:
        cancelGrabTaskOperateTaskRecord.assembleParam(dto)

        then:
        dto.getCityId() == 1
    }

    def "test build Record Content"() {
        given:
        when(map.getString(anyString(), anyString())).thenReturn("")

        when:
        String result = cancelGrabTaskOperateTaskRecord.buildRecordContent(new VBKGrabTaskOperateDTO(successNum: 0))

        then:
        result == ""
    }

    def "test build And Insert Record"() {
        given:
        when(dateZoneConvertUtil.getLocalTimeByCityId(anyString(), anyInt(), anyInt())).thenReturn("2024-07-04 12:00:00")
        when(vbkGrabTaskRecordRepository.insert(any())).thenReturn(0)
        when(map.getString(anyString(), anyString())).thenReturn("")

        when:
        def result = cancelGrabTaskOperateTaskRecord.buildAndInsertRecord(new VBKGrabTaskOperateDTO(taskId: "taskId", supplierId: 1l, operatorName: "operatorName", operatorType: 0, currentDateStr: "2024-07-04 12:00:00", cityId: 0))

        then:
        result == null
    }

    def "test getLocalTimeByCityId"() {
        given:
        when(dateZoneConvertUtil.getLocalTimeByCityId(anyString(), anyInt(), anyInt())).thenReturn("2024-07-04 13:00:00")
        when(vbkGrabTaskRecordRepository.insert(any())).thenReturn(0)
        when(map.getString(anyString(), anyString())).thenReturn("")

        when:
        def result = cancelGrabTaskOperateTaskRecord.getLocalTimeByCityId(1, "2024-07-04 12:00:00")

        then:
        result == "2024-07-04 13:00:00"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme