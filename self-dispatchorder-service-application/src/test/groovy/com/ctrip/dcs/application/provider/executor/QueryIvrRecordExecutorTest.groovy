package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.command.QueryIvrRecordExeCmd
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryIvrRecordRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryIvrRecordResponseType
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class QueryIvrRecordExecutorTest extends Specification {
    @Mock
    Logger logger
    @Mock
    QueryIvrRecordExeCmd queryIvrRecordExeCmd
    @InjectMocks
    QueryIvrRecordExecutor queryIvrRecordExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(queryIvrRecordExeCmd.execute(any())).thenReturn([null])

        when:
        QueryIvrRecordRequestType requestType = new QueryIvrRecordRequestType();
        requestType.setUserOrderId("11");
        requestType.setSupplyOrderId("22");
        requestType.setRecordGuid("sdfasdf");
        QueryIvrRecordResponseType result = queryIvrRecordExecutor.execute(requestType)

        then:
        result != null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme