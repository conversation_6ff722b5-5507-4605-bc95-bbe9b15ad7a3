package com.ctrip.dcs.application.listener.grab

import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO
import com.ctrip.dcs.application.listener.grab.record.OperateTaskRecordService
import com.ctrip.dcs.domain.common.util.JsonUtil
import com.ctrip.igt.framework.common.clogging.Logger
import qunar.tc.qmq.NeedRetryException
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class VBKGrabTaskOperateTaskListenerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    Map<String, OperateTaskRecordService> agentMap
    @Mock
    BaseMessage message
    @InjectMocks
    VBKGrabTaskOperateTaskListener vBKGrabTaskOperateTaskListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message 1"() {
        given:
        when(message.times()).thenReturn(21)
        when:
        def r1 = vBKGrabTaskOperateTaskListener.onMessage(message)

        then:
        r1 == null
    }

    def "test on Message 2"() {
        given:
        when(message.times()).thenReturn(1)
        when(message.getStringProperty("jsonStr")).thenReturn("")
        when:
        def r1 = vBKGrabTaskOperateTaskListener.onMessage(message)

        then:
        r1 == null
    }

    def "test on Message 3"() {
        given:
        VBKGrabTaskOperateDTO dto = new VBKGrabTaskOperateDTO()
        when(message.times()).thenReturn(1)
        when(message.getStringProperty("jsonStr")).thenReturn("{}")
        when:
        vBKGrabTaskOperateTaskListener.onMessage(message)

        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "VBKGrabTaskOperateTaskListener"
    }

    def "test on Message 4"() {
        given:
        VBKGrabTaskOperateDTO dto = new VBKGrabTaskOperateDTO()
        dto.setOperatorType(0)
        when(message.times()).thenReturn(1)
        when(message.getStringProperty("jsonStr")).thenReturn(JsonUtil.toJson(dto))
        when:
        def r1 = vBKGrabTaskOperateTaskListener.onMessage(message)

        then:
        r1 == null
    }

    def "test on Message 5"() {
        given:
        VBKGrabTaskOperateDTO dto = new VBKGrabTaskOperateDTO()
        dto.setOperatorType(1)
        when(message.times()).thenReturn(1)
        when(message.getStringProperty("jsonStr")).thenReturn(JsonUtil.toJson(dto))
        when:
        def r1 = vBKGrabTaskOperateTaskListener.onMessage(message)

        then:
        r1 == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme