package com.ctrip.dcs.application.listener.grab.record

import com.ctrip.dcs.application.command.dto.VBKGrabTaskOperateDTO
import com.ctrip.dcs.domain.common.enums.GrabSettlementEnum
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabTaskRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKGrabTaskRecordRepository
import com.ctrip.dcs.infrastructure.common.util.DateZoneConvertUtil
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class CreateGrabTaskOperateTaskRecordTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DateZoneConvertUtil dateZoneConvertUtil
    @Mock
    VBKDriverGrabTaskRepository vbkDriverGrabTaskRepository
    @Mock
    VBKGrabTaskRecordRepository vbkGrabTaskRecordRepository
    @Mock
    VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository
    @Mock
    ConfigService map
    @InjectMocks
    CreateGrabTaskOperateTaskRecord createGrabTaskOperateTaskRecord

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test build Record Content"() {
        given:
        when(map.getString(anyString(), anyString())).thenReturn("getStringResponse")

        when:
        String result = createGrabTaskOperateTaskRecord.buildRecordContent(new VBKGrabTaskOperateDTO(payForDriver: payForDriver, grabEndTime: "grabEndTime", makeUpEffectTime: "makeUpEffectTime", initialType: initialType, rewardsType: rewardsType, initialValue: 0 as BigDecimal, initialRate: 0 as BigDecimal, rewardsValue: 0 as BigDecimal, rewardsRate: 0 as BigDecimal, driverNum: 1l))

        then:
        result == content

        where:
        initialType                          | rewardsType                          | payForDriver || content
        null                                 | null                                 | null         || "getStringResponsegetStringResponsegetStringResponse"
        GrabSettlementEnum.VALUE.getType()   | GrabSettlementEnum.DISPLAY.getType() | 1            || "getStringResponsegetStringResponsegetStringResponsegetStringResponse"
        GrabSettlementEnum.DISPLAY.getType() | GrabSettlementEnum.VALUE.getType()   | 0            || "getStringResponsegetStringResponsegetStringResponsegetStringResponse"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme