package com.ctrip.dcs.application.scheduler

import com.ctrip.dcs.application.command.SubmitBroadcastGrabExeCmd
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.check.CheckConfig
import com.ctrip.dcs.domain.schedule.check.source.CheckSourceConfig
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO
import com.ctrip.dcs.domain.schedule.gateway.DriverInServiceGateway
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.sort.SortConfig
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.infrastructure.adapter.trocks.TRocksProviderAdapter
import qunar.tc.schedule.MockParameter
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class BroadcastPushJobTest extends Specification {
    @Mock
    GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository
    @Mock
    MessageProviderService messageProviderService
    @Mock
    SubSkuRepository subSkuRepository
    @Mock
    TRocksProviderAdapter rocksProviderAdapter
    @Mock
    DriverInServiceGateway driverInServiceGateway
    @Mock
    ConfigService broadcastGrabConfig
    @Mock
    SubmitBroadcastGrabExeCmd submitBroadcastGrabExeCmd
    @InjectMocks
    BroadcastPushJob broadcastPushJob

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    def "test execute"() {
        given:
        when(grabDspOrderDriverIndexRepository.queryCountBroadcastPushTime(any(Date.class), any(Date.class))).thenReturn(1L)
        when(grabDspOrderDriverIndexRepository.queryBroadcastPushTime(any(Date.class), any(Date.class), anyInt(), anyInt())).thenReturn([new GrabDspOrderDriverIndexDO(driverId: 1l, isBroadcast: 0, duid: "47067200989216864-1702697426785-1-1-8002-3-4-0-0-0", cityId: 1l, broadcastPushTimeBj: new Date(), grabPushTimeBj: new Date(), dspOrderId: "1")])
        when(subSkuRepository.find(anyInt())).thenReturn(new SubSkuVO(0, "subSkuName", DspType.SYSTEM_ASSIGN, TakenType.DEFAULT, new CheckConfig("checkId", "checkSourceId", new CheckSourceConfig(0, 0), ["dspCheckItem"], ["takenCheckItem"], ["I1": 1]), new SortConfig("sortId", "score", ["features": 0d]), 1l))
        when(driverInServiceGateway.queryDriverInServiceFromIndex(any(List<Long>.class))).thenReturn([(1l): Boolean.TRUE])
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)
        when(submitBroadcastGrabExeCmd.isGrayscaleCity(anyLong())).thenReturn(true)

        when:
        def result = broadcastPushJob.execute(new MockParameter())

        then:
        result == null
    }

    def "test send Broadcast Message"() {
        given:
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)

        when:
        def result = broadcastPushJob.sendBroadcastMessage([new GrabDspOrderDriverIndexDO(driverId: 1L, dspOrderId: "1")])

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme