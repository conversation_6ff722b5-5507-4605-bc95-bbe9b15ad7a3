package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand
import com.ctrip.dcs.application.command.validator.SaaSTripChangeDriverValidator
import com.ctrip.dcs.application.provider.converter.SaaSOperateDriverCarConverter
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.service.*
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.dsporder.carconfig.ManualSubSkuConf
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.DcsVbkSupplierOrderGateway
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.infrastructure.service.ConfirmSaasDspOrderServiceImpl
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasAssignDriverRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.*
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.Specification
import spock.lang.Unroll

class SaaSTripChangeDriverExeCmdTest extends Specification {

    def saaSTripChangeDriverValidator = Mock(SaaSTripChangeDriverValidator)
    def selfOrderQueryGateway = Mock(SelfOrderQueryGateway)


    def queryDriverService = Mock(QueryDriverService)
    def queryVehicleService = Mock(QueryVehicleService)
    def orderQueryService = Mock(QueryDspOrderService)

    def checkService = Mock(CheckService)
    def driverOrderFactory = Mock(DriverOrderFactory)
    def confirmDspOrderService = Mock(ConfirmDspOrderService)
    def confirmSaasDspOrderService = Mock(ConfirmSaasDspOrderServiceImpl)


    def messageProducer = Mock(MessageProviderService)
    def manualSubSkuConf = Mock(ManualSubSkuConf)
    def subSkuRepository = Mock(SubSkuRepository)
    def distributedLockService = Mock(DistributedLockService)

    def queryTransportGroupService = Mock(QueryTransportGroupService)
    def dcsVbkSupplierOrderGateway = Mock(DcsVbkSupplierOrderGateway)

    def executor = new SaaSTripChangeDriverExeCmd(
            saaSTripChangeDriverValidator: saaSTripChangeDriverValidator,
            queryDriverService: queryDriverService,
            queryVehicleService: queryVehicleService,
            orderQueryService: orderQueryService,

            checkService: checkService,
            driverOrderFactory: driverOrderFactory,
            confirmDspOrderService: confirmDspOrderService,
            confirmSaasDspOrderService: confirmSaasDspOrderService,

            messageProducer: messageProducer,
            manualSubSkuConf: manualSubSkuConf,
            subSkuRepository: subSkuRepository,
            distributedLockService: distributedLockService,

            selfOrderQueryGateway: selfOrderQueryGateway,
            queryTransportGroupService: queryTransportGroupService,
            dcsVbkSupplierOrderGateway: dcsVbkSupplierOrderGateway
    )

    @Unroll
    def "test execute"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        selfOrderQueryGateway.queryDspOrder(_) >> dspOrderVO
        queryVehicleService.query(_, _) >> vehicleVO
        queryDriverService.queryDriver(_ as Long, _ as ParentCategoryEnum, _ as Long) >> driverVO
        checkService.check(_) >> checkModel
        driverOrderFactory.createForSaaS(_, _, _, _, _,_) >> driverOrder
        subSkuRepository.find(_) >> subSkuVO

        when: "执行校验方法"
        def order = executor.execute(req)

        then: "验证校验结果"
        order.equals(res)

        where:
        req          | lock | dspOrderVO      | vehicleVO      | driverVO      | checkModel      | driverOrder      | subSkuVO      || res
        getSelfReq() | true | getDspOrderVO() | getVehicleVO() | getDriverVO() | getCheckModel() | getDriverOrder() | getSubSkuVO() || "16211561979363356"
    }

    @Unroll
    def "test execute newProcess"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        selfOrderQueryGateway.queryDspOrder(_) >> dspOrderVO
        queryVehicleService.query(_, _) >> vehicleVO
        queryDriverService.queryDriver(_ as Long, _ as ParentCategoryEnum, _ as Long) >> driverVO
        checkService.check(_) >> checkModel
        driverOrderFactory.createForSaaS(_, _, _, _, _,_) >> driverOrder
        subSkuRepository.find(_) >> subSkuVO

        when: "执行校验方法"
        def order = executor.execute(req)

        then: "验证校验结果"
        order.equals(res)

        where:
        req                    | lock | dspOrderVO      | vehicleVO      | driverVO      | checkModel      | driverOrder      | subSkuVO      || res
        getSelfReqNewProcess() | true | getDspOrderVO() | getVehicleVO() | getDriverVO() | getCheckModel() | getDriverOrder() | getSubSkuVO() || "16211561979363356"
        getSelfReqNewProcess() | true | getDspOrderVO() | null           | getDriverVO() | getCheckModel() | getDriverOrder() | getSubSkuVO() || "16211561979363356"
    }

    @Unroll
    def "test exception"() {

        given: "Mock数据"

        def distributedLock = Mock(DistributedLockService.DistributedLock.class)
        distributedLockService.getLock(_) >> distributedLock
        distributedLock.tryLock() >> lock
        orderQueryService.query(_) >> dspOrderVO
        queryVehicleService.query(_, _) >> vehicleVO
        queryDriverService.queryDriver(_ as Long, _ as ParentCategoryEnum, _ as Long) >> driverVO
        checkService.check(_) >> checkModel
        driverOrderFactory.createForSaaS(_, _, _, _, _,_) >> driverOrder
        subSkuRepository.find(_) >> subSkuVO

        when: "执行校验方法"
        def order = executor.execute(req)

        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == res

        where:
        req          | lock  | dspOrderVO      | vehicleVO      | driverVO      | checkModel      | isIsNew | driverOrder      | subSkuVO      || res
        getSelfReq() | false | getDspOrderVO() | getVehicleVO() | getDriverVO() | getCheckModel() | true    | getDriverOrder() | getSubSkuVO() || "10033"

    }

    SaaSOperateDriverCarCommand getOtherReq() {
        def saasAssignDriverRequestType = new SaasAssignDriverRequestType()
        saasAssignDriverRequestType.setOrder(buildSaaSOtherOrderInfo())
        saasAssignDriverRequestType.setOperator(buildSaaSOperatorInfo())
        saasAssignDriverRequestType.setSupplier(buildSaaSSupplierInfo())
        saasAssignDriverRequestType.setCar(buildSaaSOtherCarInfo())
        saasAssignDriverRequestType.setDriver(buildSaaSOtherDriverInfo())
        def cmd = SaaSOperateDriverCarConverter.converter(saasAssignDriverRequestType)
        return cmd
    }

    SaaSOperateDriverCarCommand getSelfReq() {
        def saasAssignDriverRequestType = new SaasAssignDriverRequestType()
        saasAssignDriverRequestType.setOrder(buildSaaSOrderInfo())
        saasAssignDriverRequestType.setOperator(buildSaaSOperatorInfo())
        saasAssignDriverRequestType.setSupplier(buildSaaSSupplierInfo())
        saasAssignDriverRequestType.setCar(buildSaaSSelfCarInfo())
        saasAssignDriverRequestType.setDriver(buildSaaSSelfDriverInfo())
        def cmd = SaaSOperateDriverCarConverter.converter(saasAssignDriverRequestType)
        return cmd
    }


    SaaSOperateDriverCarCommand getSelfReqNewProcess() {
        def saasAssignDriverRequestType = new SaasAssignDriverRequestType()
        saasAssignDriverRequestType.setOrder(buildSaaSOrderInfo())
        saasAssignDriverRequestType.setOperator(buildSaaSOperatorInfo())
        saasAssignDriverRequestType.setSupplier(buildSaaSSupplierInfo())
        saasAssignDriverRequestType.setCar(buildSaaSSelfCarInfo())
        saasAssignDriverRequestType.setDriver(buildSaaSSelfDriverInfo())
        saasAssignDriverRequestType.getDriver().setDriverVisibleRemark("eer")
        saasAssignDriverRequestType.getDriver().setDriverSettleCurrency("1")
        saasAssignDriverRequestType.getDriver().setDriverSettlePrice(BigDecimal.TEN)
        saasAssignDriverRequestType.setNewProcess(Boolean.TRUE)
        saasAssignDriverRequestType.setCheckCode(1)
        def cmd = SaaSOperateDriverCarConverter.converter(saasAssignDriverRequestType)
        return cmd
    }


    DspOrderVO getDspOrderVO() {
        DspOrderVO order = new DspOrderVO()
        order.setDspOrderId("16211561979363356")
        order.setSupplierId(111)
        order.setCountryId(1111L)
        order.setCityId(12)
        order.setCategoryCode(CategoryCodeEnum.TO_AIRPORT)
        return order
    }

    DspOrderVO getDspOrderVO_NoSupplier() {
        DspOrderVO order = new DspOrderVO()
        order.setDspOrderId("16211561979363356")
        order.setSupplierId(null)
        order.setCountryId(1111L)
        order.setCityId(12)
        order.setUserOrderId("213456575")
        order.setCategoryCode(CategoryCodeEnum.TO_AIRPORT)
        return order
    }

    SubSkuVO getSubSkuVO() {
        SubSkuVO order = new SubSkuVO()
        order.setDspType(DspType.VBK_ASSIGN)
        order.setTakenType(TakenType.SUPPLIER)
        order.setSubSkuId(1)
        return order
    }


    VehicleVO getVehicleVO() {
        VehicleVO order = new VehicleVO()
        order.setCarId(16211561979363356L)
        order.setCarLicense("京牌")
        order.setCarTypeId(1)

        return order
    }

    DriverVO getDriverVO() {
        DriverVO order = new DriverVO()
        order.setDriverId(16211561979363356L)
        return order
    }


    CheckModel getCheckModel() {
        DspModelVO dspModelVO = new DspModelVO()
        CheckModel order = new CheckModel(dspModelVO)
        DspModelVO dspModel = new DspModelVO()
        TransportGroupVO transportGroupVO = new TransportGroupVO()
        transportGroupVO.setTransportGroupMode(TransportGroupMode.DEFAULT_MANUAL_DISPATCH)
        transportGroupVO.setTransportGroupId(111)
        dspModel.setTransportGroup(transportGroupVO)
        order.setModel(dspModel)
        return order
    }

    DriverOrderVO getDriverOrder() {
        DriverOrderVO order = new DriverOrderVO()
        order.setDriverOrderId("111")
        order.setDriverId(16211561979363356L)
        order.setDspOrderId("16211561979363356")
        return order
    }

    SaaSOrderInfo buildSaaSOrderInfo() {
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.TRIP.getCode())
        return saaSOrderInfo
    }

    SaaSOrderInfo buildSaaSOtherOrderInfo() {
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.DISTRIBUTOR.getCode())
        return saaSOrderInfo
    }

    SaaSDriverInfo buildSaaSSelfDriverInfo() {
        SaaSDriverInfo saaSDriverInfo = new SaaSDriverInfo()
        saaSDriverInfo.setDriverId("111")
        saaSDriverInfo.setIsSelfDriver(1)
        return saaSDriverInfo
    }

    SaaSCarInfo buildSaaSSelfCarInfo() {
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarId(111)
        return saaSCarInfo
    }

    SaaSDriverInfo buildSaaSOtherDriverInfo() {
        SaaSDriverInfo saaSDriverInfo = new SaaSDriverInfo()
        saaSDriverInfo.setIsSelfDriver(0)
        saaSDriverInfo.setDriverLanguage("yy")
        saaSDriverInfo.setDriverMobile("15555555555")
        saaSDriverInfo.setDriverMobileCountryCode("1111")
        saaSDriverInfo.setDriverName("xiao_ming")
        saaSDriverInfo.setDriverOtherContract("other")
        return saaSDriverInfo
    }

    SaaSCarInfo buildSaaSOtherCarInfo() {
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarBrandId(1)
        saaSCarInfo.setCarColorId(2)
        saaSCarInfo.setCarLicense("京ACD933")
        saaSCarInfo.setCarSeriesId(3)
        saaSCarInfo.setCarTypeId(4)
        saaSCarInfo.setCarDesc("desc")
        return saaSCarInfo
    }

    SaaSSupplierInfo buildSaaSSupplierInfo() {
        SaaSSupplierInfo saaSSupplierInfo = new SaaSSupplierInfo()
        saaSSupplierInfo.setSupplierId(111L)
        saaSSupplierInfo.setSupplierName("供应商")
        return saaSSupplierInfo
    }

    SaaSOperatorInfo buildSaaSOperatorInfo() {
        SaaSOperatorInfo saaSOperatorInfo = new SaaSOperatorInfo()
        saaSOperatorInfo.setOperatorUserAccount("1111")
        saaSOperatorInfo.setOperatorUserName("操作人")
        saaSOperatorInfo.setOperatorUserType("systemUser")
        return saaSOperatorInfo
    }


}
