package com.ctrip.dcs.application.listener

import com.ctrip.dcs.application.command.ExpireGrabOrderExeCmd
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ExpireGrabOrderListenerTest extends Specification {
    @Mock
    ExpireGrabOrderExeCmd expireGrabOrderExeCmd
    @Mock
    GrabCentreRepository grabCentreRepository
    @Mock
    GrabOrderDetailRepository grabOrderDetailRepository

    @InjectMocks
    ExpireGrabOrderListener expireGrabOrderListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Qunar Dispatcher Confirm Message"() {
        when:
        expireGrabOrderListener.onQunarDispatcherConfirmMessage(new BaseMessage())

        BaseMessage message = new BaseMessage()
        message.setProperty("supplyOrderId", "supplyOrderId")
        expireGrabOrderListener.onQunarDispatcherConfirmMessage(message)

        then:
        verify(grabCentreRepository, times(1)).deleteAll(any())
    }

    def "test on onMessage"() {
        when:
        BaseMessage message = new BaseMessage()
        message.setProperty("supplyOrderId", "supplyOrderId")
        message.setProperty("driverIds", "1,2,3")
        def res = expireGrabOrderListener.onMessage(message)

        then:
        res == null
    }

    def "test on onMessage2"() {
        when:
        BaseMessage message = new BaseMessage()
        message.setProperty("driverId", "1")
        def res = expireGrabOrderListener.onMessage(message)

        then:
        res == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme