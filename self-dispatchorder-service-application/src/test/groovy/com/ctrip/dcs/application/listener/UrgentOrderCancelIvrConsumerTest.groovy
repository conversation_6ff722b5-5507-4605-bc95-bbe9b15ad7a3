package com.ctrip.dcs.application.listener

import com.ctrip.dcs.domain.common.service.IvrCallService
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class UrgentOrderCancelIvrConsumerTest extends Specification {
    @Mock
    IvrCallService ivrCallService
    @InjectMocks
    UrgentOrderCancelIvrConsumer urgentOrderCancelIvrConsumer

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test order Taken"() {
        given:

        when(ivrCallService.cancelIvrRecord(anyString(), any())).thenReturn(true)

        when:
        Message message = new BaseMessage();
        urgentOrderCancelIvrConsumer.orderTaken(message)

        then:
        message != null//todo - validate something
    }

    def "test order Cancel"() {
        given:
        when(ivrCallService.cancelIvrRecord(anyString(), any())).thenReturn(true)

        when:
        Message message = new BaseMessage();
        urgentOrderCancelIvrConsumer.orderCancel(message)

        then:
        message != null//todo - validate something
    }
    def "test order Cancel not Empty"() {
        given:
        when(ivrCallService.cancelIvrRecord(anyString(), any())).thenReturn(true)

        when:
        Message message = new BaseMessage();
        message.setProperty("supplyOrderIds","122");
        urgentOrderCancelIvrConsumer.orderCancel(message)

        then:
        message != null//todo - validate something
    }

    def "test supply Order Confirm Push IM"() {
        given:
        when(ivrCallService.cancelIvrRecord(anyString(), any())).thenReturn(true)

        when:
        Message message = new BaseMessage();
        message.setProperty("confirmType","10");
        message.setProperty("supplierOrderId","121212");
        urgentOrderCancelIvrConsumer.supplyOrderConfirm(message)

        then:
        message != null//todo - validate something
    }

    def "test supply Order Confirm"() {
        given:
        when(ivrCallService.cancelIvrRecord(anyString(), any())).thenReturn(true)

        when:
        Message message = new BaseMessage();
        message.setProperty("confirmType","20");
        message.setProperty("supplierOrderId","121212");
        urgentOrderCancelIvrConsumer.supplyOrderConfirm(message)

        then:
        message != null//todo - validate something
    }
}