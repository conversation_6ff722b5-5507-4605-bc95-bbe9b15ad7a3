package com.ctrip.dcs.application.provider.executor

import com.ctrip.dcs.application.command.ExecuteScheduleExeCmd
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import com.ctrip.dcs.self.dispatchorder.interfaces.ExecuteScheduleRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.ExecuteScheduleResponseType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ExecuteScheduleExecutorTest extends Specification {
    @Mock
    ExecuteScheduleExeCmd cmd
    @Mock
    ScheduleRepository scheduleRepository
    @Mock
    ExecuteScheduleRequestType requestType
    @Mock
    ScheduleDO schedule
    @InjectMocks
    ExecuteScheduleExecutor executeScheduleExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(scheduleRepository.find(anyLong())).thenReturn(schedule)
        when(schedule.getDspOrderId()).thenReturn("1")
        when(schedule.getScheduleId()).thenReturn(1L)

        when:
        ExecuteScheduleResponseType result = executeScheduleExecutor.execute(requestType)

        then:
        result != null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme