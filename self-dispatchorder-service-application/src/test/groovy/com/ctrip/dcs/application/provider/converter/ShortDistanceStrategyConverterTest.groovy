package com.ctrip.dcs.application.provider.converter

import com.ctrip.dcs.application.command.dto.QueryShortDistanceStrategyResDTO
import com.ctrip.dcs.domain.dsporder.entity.ShortDistanceStrategyDO
import com.ctrip.dcs.domain.dsporder.entity.ShortDistanceStrategyDateDTO
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryShortDistanceStrategyRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.SaveShortDistanceStrategyRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.ShortDistanceStrategyDateInfo
import com.ctrip.igt.PaginationDTO
import com.ctrip.igt.PaginatorDTO
import spock.lang.Specification
import spock.lang.Unroll

import java.time.LocalDate
import java.time.LocalDateTime

class ShortDistanceStrategyConverterTest extends Specification {
    def converter = new ShortDistanceStrategyConverter()

    @Unroll
    def "测试SaveRequest转换逻辑: #caseName"() {
        given: "准备请求对象"
        def request = new SaveShortDistanceStrategyRequestType(
                id: 1L,
                code: "STRAT_001",
                cityId: 1001L,
                categoryCodeList: ["CAT1", "CAT2"],
                channelIdList: ["CH1", "CH2"],
                startDis: 0,
                endDis: 10,
                vehicleGroupIdList: [2001L, 2002L],
                startEndDateList: dates,
                state: 1,
                operator: "admin"
        )

        when: "执行转换方法"
        def command = converter.convert(request)

        then: "验证转换结果"
        with(command.shortDistanceStrategy) {
            id == 1L
            code == "STRAT_001"
            cityId == 1001L
            categoryCodeList == ["CAT1", "CAT2"]
            channelIdList == ["CH1", "CH2"]
            startDis == 0
            endDis == 10
            vehicleGroupIdList == [2001L, 2002L]
            startEndDateBjList*.startDate == expectedStartDates
            startEndDateBjList*.endDate == expectedEndDates
            state == 1
            operator == "admin"
        }

        where:
        caseName       | dates                                  | expectedStartDates                                           | expectedEndDates
        "正常日期转换" | [dateInfo("2023-01-01", "2023-01-31")] | [LocalDate.parse("2023-01-01")]                              | [LocalDate.parse("2023-01-31")]
        "空日期列表"   | null                                   | []                                                           | []
        "多日期条目"   | [dateInfo("2023-02-01", "2023-02-28"),
                          dateInfo("2023-03-01", "2023-03-31")] | ["2023-02-01", "2023-03-01"].collect { LocalDate.parse(it) } | ["2023-02-28", "2023-03-31"].collect { LocalDate.parse(it) }
    }

    @Unroll
    def "测试QueryCondition转换逻辑: #caseName"() {
        given: "准备请求对象"
        def request = new QueryShortDistanceStrategyRequestType(
                id: idParam,
                code: "SEARCH_001",
                cityId: 1001L,
                categoryCodeList: ["CAT1", "CAT2"],
                channelIdList: ["CH1", "CH2"],
                vehicleGroupIdList: [3001L, 3002L],
                state: 1,
                paginator: new PaginatorDTO(pageNo: 2, pageSize: 20)
        )

        when: "执行转换方法"
        def condition = converter.convert(request)

        then: "验证转换结果"
        with(condition) {
            id == idParam
            code == "SEARCH_001"
            cityId == 1001L
            categoryCodeList == ["CAT1", "CAT2"]
            channelIdList == ["CH1", "CH2"]
            vehicleGroupIdList == [3001L, 3002L]
            state == 1
            paginator.pageNo == 2
            paginator.pageSize == 20
        }

        where:
        caseName     | idParam
        "包含ID查询" | 100L
        "无ID查询"   | null
    }

    def "测试QueryResponse转换逻辑"() {
        given: "准备DTO对象"
        def input = new QueryShortDistanceStrategyResDTO([
                createStrategyDO(1L, LocalDateTime.parse("2023-01-01T10:00:00")),
                createStrategyDO(2L, LocalDateTime.parse("2023-01-02T11:00:00"))],
                new PaginationDTO(totalSize: 2, pageSize: 10, totalPages: 1, pageNo: 1)
        )

        when: "执行转换方法"
        def output = converter.convert(input)

        then: "验证转换结果"
        with(output) {
            configData.size() == 2
            pagination.totalSize == 2

            configData.eachWithIndex { item, index ->
                assert item.id == (index + 1).longValue()
                assert item.createTime == "2023-01-0${index + 1} 1${index}:00:00"
            }

            configData[0].startEndDateList[0].startDate == "2023-01-01"
            configData[1].startEndDateList[0].endDate == "2023-01-31"
        }
    }

    @Unroll
    def "测试日期格式转换: #caseName"() {
        given: "准备日期对象"
        def dateDTO = new ShortDistanceStrategyDateDTO(
                startDate: LocalDate.parse(startStr),
                endDate: LocalDate.parse(endStr)
        )

        when: "执行日期转换"
        def info = converter.convert(dateDTO)

        then: "验证日期格式"
        info.startDate == startStr
        info.endDate == endStr

        where:
        caseName     | startStr     | endStr
        "普通日期"   | "2023-01-01" | "2023-01-31"
        "闰年日期"   | "2024-02-29" | "2024-03-01"
        "跨年月日期" | "2022-12-31" | "2023-01-01"
    }

    // 辅助方法
    private ShortDistanceStrategyDateInfo dateInfo(String start, String end) {
        new ShortDistanceStrategyDateInfo(startDate: start, endDate: end)
    }

    private ShortDistanceStrategyDO createStrategyDO(Long id, LocalDateTime createTime) {
        new ShortDistanceStrategyDO(
                id: id,
                code: "STRAT_${id}",
                startEndDateBjList: [
                        new ShortDistanceStrategyDateDTO(
                                startDate: LocalDate.parse("2023-01-01"),
                                endDate: LocalDate.parse("2023-01-31")
                        )
                ],
                createTime: createTime
        )
    }
}
