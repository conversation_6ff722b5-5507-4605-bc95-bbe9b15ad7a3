package com.ctrip.dcs.application.command

import com.ctrip.dcs.application.command.api.UpdateOrderSettlementBeforeTakenCommand
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.enums.OrderVersionEnum
import com.ctrip.dcs.domain.common.service.QueryOrderSettlePriceService
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDetailDO
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository
import com.ctrip.dcs.domain.dsporder.value.OrderSettlePriceVO
import com.ctrip.dcs.infrastructure.adapter.dto.SyncSettleToDriverDTO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class UpdateOrderSettlementBeforeTakenCmdTest extends Specification {
    @Mock
    Logger logger
    @Mock
    QueryOrderSettlePriceService queryOrderSettlePriceService
    @Mock
    DspOrderDetailRepository dspOrderDetailRepository
    @Mock
    Map<String,String> commonConf;
    @InjectMocks
    UpdateOrderSettlementBeforeTakenCmd updateOrderSettlementBeforeTakenCmd

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        UpdateOrderSettlementBeforeTakenCommand command1 = new UpdateOrderSettlementBeforeTakenCommand(userOrderId: "1", dspOrderId: "1", carTypeId: 117, categoryCode: CategoryCodeEnum.FROM_AIRPORT.toString(), supplierId: 110L, serviceProviderId: 1, orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), orderVersion: OrderVersionEnum.CTRIP.getVersion(), settleToDriver: 1)
        UpdateOrderSettlementBeforeTakenCommand command2 = new UpdateOrderSettlementBeforeTakenCommand(userOrderId: "1", dspOrderId: "1", carTypeId: 117, categoryCode: CategoryCodeEnum.FROM_AIRPORT.toString(), supplierId: 110L, serviceProviderId: 1, orderSourceCode: OrderSourceCodeEnum.TRIP.getCode(), orderVersion: OrderVersionEnum.QUNAR.getVersion(), settleToDriver: 1)
        OrderSettlePriceVO v1 = OrderSettlePriceVO.builder().supplierId(1L).dspOrderId("1").settleToDriver(1).build()
        when(queryOrderSettlePriceService.queryOrderSettlePrice(anyString(), anyInt(), anyString(), anyLong(), anyInt(), anyInt())).thenReturn(v1)
        when(dspOrderDetailRepository.find(anyString())).thenReturn(new DspOrderDetailDO())
        when(commonConf.get(anyString())).thenReturn("on")

        when:
        updateOrderSettlementBeforeTakenCmd.execute(command1)
        updateOrderSettlementBeforeTakenCmd.execute(command2)

        then:
        command1.getOrderVersion() == 5
        command2.getOrderVersion() == 4
    }

    def "test toSyncSettleToDriverDTO"() {
        given:
        UpdateOrderSettlementBeforeTakenCommand command = new UpdateOrderSettlementBeforeTakenCommand(userOrderId: "1", dspOrderId: "1")
        OrderSettlePriceVO v1 = OrderSettlePriceVO.builder().supplierId(1L).dspOrderId("1").settleToDriver(1).build()

        when:
        SyncSettleToDriverDTO result = updateOrderSettlementBeforeTakenCmd.toSyncSettleToDriverDTO(v1)

        then:
        result.getOrderId() == v1.getDspOrderId()
        result.getPreSupplierSettleAmount() == v1.getPreSupplierSettleAmount()
        result.getPreSupplierSettleCurrency() == v1.getPreSupplierSettleCurrency()
        result.getPreDriverSettleAmount() == v1.getPreDriverSettleAmount()
        result.getPreDriverSettleCurrency() == v1.getPreDriverSettleCurrency()
    }

    def "test update Order Detail Extend Info"() {
        given:
        DspOrderDetailDO dspOrderDetailDO = new DspOrderDetailDO()
        OrderSettlePriceVO v1 = OrderSettlePriceVO.builder().supplierId(1L).dspOrderId("1").settleToDriver(1).build()
        when(dspOrderDetailRepository.find(anyString())).thenReturn(dspOrderDetailDO)

        when:
        updateOrderSettlementBeforeTakenCmd.updateOrderDetailExtendInfo(v1)

        then:
        dspOrderDetailDO.getExtendInfo() != null
    }

    def "test put Extend Info Map"() {
        OrderSettlePriceVO v1 = OrderSettlePriceVO.builder().supplierId(1L).dspOrderId("1").preDriverSettleAmount(BigDecimal.ONE).preDriverSettleCurrency("CNY").settleToDriver(1).build()
        OrderSettlePriceVO v2 = OrderSettlePriceVO.builder().supplierId(1L).dspOrderId("1").preSupplierSettleAmount(BigDecimal.TEN).preSupplierSettleCurrency("CNX").settleToDriver(0).build()
        Map<String, Object> m1 = new HashMap<String, Object>()
        Map<String, Object> m2 = new HashMap<String, Object>()
        when:
        updateOrderSettlementBeforeTakenCmd.putExtendInfoMap(v1, m1)
        updateOrderSettlementBeforeTakenCmd.putExtendInfoMap(v2, m2)

        then:
        m1.get("settleToDriver") == 1
        m1.get("preDriverSettleAmount") == BigDecimal.ONE
        m1.get("preDriverSettleCurrency") == "CNY"
        m2.get("settleToDriver") == 0
        m2.get("preSupplierSettleAmount") == BigDecimal.TEN
        m2.get("preSupplierSettleCurrency") == "CNX"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme