package com.ctrip.dcs.application.listener

import qunar.tc.qmq.NeedRetryException
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class ExecuteScheduleListenerTest extends Specification {

    ExecuteScheduleListener listener = new ExecuteScheduleListener()

    def "OnMessage Error"() {
        given:

        when:
        listener.onMessage(null)

        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "ExecuteScheduleListenerError"
    }
}
