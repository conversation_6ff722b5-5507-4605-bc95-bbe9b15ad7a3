package executor

import com.ctrip.dcs.application.command.SaaSUpdateDspOrderExeCmd
import com.ctrip.dcs.application.provider.executor.SaasUpdateDspOrderExecutor
import com.ctrip.dcs.infrastructure.common.util.LocalDateUtils
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasUpdateDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasUpdateDspOrderResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSExtendInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSExtraFlightInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSFeeInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSPoiInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSUserCountInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaaSXproductInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.SaasUpdateDspOrderInfo
import com.ctrip.igt.framework.common.exception.BizException
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

class SaasUpdateDspOrderExecutorTest extends Specification {

    def saaSUpdateDspOrderExeCmd = Mock(SaaSUpdateDspOrderExeCmd)

    def executor = new SaasUpdateDspOrderExecutor(
            saaSUpdateDspOrderExeCmd: saaSUpdateDspOrderExeCmd,

    )

    @Unroll
    def "test execute suc"() {

        given: "Mock数据"
        SaasUpdateDspOrderInfo saasUpdateDspOrderInfo = buildSaasUpdateDspOrderInfo()
        def request = new SaasUpdateDspOrderRequestType(null, null, saasUpdateDspOrderInfo, 1L)
        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true
    }


    def "test execute fail"() {
        given: "Mock数据"
        SaasUpdateDspOrderInfo saasUpdateDspOrderInfo = buildSaasUpdateDspOrderInfo()
        def request = new SaasUpdateDspOrderRequestType(null, null, saasUpdateDspOrderInfo, null)
        request.setUpdateDspOrderInfo(saasUpdateDspOrderInfo)
        request.getUpdateDspOrderInfo().setVbkOrderId(getVbkOrderId)
        request.getUpdateDspOrderInfo().setDspOrderId(getDspOrderId)
        request.getUpdateDspOrderInfo().setEstimatedUseTime(getEstimatedUseTime)
        request.getUpdateDspOrderInfo().setOrderSourceCode(getOrderSourceCode)
        request.getUpdateDspOrderInfo().setFromPoi(getFromPoi)
        request.getUpdateDspOrderInfo().setToPoi(getToPoi)
        if (getUpdateDspOrderInfo) {
            request.setUpdateDspOrderInfo(null)
        }
        when: "执行校验方法"
        def responseType = executor.execute(request)
        then: "验证校验结果"
        responseType.responseResult.success == false

        where:
        getUpdateDspOrderInfo | getVbkOrderId | getDspOrderId | getEstimatedUseTime | getOrderSourceCode | getFromPoi        | getToPoi
        true                  | "1"           | "1"           | null                | null               | null              | null
        false                 | null          | "1"           | null                | null               | null              | null
        false                 | "1"           | null          | null                | null               | null              | null
        false                 | "1"           | "1"           | "2024-01-01"        | null               | null              | null
        false                 | "1"           | "1"           | "2024-01-01"        | null               | null              | null
        false                 | "1"           | "1"           | "2024-01-01"        | 1                  | null              | null
        false                 | "1"           | "1"           | "2024-01-01"        | 1                  | new SaaSPoiInfo() | null

    }

    SaasUpdateDspOrderInfo buildSaasUpdateDspOrderInfo() {
        SaasUpdateDspOrderInfo saasUpdateDspOrderInfo = new SaasUpdateDspOrderInfo()
        saasUpdateDspOrderInfo.setDspOrderId("222")
        saasUpdateDspOrderInfo.setVbkOrderId("111")
        saasUpdateDspOrderInfo.setEstimatedUseTime(LocalDateUtils.toString(new Date(), "yyyy-MM-dd HH:mm:ss"))
        saasUpdateDspOrderInfo.setOrderSourceCode(2)
        SaaSPoiInfo fromPoi = new SaaSPoiInfo()
        fromPoi.setAddress("beijing")
        fromPoi.setCityId("111")
        saasUpdateDspOrderInfo.setFromPoi(fromPoi)

        SaaSPoiInfo toPoi = new SaaSPoiInfo()
        toPoi.setAddress("beijing")
        toPoi.setCityId("111")
        saasUpdateDspOrderInfo.setToPoi(toPoi)

        SaaSExtendInfo extendInfo = new SaaSExtendInfo()
        extendInfo.setDriverRemark("司机可见备注")
        saasUpdateDspOrderInfo.setExtendInfo(extendInfo)

        return saasUpdateDspOrderInfo
    }

    def "test execute"() {
        given:
        SaasUpdateDspOrderInfo saasUpdateDspOrderInfo = buildSaasUpdateDspOrderInfo();
        saasUpdateDspOrderInfo.setNewProcess(1)
        saasUpdateDspOrderInfo.setUserOrderId("12321312")
        saasUpdateDspOrderInfo.setVehicleGroupName("VehicleGroupName")
        saasUpdateDspOrderInfo.setEstimatedKm(estimatedKm)
        saasUpdateDspOrderInfo.setLastConfirmCarTime(lastConfirmCarTime)
        SaaSUserCountInfo userCountInfo = new SaaSUserCountInfo();
        userCountInfo.setAdultCount(1)
        saasUpdateDspOrderInfo.setUserCount(userCountInfo)
        SaasUpdateDspOrderRequestType requestType = new SaasUpdateDspOrderRequestType();
        requestType.setUpdateDspOrderInfo(saasUpdateDspOrderInfo);

        when:
        SaasUpdateDspOrderResponseType response = executor.execute(requestType);

        then:
        Assert.assertTrue(response.getResponseResult().isSuccess());

        where:
        estimatedKm        | lastConfirmCarTime    || expect
        null               | null                  || Boolean.TRUE
        new BigDecimal(10) | "2024-08-03 11:00:00" || Boolean.TRUE
    }


    def "test validParam is failed"() {
        given:
        SaasUpdateDspOrderRequestType requestType = new SaasUpdateDspOrderRequestType();
        SaasUpdateDspOrderInfo updateDspOrderInfo = new SaasUpdateDspOrderInfo();
        requestType.setUpdateDspOrderInfo(updateDspOrderInfo)
        updateDspOrderInfo.setUserOrderId(userOrderId);
        updateDspOrderInfo.setOrderSourceCode(orderSourceCode)
        updateDspOrderInfo.setVehicleGroupName(vehicleGroupName)
        updateDspOrderInfo.setEstimatedKm(estimatedKm)
        updateDspOrderInfo.setLastConfirmCarTime(lastConfirmCarTime)

        when:
        executor.validParam(requestType);

        then:
        def ex = thrown(BizException);
        Assert.assertTrue(ex != null)

        where:
        userOrderId | orderSourceCode | vehicleGroupName               | estimatedKm                       | lastConfirmCarTime               || expect
        null        | null            | null                           | null                              | null                             || null
        "11111"     | null            | null                           | null                              | null                             || null
        "11111"     | 2               | null                           | null                              | null                             || null
        "11111"     | 2               | buildTooManyvehicleGroupName() | null                              | null                             || null
        "11111"     | 2               | "vehicleGroupName"             | new BigDecimal(-1)                | null                             || null
        "11111"     | 2               | "vehicleGroupName"             | new BigDecimal(1000000000000001L) | null                             || null
        "11111"     | 2               | "vehicleGroupName"             | new BigDecimal(10)                | buildToMannylastConfirmCarTime() || null
        "11111"     | 2               | "vehicleGroupName"             | new BigDecimal(10)                | "2024-08-03 11:11:11"            || null
    }


    def "test validExtendInfo success"() {
        given:
        SaaSExtendInfo saaSExtendInfo = new SaaSExtendInfo();
        saaSExtendInfo.setVbkDistributorName(vbkDistributorName)
        saaSExtendInfo.setRemark(remark);

        when:
        executor.validExtendInfo(saaSExtendInfo);

        then:
        Objects.equals(saaSExtendInfo.getVbkDistributorName(), vbkDistributorName)

        where:
        vbkDistributorName   | remark   || expect
        null                 | null     || null
        "VbkDistributorName" | "Remark" || null
    }

    def "test validExtendInfo failed"() {
        given:
        SaaSExtendInfo saaSExtendInfo = new SaaSExtendInfo();
        saaSExtendInfo.setVbkDistributorName(vbkDistributorName)
        saaSExtendInfo.setRemark(remark);

        when:
        executor.validExtendInfo(saaSExtendInfo);

        then:
        def ex = thrown(BizException);
        Assert.assertTrue(ex != null)

        where:
        vbkDistributorName                | remark                || expect
        buildTooMannyVbkDistributorName() | null                  || null
        "VbkDistributorName"              | buildTooMannyRemark() || null
    }

    def "test validPoiInfo success"() {
        given:
        SaaSPoiInfo fromPoi = new SaaSPoiInfo();
        fromPoi.setAddress("fromAddress")
        SaaSPoiInfo toPoi = new SaaSPoiInfo();
        toPoi.setAddress("toAddress")

        when:
        executor.validPoiInfo(fromPoi, toPoi);

        then:
        Objects.equals(fromPoi.getAddress(), "fromAddress");
    }

    def "test validPoiInfo failed"() {
        given:
        SaaSPoiInfo fromPoi = new SaaSPoiInfo();
        fromPoi.setAddress(fromAddress)
        SaaSPoiInfo toPoi = new SaaSPoiInfo();
        toPoi.setAddress(toAddress)

        when:
        executor.validPoiInfo(fromPoi, toPoi);

        then:
        def ex = thrown(BizException);
        Assert.assertTrue(ex != null)

        where:
        fromAddress           | toAddress             || expect
        null                  | null                  || null
        buildToMannyAddress() | null                  || null
        "fromAddress"         | null                  || null
        "fromAddress"         | buildToMannyAddress() || null
    }

    def "test validUserCount success"() {
        given:
        SaaSUserCountInfo userCountInfo = new SaaSUserCountInfo();
        userCountInfo.setAdultCount(adultCount);
        userCountInfo.setChildCount(childCount);
        userCountInfo.setBagCount(bagCount);

        when:
        executor.validUserCount(userCountInfo);

        then:
        Objects.equals(userCountInfo.getAdultCount(), adultCount);

        where:
        adultCount | childCount | bagCount
        1          | null       | null
        1          | 1          | 1
    }

    def "test validUserCount failed"() {
        given:
        SaaSUserCountInfo userCountInfo = new SaaSUserCountInfo();
        userCountInfo.setAdultCount(adultCount);
        userCountInfo.setChildCount(childCount);
        userCountInfo.setBagCount(bagCount);

        when:
        executor.validUserCount(userCountInfo);

        then:
        def ex = thrown(BizException);
        Assert.assertTrue(ex != null);

        where:
        adultCount | childCount | bagCount
        0          | null       | null
        55         | null       | null
        1          | 0          | null
        1          | 55         | null
        1          | 2          | 0
        1          | 2          | 55
    }

    def "test validFeeInfo"() {
        given:
        SaaSFeeInfo saaSFeeInfo = new SaaSFeeInfo();
        saaSFeeInfo.setCostAmount(costAmount)

        when:
        executor.validFeeInfo(saaSFeeInfo);

        then:
        def ex = thrown(BizException);
        ex.code == expect

        where:
        costAmount                             || expect
        new BigDecimal("-1")                   || "09011000"
        BigDecimal.valueOf(10000000000000001L) || "09011000"
    }

    def "test xproductInfo"() {
        given:
        SaaSXproductInfo xproductInfo = new SaaSXproductInfo();
        xproductInfo.setPackageName(packageName)
        xproductInfo.setAdditionalServices(addition)

        when:
        executor.validXproductInfo(xproductInfo);

        then:
        def ex = thrown(BizException);
        ex.code == expect

        where:
        packageName                    | addition                          || expect
        buildTooManyvehicleGroupName() | buildTooMannyVbkDistributorName() || "09011000"
        "123"                          | buildTooMannyVbkDistributorName() || "09011000"
    }

    def "test SaaSExtraFlightInfo"() {
        given:
        SaaSExtraFlightInfo flightInfo = new SaaSExtraFlightInfo();
        flightInfo.setTerminalName(packageName)

        when:
        executor.validFlightInfo(flightInfo);

        then:
        def ex = thrown(BizException);
        ex.code == expect

        where:
        packageName                     || expect
        buildTooManyvehicleGroupName()  || "09011000"
    }

    def String buildTooManyvehicleGroupName() {
        return "1221312321321213313123221312321321213313123221312321321213313123221312321321213313123221312321321213313123221312321321213313123221312321321213313123221312" +
                "221312321321213313123221312321321213313123221312321321213313123221312321321213313123221312321321213313123221312321321213313123221312321321213313123221312321321213313123" +
                "221312321321213313123221312321321213313123221312321321213313123221312321321213313123321321213313123";
    }

    def String buildToMannylastConfirmCarTime() {
        return "1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111";
    }

    def String buildTooMannyVbkDistributorName() {
        return "VbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistri" +
                "VbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorName" +
                "VbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorName" +
                "VbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorName" +
                "VbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorName" +
                "VbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorName" +
                "VbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorName" +
                "VbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorName" +
                "VbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNameVbkDistributorNamebutorName"
    }

    def String buildTooMannyRemark() {
        return "RemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemark" +
                "RemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemark" +
                "RemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemark" +
                "RemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemark" +
                "RemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemarkRemark";
    }

    def String buildToMannyAddress() {
        return "fromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddress" +
                "fromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddress" +
                "fromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddress" +
                "fromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddress" +
                "fromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddress" +
                "fromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddressfromAddress"
    }
}
