package executor

import com.ctrip.dcs.application.command.CreateDspOrderExeCmd
import com.ctrip.dcs.application.command.CreateGrabOrderExeCmd
import com.ctrip.dcs.application.provider.executor.CreateDspOrderExecutor
import com.ctrip.dcs.application.provider.executor.drivergrab.CreateDriverGrabOrdersExecutor
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDriverGrabOrdersRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDriverGrabOrdersResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDspOrderResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.BaseInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.CreateDspOrderInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.FeeInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.ProductInfo
import org.assertj.core.util.Lists
import spock.lang.Specification
import spock.lang.Unroll

class CreateDriverGrabOrdersExecutorTest extends Specification {

    def createGrabOrderExeCmd = Mock(CreateGrabOrderExeCmd)

    def executor = new CreateDriverGrabOrdersExecutor(
            createGrabOrderExeCmd: createGrabOrderExeCmd,

    )

    @Unroll
    def "test execute suc"() {

        given: "Mock数据"

        CreateDriverGrabOrdersRequestType req = new CreateDriverGrabOrdersRequestType()
        req.setGrabOrderType(grabOrderType)
        req.setDriverIds(Lists.newArrayList(1L,2L))

        when: "执行校验方法"
        CreateDriverGrabOrdersResponseType execute = executor.execute(req)

        then: "验证校验结果"
        execute.getResponseResult().getReturnCode() == code

        where:

        grabOrderType || code
        1             || "200"
        0             || "200"

    }

    def "test execute fail"() {

        given: "Mock数据"
        CreateDriverGrabOrdersRequestType req = new CreateDriverGrabOrdersRequestType()
        req.setGrabOrderType(3)
        req.setDriverIds(Lists.newArrayList(1L,2L))

        when: "执行校验方法"
        CreateDriverGrabOrdersResponseType execute = executor.execute(req)

        then: "验证校验结果"
        execute.getResponseResult().getReturnCode() == "09011000"


    }
}
