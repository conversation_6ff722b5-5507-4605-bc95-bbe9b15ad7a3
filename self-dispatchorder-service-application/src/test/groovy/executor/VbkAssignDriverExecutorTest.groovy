package executor


import com.ctrip.dcs.application.command.VbkAssignDriverExeCmd
import com.ctrip.dcs.application.provider.executor.VbkAssignDriverExecutor
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkAssignDriverRequestType
import spock.lang.Specification
import spock.lang.Unroll

class VbkAssignDriverExecutorTest extends Specification {

    def vbkAssignDriverExeCmd = Mock(VbkAssignDriverExeCmd)

    def executor = new VbkAssignDriverExecutor(
            vbkAssignDriverExeCmd: vbkAssignDriverExeCmd,

    )

    @Unroll
    def "test execute suc"() {

        given: "Mock数据"

        def request = new VbkAssignDriverRequestType(userOrderId: "1", dspOrderId: "1", driverId: 1, operUserName: "xxx",
                operUserType: "xxx", sysUserAccount: "zzz",
                transportGroupId: 1, supplierId: 33)

        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true


    }

    def "test execute fail"() {

        given: "Mock数据"


        def request = new VbkAssignDriverRequestType(userOrderId: "1")

        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == false


    }


}
