package executor

import com.ctrip.dcs.application.command.SaaSCreateDspOrderExeCmd
import com.ctrip.dcs.application.provider.executor.SaasCreateDspOrderExecutor
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.util.JsonUtil
import com.ctrip.dcs.infrastructure.common.util.LocalDateUtils
import com.ctrip.dcs.infrastructure.common.util.LocalJsonUtils
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCreateDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.*
import spock.lang.Specification
import spock.lang.Unroll

class SaasCreateDspOrderExecutorTest extends Specification {

    def saaSCreateDspOrderExeCmd = Mock(SaaSCreateDspOrderExeCmd)

    def executor = new SaasCreateDspOrderExecutor(
            saaSCreateDspOrderExeCmd: saaSCreateDspOrderExeCmd,

    )

    @Unroll
    def "test execute suc"() {

        given: "Mock数据"
        def request = buildCreateDspOrderRequestType()
        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true
    }


    def "test execute fail"() {
        given: "Mock数据"
        def request = new SaasCreateDspOrderRequestType(null, null, null)
        request.setCreateDspOrderInfo(new SaasCreateDspOrderInfo())
        request.getCreateDspOrderInfo().setBaseInfo(getBaseInfo)
        request.getCreateDspOrderInfo().setFromPoi(getFromPoi)
        request.getCreateDspOrderInfo().setToPoi(getToPoi)

        when: "执行校验方法"
        def responseType = executor.execute(request)
        then: "验证校验结果"
        responseType.responseResult.success == false

        where:
        getBaseInfo        | getFromPoi        | getToPoi
        null               | new SaaSPoiInfo() | new SaaSPoiInfo()
        new SaaSBaseInfo() | null              | new SaaSPoiInfo()
        new SaaSBaseInfo() | new SaaSPoiInfo() | null


    }

    SaasCreateDspOrderInfo buildSaasCreateDspOrderInfo() {
        SaasCreateDspOrderInfo saasCreateDspOrderInfo = new SaasCreateDspOrderInfo()
        SaaSBaseInfo baseInfo = new SaaSBaseInfo()
        baseInfo.setDspOrderId(null)
        baseInfo.setVbkOrderId("111")
        baseInfo.setCategoryCode(CategoryCodeEnum.FROM_AIRPORT.getType())
        baseInfo.setCityId(111)
        baseInfo.setFromCityId(111)
        baseInfo.setDeptCity("beijing")
        baseInfo.setToCityId(111)
        baseInfo.setArriveCityName("beijing")
        baseInfo.setVehicleGroupId(1)
        baseInfo.setVehicleGroupName("预订车组名称")
        baseInfo.setEstimatedUseTime(new Date() as String)
        baseInfo.setEstimatedUseTime(LocalDateUtils.toString(new Date(), "yyyy-MM-dd HH:mm:ss"))

        baseInfo.setUseDays(new BigDecimal("1"))
        baseInfo.setSupplierId(1)
        baseInfo.setDriverOrderId("")
        baseInfo.setOrderSourceCode(OrderSourceCodeEnum.DISTRIBUTOR.getCode().toString())
        baseInfo.setOrderCancelReasonDetail(null)
        saasCreateDspOrderInfo.setBaseInfo(baseInfo)

        SaaSPoiInfo fromPoi = new SaaSPoiInfo()
        fromPoi.setAddress("beijing")
        fromPoi.setCityId("111")
        saasCreateDspOrderInfo.setFromPoi(fromPoi)

        SaaSPoiInfo toPoi = new SaaSPoiInfo()
        toPoi.setAddress("beijing")
        toPoi.setCityId("111")
        saasCreateDspOrderInfo.setToPoi(toPoi)

        SaaSExtraFlightInfo extraFlightInfo = new SaaSExtraFlightInfo()
        extraFlightInfo.setFlightNo("BJ103")
        extraFlightInfo.setTerminalName("航站楼名称")
        extraFlightInfo.setFlightArriveTime(LocalDateUtils.toString(LocalDateUtils.addDays(new Date(), 12), "yyyy-MM-dd HH:mm:ss"))
        saasCreateDspOrderInfo.setExtraFlightInfo(extraFlightInfo)


        SaaSXproductInfo xproductInfo = new SaaSXproductInfo()
        xproductInfo.setAdditionalServices("附加服务")
        saasCreateDspOrderInfo.setXproductInfo(xproductInfo)

        SaaSUserCountInfo userCount = new SaaSUserCountInfo()
        userCount.setAdultCount(1)
        userCount.setChildCount(1)
        userCount.setBagCount(1)
        saasCreateDspOrderInfo.setUserCount(userCount)

        SaaSExtendInfo extendInfo = new SaaSExtendInfo()
        extendInfo.setDriverRemark("司机可见备注")
        saasCreateDspOrderInfo.setExtendInfo(extendInfo)


        SaaSFeeInfo feeInfo = new SaaSFeeInfo()
        feeInfo.setCostAmount(new BigDecimal("2.00"))
        feeInfo.setUserCurrency("用户币种")
        feeInfo.setSupplierCurrency("供应币种")
        saasCreateDspOrderInfo.setFeeInfo(feeInfo)
        return saasCreateDspOrderInfo
    }


    SaasCreateDspOrderRequestType buildCreateDspOrderRequestType() {
        String str = "{\n" +
                "        \"createDspOrderInfo\":{\n" +
                "        \"baseInfo\":{\n" +
                "            \"vbkOrderId\":\"123456\",\n" +
                "            \"userOrderId\":\"123456\",\n" +
                "            \"categoryCode\":\"point_to_point\",\n" +
                "            \"cityName\":\"北京市\",\n" +
                "            \"deptCity\":\"北京市\",\n" +
                "            \"arriveCityName\":\"北京市\",\n" +
                "            \"vehicleGroupId\":123,\n" +
                "            \"vehicleGroupName\":\"预订车组名称\",\n" +
                "            \"estimatedUseTime\":\"2024-02-26 12:12:00\",\n" +
                "            \"useDays\":1,\n" +
                "            \"supplierId\":123,\n" +
                "            \"orderSourceCode\":2\n" +
                "        },\n" +
                "        \"xproductInfo\":{\n" +
                "            \"additionalServices\":\"附加服务\"\n" +
                "        },\n" +
                "        \"feeInfo\":{\n" +
                "            \"costAmount\":0,\n" +
                "            \"userCurrency\":\"币种\",\n" +
                "            \"supplierCurrency\":\"币种\"\n" +
                "        },\n" +
                "        \"extraFlightInfo\":{\n" +
                "            \"flightNo\":\"1111\",\n" +
                "            \"flightArriveTime\":\"北京\",\n" +
                "            \"terminalName\":\"北京\"\n" +
                "        },\n" +
                "        \"userCount\":{\n" +
                "            \"adultCount\":1,\n" +
                "            \"childCount\":1,\n" +
                "            \"bagCount\":1\n" +
                "        },\n" +
                "        \"extendInfo\":{\n" +
                "            \"driverRemark\":\"司机可见备注\",\n" +
                "            \"vbkDistributorName\":\"分销商名称\"\n" +
                "        },\n" +
                "        \"fromPoi\":{\n" +
                "            \"address\":\"具体出发点\"\n" +
                "        },\n" +
                "        \"toPoi\":{\n" +
                "            \"address\":\"具体到达点\"\n" +
                "        }\n" +
                "    }\n" +
                "}";
        SaasCreateDspOrderRequestType request = JsonUtil.fromJson(str, SaasCreateDspOrderRequestType.class);
        return request;
    }
}
