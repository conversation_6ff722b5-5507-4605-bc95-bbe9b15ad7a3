package executor

import com.ctrip.dcs.application.command.ReDispatchSubmitExeCmd
import com.ctrip.dcs.application.provider.executor.ReDispatchSubmitExecutor
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.value.ReasonDetailVO
import com.ctrip.dcs.self.dispatchorder.interfaces.ReDispatchSubmitRequestType
import com.ctrip.igt.framework.common.result.Result
import spock.lang.Specification
import spock.lang.Unroll

class ReDispatchSubmitExecutorTest extends Specification {

    def reDispatchSubmitExeCmd = Mock(ReDispatchSubmitExeCmd)

    def executor = new ReDispatchSubmitExecutor(
            reDispatchSubmitExeCmd: reDispatchSubmitExeCmd,

    )

    @Unroll
    def "test execute suc"() {

        given: "Mock数据"
        reDispatchSubmitExeCmd.execute(_) >> Result.Builder.<ReasonDetailVO> newResult().success().build();

        def request = new ReDispatchSubmitRequestType(userOrderId: "1")

        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true


    }

    def "test execute fail"() {

        given: "Mock数据"
        reDispatchSubmitExeCmd.execute(_) >> Result.Builder.<ReasonDetailVO>newResult().fail().
                withCode(ErrorCode.DISPATCH_FREQUENTLY_CALLED_ERROR.getCode()).withMsg(ErrorCode.DISPATCH_FREQUENTLY_CALLED_ERROR.getDesc()).build()


        def request = new ReDispatchSubmitRequestType(userOrderId: "1")

        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == false


    }

    def "test execute fail1"() {

        given: "Mock数据"
        reDispatchSubmitExeCmd.execute(_) >> null

        def request = new ReDispatchSubmitRequestType(userOrderId: "1")

        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == false


    }
}
