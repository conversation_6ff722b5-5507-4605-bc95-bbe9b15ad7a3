package executor

import com.ctrip.dcs.application.command.SaaSOtherChangeDriverExeCmd
import com.ctrip.dcs.application.command.SaaSTripChangeDriverExeCmd
import com.ctrip.dcs.application.provider.executor.SaasChangeDriverExecutor
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.dsporder.repository.CkLogRepository
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasChangeDriverRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.*
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import spock.lang.Specification
import spock.lang.Unroll

class SaasChangeDriverExecutorTest extends Specification {

    def saaSOtherChangeDriverExeCmd = Mock(SaaSOtherChangeDriverExeCmd)
    def saaSTripChangeDriverExeCmd = Mock(SaaSTripChangeDriverExeCmd)
    def ckLogRepository = Mock(CkLogRepository)

    def executor = new SaasChangeDriverExecutor(
            saaSOtherChangeDriverExeCmd: saaSOtherChangeDriverExeCmd,
            saaSTripChangeDriverExeCmd: saaSTripChangeDriverExeCmd,
            ckLogRepository:ckLogRepository
    )

    @Unroll
    def "test execute sucForSelf"() {

        given: "Mock数据"
        def request = new SaasChangeDriverRequestType()
        request.setOrder(buildSaaSOrderInfo())
        request.setDriver(buildSaaSSelfDriverInfo())
        request.setCar(buildSaaSSelfCarInfo())
        request.setSupplier(buildSaaSSupplierInfo())
        request.setOperator(buildSaaSOperatorInfo())
        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true
    }

    @Unroll
    def "test execute sucForOther"() {
        given: "Mock数据"
        def request = new SaasChangeDriverRequestType()
        request.setOrder(buildSaaSOtherOrderInfo())
        request.setDriver(buildSaaSOtherDriverInfo())
        request.setCar(buildSaaSOtherCarInfo())
        request.setSupplier(buildSaaSSupplierInfo())
        request.setOperator(buildSaaSOperatorInfo())
        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true
    }

    def "test execute failForSelf"() {
        given: "Mock数据"
        def request = new SaasChangeDriverRequestType()
        request.setOrder(buildSaaSOrderInfo())
        request.setDriver(buildSaaSSelfDriverInfo())
        request.setSupplier(buildSaaSSupplierInfo())
        request.setOperator(buildSaaSOperatorInfo())
        when: "执行校验方法"
        def responseType = executor.execute(request)
        then: "验证校验结果"
        responseType.responseResult.success == false
    }

    def "test execute failForOther"() {
        given: "Mock数据"
        def request = new SaasChangeDriverRequestType()
        request.setOrder(buildSaaSOrderInfo())
        request.getOrder().setDspOrderId(dspOrderId)
        request.getOrder().setOrderSourceCode(orderSourceCode)
        request.setDriver(buildSaaSSelfDriverInfo())
        request.getDriver().setIsSelfDriver(isSelfDriver)
        request.getDriver().setDriverId(driverId)
        request.getDriver().setDriverName(driverName)
        request.getDriver().setDriverMobile(driverMobile)
        request.getDriver().setCityId(cityId)
        request.setCar(buildSaaSSelfCarInfo())
        request.getCar().setCarId(carId)
        request.getCar().setCarLicense(carLicense)
        request.setSupplier(buildSaaSSupplierInfo())
        request.getSupplier().setSupplierId(supplierId)
        request.setOperator(buildSaaSOperatorInfo())
        request.getOperator().setOperatorUserType(operatorUserType)
        request.getOperator().setOperatorUserAccount(operatorUserAccount)
        request.getOperator().setOperatorUserName(operatorUserName)


        when: "执行校验方法"
        def responseType = executor.validRequestByOrderSourceCode(request)
        then: "验证校验结果"
        !responseType

        where:
        dspOrderId | orderSourceCode | isSelfDriver | driverId | driverName | driverMobile | cityId | carId | carLicense | supplierId | operatorUserType | operatorUserAccount | operatorUserName | confTime || code
        "1"        | null            | null         | "1"      | null       | null         | null   | 5     | null       | 1          | null             | "1111"              | "7"              | null     || "********"
        null       | 1               | null         | "1"      | "220"      | null         | null   | 5     | null       | 1          | null             | "1111"              | "7"              | null     || "********"
        "1"        | 1               | null         | "1"      | "600"      | null         | null   | 5     | null       | null       | null             | "1111"              | "7"              | null     || "********"
        "1"        | 1               | null         | "1"      | "600"      | null         | null   | 5     | null       | 1          | null             | "1111"              | "7"              | null     || "********"
        "1"        | 1               | null         | "1"      | "600"      | null         | null   | 5     | null       | 1          | "1"              | null                | "7"              | null     || "********"
        "1"        | 1               | null         | "1"      | "600"      | null         | null   | 5     | null       | 1          | "1"              | "1111"              | null             | null     || "********"
        "1"        | 1               | null         | "1"      | "600"      | null         | null   | 5     | null       | 1          | "1"              | "1111"              | "1"              | null     || "********"
        "1"        | 1               | 1            | "1"      | "600"      | null         | null   | null  | null       | 1          | "1"              | "1111"              | "1"              | null     || "********"
        "1"        | 1               | 1            | null     | "600"      | null         | 1      | 1     | null       | 1          | "1"              | "1111"              | "1"              | null     || "********"
        "1"        | 2               | 1            | null     | "600"      | null         | 1      | null  | null       | 1          | "1"              | "1111"              | "1"              | null     || "********"
        "1"        | 2               | 0            | null     | "1"        | null         | 1      | null  | null       | 1          | "1"              | "1111"              | "1"              | null     || "********"
        "1"        | 2               | 0            | null     | null       | "1"          | 1      | null  | null       | 1          | "1"              | "1111"              | "1"              | null     || "********"
        "1"        | 2               | 1            | null     | null       | "1"          | null   | null  | null       | 1          | "1"              | "1111"              | "1"              | null     || "********"
        "1"        | 2               | 0            | null     | null       | "1"          | 1      | null  | null       | 1          | "1"              | "1111"              | "1"              | null     || "********"
        "1"        | 2               | 1            | "1"      | "1"        | "1"          | null   | null  | null       | 1          | "1"              | "1111"              | "1"              | null     || "********"

    }


    @Unroll
    def "test execute sucForSelf newProcess"() {

        given: "Mock数据"
        def request = new SaasChangeDriverRequestType()
        request.setOrder(buildSaaSOrderInfo())
        request.setDriver(buildSaaSSelfDriverInfo())
        request.setCar(buildSaaSSelfCarInfo())
        request.setSupplier(buildSaaSSupplierInfo())
        request.setOperator(buildSaaSOperatorInfo())
        request.getOperator().setBatchAssign(false)
        request.getOperator().setNotUpdateSettlePriceAndCurrency()
        request.getOperator().setTkeSource("PC")
        request.setNewProcess(Boolean.TRUE)
        request.setCheckCode(1)
        request.setChangeReason("22")
        request.setChangeReasonDesc("")
        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true
    }

    @Unroll
    def "test execute sucForOther newProcess"() {
        given: "Mock数据"
        def request = new SaasChangeDriverRequestType()
        request.setOrder(buildSaaSOtherOrderInfo())
        request.setDriver(buildSaaSOtherDriverInfo())
        request.setCar(buildSaaSOtherCarInfo())
        request.setSupplier(buildSaaSSupplierInfo())
        request.setOperator(buildSaaSOperatorInfo())
        request.getOperator().setBatchAssign(false)
        request.getOperator().setNotUpdateSettlePriceAndCurrency()
        request.getOperator().setTkeSource("PC")
        request.setNewProcess(Boolean.TRUE)
        request.setCheckCode(1)
        request.setChangeReason("22")
        request.setChangeReasonDesc("")
        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true
    }

    public void validate(AbstractValidator<SaasChangeDriverRequestType> validator) {
        validator.ruleFor("order").notNull();
        validator.ruleFor("driver").notNull();
        validator.ruleFor("car").notNull();
        validator.ruleFor("supplier").notNull();
        validator.ruleFor("operator").notNull();

    }
    def "test execute valid"() {
        given: "Mock数据"
        AbstractValidator validator = new AbstractValidator(SaasChangeDriverRequestType.class);
        when: "执行校验方法"
        def responseType = executor.validate(validator)
        then: "验证校验结果"
        responseType == null
    }

    SaaSOrderInfo buildSaaSOrderInfo() {
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.TRIP.getCode())
        return saaSOrderInfo
    }

    SaaSOrderInfo buildSaaSOtherOrderInfo() {
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.DISTRIBUTOR.getCode())
        return saaSOrderInfo
    }

    SaaSDriverInfo buildSaaSSelfDriverInfo() {
        SaaSDriverInfo saaSDriverInfo = new SaaSDriverInfo()
        saaSDriverInfo.setDriverId("111")
        saaSDriverInfo.setIsSelfDriver(1)
        saaSDriverInfo.setDriverVisibleRemark("test'")
        saaSDriverInfo.setDriverSettleCurrency("test'")
        saaSDriverInfo.setDriverSettlePrice(BigDecimal.TEN)
        saaSDriverInfo.setDriverVisibleRemark("test'")
        return saaSDriverInfo
    }

    SaaSCarInfo buildSaaSSelfCarInfo() {
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarId(111)
        return saaSCarInfo
    }

    SaaSDriverInfo buildSaaSOtherDriverInfo() {
        SaaSDriverInfo saaSDriverInfo = new SaaSDriverInfo()
        saaSDriverInfo.setIsSelfDriver(0)
        saaSDriverInfo.setDriverLanguage("yy")
        saaSDriverInfo.setDriverMobile("15555555555")
        saaSDriverInfo.setDriverMobileCountryCode("1111")
        saaSDriverInfo.setDriverName("xiao_ming")
        saaSDriverInfo.setDriverOtherContract("other")
        return saaSDriverInfo
    }

    SaaSCarInfo buildSaaSOtherCarInfo() {
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarBrandId(1)
        saaSCarInfo.setCarColorId(2)
        saaSCarInfo.setCarLicense("京ACD933")
        saaSCarInfo.setCarSeriesId(3)
        saaSCarInfo.setCarTypeId(4)
        saaSCarInfo.setCarDesc("desc")
        return saaSCarInfo
    }

    SaaSSupplierInfo buildSaaSSupplierInfo() {
        SaaSSupplierInfo saaSSupplierInfo = new SaaSSupplierInfo()
        saaSSupplierInfo.setSupplierId(111L)
        saaSSupplierInfo.setSupplierName("供应商")
        return saaSSupplierInfo
    }

    SaaSOperatorInfo buildSaaSOperatorInfo() {
        SaaSOperatorInfo saaSOperatorInfo = new SaaSOperatorInfo()
        saaSOperatorInfo.setOperatorUserAccount("1111")
        saaSOperatorInfo.setOperatorUserName("操作人")
        saaSOperatorInfo.setOperatorUserType("systemUser")
        return saaSOperatorInfo
    }
}
