package executor

import com.ctrip.dcs.application.command.SaaSOtherBindCarExeCmd
import com.ctrip.dcs.application.command.SaaSTripBindCarExeCmd
import com.ctrip.dcs.application.command.api.SaaSOperateDriverCarCommand
import com.ctrip.dcs.application.provider.converter.SaaSOperateDriverCarConverter
import com.ctrip.dcs.application.provider.executor.SaasBindCarExecutor
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.dsporder.repository.CkLogRepository
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasAssignDriverRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasBindCarRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasChangeDriverRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.*
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import spock.lang.Specification
import spock.lang.Unroll

class SaasBindCarExecutorTest extends Specification {

    def saaSOtherBindCarExeCmd = Mock(SaaSOtherBindCarExeCmd)
    def saaSTripBindCarExeCmd = Mock(SaaSTripBindCarExeCmd)
    def ckLogRepository=Mock(CkLogRepository)

    def executor = new SaasBindCarExecutor(
            saaSOtherBindCarExeCmd: saaSOtherBindCarExeCmd,
            saaSTripBindCarExeCmd: saaSTripBindCarExeCmd,
            ckLogRepository:ckLogRepository
    )

    @Unroll
    def "test execute sucForSelf"() {

        given: "Mock数据"
        def request = new SaasBindCarRequestType()
        request.setOrder(buildSaaSOrderInfo())
        request.setCar(buildSaaSSelfCarInfo())
        request.setSupplier(buildSaaSSupplierInfo())
        request.setOperator(buildSaaSOperatorInfo())
        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true
    }

    @Unroll
    def "test execute sucForSelfNewProcess"() {

        given: "Mock数据"
        def request = new SaasBindCarRequestType()
        request.setOrder(buildSaaSOrderInfo())
        request.setCar(buildSaaSSelfCarInfo())
        request.setSupplier(buildSaaSSupplierInfo())
        request.setOperator(buildSaaSOperatorInfo())
        request.setNewProcess(Boolean.TRUE)
        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true
    }

    @Unroll
    def "test execute sucForOther"() {
        given: "Mock数据"
        def request = new SaasBindCarRequestType()
        request.setOrder(buildSaaSOtherOrderInfo())
        request.setCar(buildSaaSOtherCarInfo())
        request.setSupplier(buildSaaSSupplierInfo())
        request.setOperator(buildSaaSOperatorInfo())
        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true
    }

    @Unroll
    def "test execute sucForOtherNewProcess"() {
        given: "Mock数据"
        def request = new SaasBindCarRequestType()
        request.setOrder(buildSaaSOtherOrderInfo())
        request.setCar(buildSaaSOtherCarInfo())
        request.setSupplier(buildSaaSSupplierInfo())
        request.setOperator(buildSaaSOperatorInfo())
        request.setNewProcess(Boolean.TRUE)
        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true
    }

    def "test execute failForSelf"() {
        given: "Mock数据"
        def request = new SaasBindCarRequestType()
        request.setOrder(buildSaaSOrderInfo())
        request.setSupplier(buildSaaSSupplierInfo())
        request.setOperator(buildSaaSOperatorInfo())
        when: "执行校验方法"
        def responseType = executor.execute(request)
        then: "验证校验结果"
        responseType.responseResult.success == false
    }

    def "test execute failForOther"() {
        given: "Mock数据"
        def request = new SaasBindCarRequestType()
        request.setOrder(buildSaaSOrderInfo())
        request.getOrder().setDspOrderId(dspOrderId)
        request.getOrder().setOrderSourceCode(orderSourceCode)
        request.setCar(buildSaaSSelfCarInfo())
        request.getCar().setCarId(carId)
        request.getCar().setCarLicense(carLicense)
        request.setSupplier(buildSaaSSupplierInfo())
        request.getSupplier().setSupplierId(supplierId)
        request.setOperator(buildSaaSOperatorInfo())
        request.getOperator().setOperatorUserType(operatorUserType)
        request.getOperator().setOperatorUserAccount(operatorUserAccount)
        request.getOperator().setOperatorUserName(operatorUserName)


        when: "执行校验方法"
        def responseType = executor.validRequestByOrderSourceCode(request)
        then: "验证校验结果"
        !responseType

        where:
        dspOrderId | orderSourceCode | isSelfDriver | driverId | driverName | driverMobile | cityId | carId | carLicense | supplierId | operatorUserType | operatorUserAccount | operatorUserName | confTime || code
        "1"        | null            | null         | "1"      | null       | null         | null   | 5     | null       | 1          | null             | "1111"              | "7"              | null     || "********"
        null       | 1               | null         | "1"      | 220        | null         | null   | 5     | null       | 1          | null             | "1111"              | "7"              | null     || "********"
        "1"        | 1               | null         | "1"      | 600        | null         | null   | 5     | null       | null       | null             | "1111"              | "7"              | null     || "********"
        "1"        | 1               | null         | "1"      | 600        | null         | null   | 5     | null       | 1          | null             | "1111"              | "7"              | null     || "********"
        "1"        | 1               | null         | "1"      | 600        | null         | null   | 5     | null       | 1          | "1"              | null                | "7"              | null     || "********"
        "1"        | 1               | null         | "1"      | 600        | null         | null   | 5     | null       | 1          | "1"              | "1111"              | null             | null     || "********"
        "1"        | 1               | null         | "1"      | 600        | null         | null   | null  | null       | 1          | "1"              | "1111"              | "1"              | null     || "********"
        "1"        | 2               | 1            | null     | 600        | null         | 1      | null  | null       | 1          | "1"              | "1111"              | "1"              | null     || "********"

    }


    def "test execute valid"() {
        given: "Mock数据"
        AbstractValidator validator = new AbstractValidator(SaasBindCarRequestType.class);
        when: "执行校验方法"
        def responseType = executor.validate(validator)
        then: "验证校验结果"
        responseType == null
    }
    SaaSOrderInfo buildSaaSOrderInfo() {
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.TRIP.getCode())
        return saaSOrderInfo
    }

    SaaSOrderInfo buildSaaSOtherOrderInfo() {
        SaaSOrderInfo saaSOrderInfo = new SaaSOrderInfo()
        saaSOrderInfo.setDspOrderId("111")
        saaSOrderInfo.setOrderSourceCode(OrderSourceCodeEnum.DISTRIBUTOR.getCode())
        return saaSOrderInfo
    }

    SaaSDriverInfo buildSaaSSelfDriverInfo() {
        SaaSDriverInfo saaSDriverInfo = new SaaSDriverInfo()
        saaSDriverInfo.setDriverId("111")
        saaSDriverInfo.setIsSelfDriver(1)
        return saaSDriverInfo
    }

    SaaSCarInfo buildSaaSSelfCarInfo() {
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarId(111)
        return saaSCarInfo
    }

    SaaSDriverInfo buildSaaSOtherDriverInfo() {
        SaaSDriverInfo saaSDriverInfo = new SaaSDriverInfo()
        saaSDriverInfo.setIsSelfDriver(0)
        saaSDriverInfo.setDriverLanguage("yy")
        saaSDriverInfo.setDriverMobile("15555555555")
        saaSDriverInfo.setDriverMobileCountryCode("1111")
        saaSDriverInfo.setDriverName("xiao_ming")
        saaSDriverInfo.setDriverOtherContract("other")
        return saaSDriverInfo
    }

    SaaSCarInfo buildSaaSOtherCarInfo() {
        SaaSCarInfo saaSCarInfo = new SaaSCarInfo()
        saaSCarInfo.setCarBrandId(1)
        saaSCarInfo.setCarColorId(2)
        saaSCarInfo.setCarLicense("京ACD933")
        saaSCarInfo.setCarSeriesId(3)
        saaSCarInfo.setCarTypeId(4)
        saaSCarInfo.setCarDesc("desc")
        return saaSCarInfo
    }

    SaaSSupplierInfo buildSaaSSupplierInfo() {
        SaaSSupplierInfo saaSSupplierInfo = new SaaSSupplierInfo()
        saaSSupplierInfo.setSupplierId(111L)
        saaSSupplierInfo.setSupplierName("供应商")
        return saaSSupplierInfo
    }

    SaaSOperatorInfo buildSaaSOperatorInfo() {
        SaaSOperatorInfo saaSOperatorInfo = new SaaSOperatorInfo()
        saaSOperatorInfo.setOperatorUserAccount("1111")
        saaSOperatorInfo.setOperatorUserName("操作人")
        saaSOperatorInfo.setOperatorUserType("systemUser")
        return saaSOperatorInfo
    }
}
