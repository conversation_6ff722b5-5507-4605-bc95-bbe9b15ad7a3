package executor

import com.ctrip.dcs.application.command.AllowAuthCmd
import com.ctrip.dcs.application.provider.executor.QueryReasonsAndAllowAuthExecutor
import com.ctrip.dcs.application.query.ReasonsQuery
import com.ctrip.dcs.domain.common.value.ReasonDetailVO
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryReasonsAndAllowAuthRequestType
import com.ctrip.igt.framework.common.result.Result
import org.assertj.core.util.Lists
import spock.lang.Specification

/**
 * <AUTHOR> ZhangZhen
 * @create 2023/7/20 16:16
 */
class QueryReasonsAndAllowAuthExecutorTest extends Specification {

    def allowAuthCmd = Mock(AllowAuthCmd)

    def reasonsQuery = Mock(ReasonsQuery)

    def executor = new QueryReasonsAndAllowAuthExecutor(
            allowAuthCmd: allowAuthCmd,
            reasonsQuery: reasonsQuery
    )

    def "base fail"() {
        given:
        def allowRes = Result.Builder.<Boolean> newResult().success().withMsg("22").withCode("11").build()
        allowAuthCmd.queryAllowAuth(_, _) >> allowRes
        when:
        def res = executor.execute(new QueryReasonsAndAllowAuthRequestType())
        then:
        res != null
        res.getResponseResult() != null
        res.getResponseResult().getReturnCode() == "11"
        res.getResponseResult().getReturnMessage() == "22"
    }

    def "base fail1"() {
        given:
        def allowRes = Result.Builder.<Boolean> newResult().success().withData(Boolean.TRUE).withMsg("22").withCode("11").build()
        allowAuthCmd.queryAllowAuth(_, _) >> allowRes
        def queryRes = Result.Builder.<List<ReasonDetailVO>> newResult().success().withMsg("44").withCode("33").withData(null).build()
        reasonsQuery.queryReason(_, _) >> queryRes
        when:
        def res = executor.execute(new QueryReasonsAndAllowAuthRequestType())
        then:
        res != null
        res.getResponseResult() != null
        res.getResponseResult().getReturnCode() == "33"
        res.getResponseResult().getReturnMessage() == "44"
    }

    def "base success"() {
        given:
        def allowRes = Result.Builder.<Boolean> newResult().success().withData(Boolean.TRUE).withMsg("22").withCode("11").build()
        allowAuthCmd.queryAllowAuth(_, _) >> allowRes

        def queryRes = Result.Builder.<List<ReasonDetailVO>> newResult().success().withMsg("44").withCode("33").withData(Lists.newArrayList(new ReasonDetailVO())).build()
        reasonsQuery.queryReason(_, _) >> queryRes
        when:
        def res = executor.execute(new QueryReasonsAndAllowAuthRequestType())
        then:
        res != null
        res.getResponseResult() != null
        res.getResponseResult().getReturnCode() == "200"
    }


}