package executor

import com.ctrip.dcs.application.command.SaaSCancelDspOrderExeCmd
import com.ctrip.dcs.application.provider.executor.SaasCancelDspOrderExecutor
import com.ctrip.dcs.self.dispatchorder.interfaces.SaasCancelDspOrderRequestType
import spock.lang.Specification
import spock.lang.Unroll

class SaasCancelDspOrderExecutorTest extends Specification {

    def saaSCancelDspOrderExeCmd = Mock(SaaSCancelDspOrderExeCmd)

    def executor = new SaasCancelDspOrderExecutor(
            saaSCancelDspOrderExeCmd: saaSCancelDspOrderExeCmd,

    )

    @Unroll
    def "test execute suc"() {

        given: "Mock数据"
        def request = new SaasCancelDspOrderRequestType(null, null, 1L, "222",
                "111", 1, "取消","",0)
        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true
    }


    def "test execute fail"() {
        given: "Mock数据"
        def request = new SaasCancelDspOrderRequestType(null, null, getSupplierId, getDspOrderId,
                getVbkOrderId, 1, null,"",0)
        when: "执行校验方法"
        def responseType = executor.execute(request)
        then: "验证校验结果"
        responseType.responseResult.success == false

        where:
        getSupplierId | getDspOrderId | getVbkOrderId
        null          | "1"           | "1"
        1             | null          | "1"
        1             | "1"           | null
        1             | null          | null
        1             | "1"           | "1"

    }

}
