package executor

import com.ctrip.dcs.application.command.CustomerAssignDriverExeCmd
import com.ctrip.dcs.application.provider.executor.CustomerAssignDriverExecutor
import com.ctrip.dcs.self.dispatchorder.interfaces.CustomerAssignDriverRequestType
import spock.lang.Specification
import spock.lang.Unroll

class CustomerAssignDriverExecutorTest extends Specification {

    def customerAssignDriverExeCmd = Mock(CustomerAssignDriverExeCmd)

    def executor = new CustomerAssignDriverExecutor(
            customerAssignDriverExeCmd: customerAssignDriverExeCmd,

    )

    @Unroll
    def "test execute suc"() {

        given: "Mock数据"

        def request = new CustomerAssignDriverRequestType(userOrderId: "1",dspOrderId: "1",driverId: 1,operUserName: "xxx",operUserType: "xxx",sysUserAccount: "zzz")

        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true


    }

    def "test execute fail"() {

        given: "Mock数据"


        def request = new CustomerAssignDriverRequestType(userOrderId: "1")

        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == false


    }


}
