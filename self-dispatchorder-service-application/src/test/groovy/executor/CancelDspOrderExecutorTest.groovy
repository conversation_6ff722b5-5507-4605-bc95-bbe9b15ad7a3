package executor

import com.ctrip.dcs.application.command.CancelDspOrderExeCmd
import com.ctrip.dcs.application.provider.executor.CancelDspOrderExecutor
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.CancelDspOrderResponseType
import spock.lang.Specification
import spock.lang.Unroll

class CancelDspOrderExecutorTest extends Specification {

    def cancelDspOrderExeCmd = Mock(CancelDspOrderExeCmd)
    def executor = new CancelDspOrderExecutor(
            cancelDspOrderExeCmd: cancelDspOrderExeCmd,

    )

    @Unroll
    def "test execute suc"() {

        given: "Mock数据"


        def request = new CancelDspOrderRequestType(userOrderId: "1", supplyOrderId: "11", cancelReasonId: 1)

        when: "执行校验方法"
        CancelDspOrderResponseType responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true
        1 * cancelDspOrderExeCmd.execute(_)


    }

    def "test execute fail"() {
        given: "Mock数据"
        def request = new CancelDspOrderRequestType(userOrderId: "1")
        when: "执行校验方法"
        CancelDspOrderResponseType responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == false


    }
}
