package executor

import com.ctrip.dcs.application.provider.executor.QueryReasonsExecutor
import com.ctrip.dcs.application.query.ReasonsQuery
import com.ctrip.dcs.domain.common.value.ReasonDetailVO
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryReasonsRequestType
import com.ctrip.igt.framework.common.result.Result
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants
import org.assertj.core.util.Lists
import spock.lang.Specification

/**
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/7/20 15:47
 */
class QueryReasonsExecutorTest extends Specification {

    def reasonsQuery = Mock(ReasonsQuery)
    def executor = new QueryReasonsExecutor(
            reasonsQuery: reasonsQuery
    )

    def "base success"() {
        given:
        def queryRes = Result.Builder.<List<ReasonDetailVO>> newResult().success().withData(Lists.newArrayList()).build()
        reasonsQuery.queryReason(_, _) >> queryRes
        when:
        def res = executor.execute(new QueryReasonsRequestType())
        then:
        res != null
        res.getResponseResult() != null
        res.getResponseResult().getReturnCode() == ServiceResponseConstants.ResStatus.SUCCESS_CODE
    }

    def "base success2"() {
        given:
        def reasonDetailVO = new ReasonDetailVO(responsible: 1, frontShow: 1)
        def queryRes = Result.Builder.<List<ReasonDetailVO>> newResult().success().withData(Lists.newArrayList(reasonDetailVO)).build()
        reasonsQuery.queryReason(_, _) >> queryRes
        when:
        def res = executor.execute(new QueryReasonsRequestType())
        then:
        res != null
        res.getResponseResult() != null
        res.getResponseResult().getReturnCode() == ServiceResponseConstants.ResStatus.SUCCESS_CODE
    }

    def "base fail"() {
        given:
        def queryRes = Result.Builder.<List<ReasonDetailVO>> newResult().fail().withCode("11").withMsg("22").build()
        reasonsQuery.queryReason(_, _) >> queryRes

        when:
        def res = executor.execute(new QueryReasonsRequestType())
        then:
        res != null
        res.getResponseResult() != null
        res.getResponseResult().getReturnCode() == "11"
        res.getResponseResult().getReturnMessage() == "22"
    }

}