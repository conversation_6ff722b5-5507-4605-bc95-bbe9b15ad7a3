package executor

import com.ctrip.dcs.application.command.VbkBindCarAndTakenExeCmd
import com.ctrip.dcs.application.provider.executor.VbkBindCarAndTakenExecutor
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkBindCarAndTakenRequestType
import spock.lang.Specification
import spock.lang.Unroll

class VbkBindCarAndTakenExecutorTest extends Specification {

    def vbkBindCarAndTakenExeCmd = Mock(VbkBindCarAndTakenExeCmd)

    def executor = new VbkBindCarAndTakenExecutor(
            vbkBindCarAndTakenExeCmd: vbkBindCarAndTakenExeCmd,

    )

    @Unroll
    def "test execute suc"() {

        given: "Mock数据"

        def request = new VbkBindCarAndTakenRequestType(userOrderId: "1", dspOrderId: "1", driverId: 1, operUserName: "xxx",
                operUserType: "xxx", sysUserAccount: "zzz",
                carLicense:"aaaa1",carTypeId: 117, carColor:"11",carDesc:"kulinan",supplierId: 33)

        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true


    }

    def "test execute fail"() {

        given: "Mock数据"


        def request = new VbkBindCarAndTakenRequestType(userOrderId: "1")

        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == false


    }


}
