package executor

import com.ctrip.dcs.application.command.CreateDspOrderExeCmd
import com.ctrip.dcs.application.provider.executor.CreateDspOrderExecutor
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.CreateDspOrderResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.BaseInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.CreateDspOrderInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.FeeInfo
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.ProductInfo
import spock.lang.Specification
import spock.lang.Unroll

class CreateDspOrderExecutorTest extends Specification {

    def createDspOrderExeCmd = Mock(CreateDspOrderExeCmd)

    def executor = new CreateDspOrderExecutor(
            createDspOrderExeCmd: createDspOrderExeCmd,

    )

    @Unroll
    def "test execute suc"() {

        given: "Mock数据"
        createDspOrderExeCmd.execute(_) >> "1"

        def baseInfo = new BaseInfo(userOrderId: "11", categoryCode:"airport_dropoff",cityId: 2, fromCityId: 2, toCityId: 2, vehicleGroupid: 117
                , estimatedUseTime: "2023-04-24 10:00:08",estimatedUseTimeBj:"2023-04-25 10:00:08"
                , predicServiceStopTime: "2023-04-24 10:00:08", predicServiceStopTimeBj: "2023-04-24 10:00:08"
                , lastConfirmCarTime: "2023-04-24 10:00:08", lastConfirmTimeBj: "2023-04-24 10:00:08"
                , lastConfirmCarTimeBj: "2023-04-24 10:00:08", lastConfirmTime: "2023-04-24 10:00:08"
                , useDays: 1, supplierId: 10086)

        def productInfo = new ProductInfo(skuId: 146)

        def feeInfo = new FeeInfo()

        def createDspOrderInfo = new CreateDspOrderInfo(baseInfo: baseInfo, productInfo: productInfo, feeInfo: feeInfo, orderScene: 1)


        def request = new CreateDspOrderRequestType(createDspOrderInfo: createDspOrderInfo)


        when: "执行校验方法"
        CreateDspOrderResponseType responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.dspOrderId == "1"


    }

    def "test execute fail"() {

        given: "Mock数据"
        createDspOrderExeCmd.execute(_) >> 1

        def createDspOrderInfo = new CreateDspOrderInfo()

        def request = new CreateDspOrderRequestType(createDspOrderInfo: createDspOrderInfo)

        when: "执行校验方法"
        CreateDspOrderResponseType responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == false


    }
}
