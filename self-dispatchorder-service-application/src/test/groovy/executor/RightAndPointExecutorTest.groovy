package executor


import com.ctrip.dcs.application.command.RightAndPointCmd
import com.ctrip.dcs.application.provider.executor.RightAndPointExecutor
import com.ctrip.dcs.domain.common.value.RightAndPointVO
import com.ctrip.dcs.self.dispatchorder.interfaces.RightAndPointRequestType
import com.ctrip.igt.framework.common.result.Result
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants
import spock.lang.Specification

/**
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/7/20 16:12
 */
class RightAndPointExecutorTest extends Specification {

    def rightAndPointCmd = Mock(RightAndPointCmd)

    def executor = new RightAndPointExecutor(
            rightAndPointCmd: rightAndPointCmd,
    )

    def "base success"() {
        given:
        def rightAndPointRes = Result.Builder.<RightAndPointVO> newResult().success().withData(new RightAndPointVO()).build()
        rightAndPointCmd.execute(_) >> rightAndPointRes
        when:
        def res = executor.execute(new RightAndPointRequestType())
        then:
        res != null
        res.getResponseResult() != null
        res.getResponseResult().getReturnCode() == ServiceResponseConstants.ResStatus.SUCCESS_CODE
    }

    def "base fail"() {
        given:
        def rightAndPointRes = Result.Builder.<RightAndPointVO> newResult().fail().withCode("11").withMsg("22").build()
        rightAndPointCmd.execute(_) >> rightAndPointRes
        when:
        def res = executor.execute(new RightAndPointRequestType())
        then:
        res != null
        res.getResponseResult() != null
        res.getResponseResult().getReturnCode() == "11"
        res.getResponseResult().getReturnMessage() == "22"
    }

}