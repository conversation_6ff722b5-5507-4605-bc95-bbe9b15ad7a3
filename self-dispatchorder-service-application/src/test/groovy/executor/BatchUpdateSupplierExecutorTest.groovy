package executor


import com.ctrip.dcs.application.command.tool.BatchUpdateSupplierExeCmd
import com.ctrip.dcs.application.provider.executor.BatchUpdateSupplierExecutor
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchUpdateSupplierRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchUpdateSupplierResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.SupplierInfo
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils
import com.ctrip.igt.framework.soa.server.validator.ValidationInterceptor
import com.ctrip.igt.framework.soa.server.validator.ValidatorFactory
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

class BatchUpdateSupplierExecutorTest extends Specification {

    def batchUpdateSupplierExeCmd = Mock(BatchUpdateSupplierExeCmd)
    def executor = new BatchUpdateSupplierExecutor(
            batchUpdateSupplierExeCmd: batchUpdateSupplierExeCmd
    )

    @Unroll
    def "test execute success"() {
        given: "Mock data"
        def request = new BatchUpdateSupplierRequestType(supplierInfoList: [new SupplierInfo(supplierId: 1, userOrderId: "1", skuId: 1, spId: 1, transportGroupId: 1)])
        batchUpdateSupplierExeCmd.execute(request) >> ServiceResponseUtils.success(new BatchUpdateSupplierResponseType())

        when: "Execute method"
        BatchUpdateSupplierResponseType response = executor.execute(request)

        then: "Verify result"
        response.responseResult.success == true
    }

    @Unroll
    def "test execute with BizException"() {
        given: "Mock data"
        def request = new BatchUpdateSupplierRequestType(supplierInfoList: [new SupplierInfo(supplierId: 1, userOrderId: "1", skuId: 1, spId: 1, transportGroupId: 1)])
        batchUpdateSupplierExeCmd.execute(request) >> { throw new BizException("ERROR_CODE", "Error message") }

        when: "Execute method"
        BatchUpdateSupplierResponseType response = executor.execute(request)

        then: "Verify result"
        response.responseResult.success == false
        response.responseResult.returnCode == 'ERROR_CODE'
        response.responseResult.returnMessage == 'Error message'
    }

    @Unroll
    def "test execute with Exception"() {
        given: "Mock data"
        def request = new BatchUpdateSupplierRequestType(supplierInfoList: [new SupplierInfo(supplierId: 1, userOrderId: "1", skuId: 1, spId: 1, transportGroupId: 1)])

        when: "Execute method"
        BatchUpdateSupplierResponseType response = executor.execute(request)

        then: "Verify result"
        response.responseResult.success == false
        1 * batchUpdateSupplierExeCmd.execute(request) >> { throw new Exception("Unexpected error") }
    }

    @Unroll
    def "validate should throw BizException for invalid input"() {
        given:
        def request = new BatchUpdateSupplierRequestType(supplierInfoList: supplierInfoList)
        def validatorFactory = Spy(ValidatorFactory) {}
        def map = Whitebox.getInternalState(ValidatorFactory, 'VALIDATOR_MAPS')
        map.putAt(BatchUpdateSupplierRequestType, executor)
        def v = new ValidationInterceptor(validatorFactory)

        when:
        v.handle(executor, request)

        then:
        def ex = thrown(RuntimeException)
        ex.message == expectedMessage

        where:
        supplierInfoList                                                             || expectedMessage
        null                                                                         || "'supplierInfoList' 必须不能为null."
        []                                                                           || "'supplierInfoList' 不能为空字符串."
        [new SupplierInfo(supplierId: -1, userOrderId: "order1", skuId: 1, spId: 1)] || "supplierId must be positive integer"
        [new SupplierInfo(supplierId: 1, userOrderId: "", skuId: 1, spId: 1)]        || "userOrderId must not null or empty"
        [new SupplierInfo(supplierId: 1, userOrderId: "order1", skuId: -1, spId: 1)] || "skuId must be positive integer"
        [new SupplierInfo(supplierId: 1, userOrderId: "order1", skuId: 1, spId: -1)] || "spId must be positive integer"
        [new SupplierInfo(supplierId: 1, userOrderId: "order1", skuId: 1, spId: 2)] || "transportGroupId must be positive integer"
    }

}
