package executor

import com.ctrip.dcs.application.command.VbkBindCarAndTakenExeCmd
import com.ctrip.dcs.application.command.VbkChangeDriverExeCmd
import com.ctrip.dcs.application.provider.executor.VbkChangeDriverExecutor
import com.ctrip.dcs.self.dispatchorder.interfaces.VbkChangeDriverRequestType
import spock.lang.Specification
import spock.lang.Unroll

class VbkChangeDriverExecutorTest extends Specification {

    def vbkChangeDriverExeCmd = Mock(VbkChangeDriverExeCmd)
    def vbkBindCarAndTakenExeCmd = Mock(VbkBindCarAndTakenExeCmd)

    def executor = new VbkChangeDriverExecutor(
            vbkChangeDriverExeCmd: vbkChangeDriverExeCmd,
            vbkBindCarAndTakenExeCmd: vbkBindCarAndTakenExeCmd
    )

    @Unroll
    def "test execute1 suc"() {
        given: "Mock数据"
        def request = new VbkChangeDriverRequestType(userOrderId: "1", dspOrderId: "1", driverId: 1, operUserName: "xxx",
                operUserType: "xxx", sysUserAccount: "zzz",
                transportGroupId: 1, supplierId: 33)
        vbkChangeDriverExeCmd.isOnlyUpdateCar(_,_,_) >> true
        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true
    }

    @Unroll
    def "test execute2 suc"() {
        given: "Mock数据"
        def request = new VbkChangeDriverRequestType(userOrderId: "1", dspOrderId: "1", driverId: 1, operUserName: "xxx",
                operUserType: "xxx", sysUserAccount: "zzz",
                transportGroupId: 1, supplierId: 33)
        vbkChangeDriverExeCmd.isOnlyUpdateCar(_,_,_) >> false
        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == true
    }

    def "test execute fail"() {

        given: "Mock数据"


        def request = new VbkChangeDriverRequestType(userOrderId: "1")

        when: "执行校验方法"
        def responseType = executor.execute(request)

        then: "验证校验结果"
        responseType.responseResult.success == false


    }


}
