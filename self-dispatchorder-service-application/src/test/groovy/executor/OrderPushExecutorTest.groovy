package executor

import com.ctrip.dcs.application.provider.executor.OrderPushExecutor
import com.ctrip.dcs.infrastructure.adapter.soa.TourVendorNoticeServiceProxy
import com.ctrip.dcs.infrastructure.common.util.ResponseResultUtil
import com.ctrip.dcs.self.dispatchorder.interfaces.OrderPushRequestType
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.tour.vendor.noticesvc.soa.v1.service.type.SendNoticeResponseType
import com.ctriposs.baiji.rpc.server.plugin.validation.RequestValidator
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> ZhangZhen
 * @create 2024/11/21 17:09
 */
class OrderPushExecutorTest extends Specification {

    def tourVendorNoticeServiceProxy = Mock(TourVendorNoticeServiceProxy)

    def executor = new OrderPushExecutor(tourVendorNoticeServiceProxy: tourVendorNoticeServiceProxy)

    @Unroll
    def "test fail suc"() {

        given:
        def sendNoticeResponse = Mock(SendNoticeResponseType)
        tourVendorNoticeServiceProxy.sendNotice(_) >> sendNoticeResponse

        when:
        def response = executor.execute(new OrderPushRequestType())

        then:
        response != null
        !ResponseResultUtil.checkResponseResult(response)
    }

    @Unroll
    def "test success suc"() {

        given:
        def sendNoticeResponse = Mock(SendNoticeResponseType)
        sendNoticeResponse.getNoticeId() >> "3333333"
        tourVendorNoticeServiceProxy.sendNotice(_) >> sendNoticeResponse

        when:
        def response = executor.execute(new OrderPushRequestType())

        then:
        response != null
        ResponseResultUtil.checkResponseResult(response)
    }

//    def "test validate"() {
//        given:
//        AbstractValidator<OrderPushRequestType> validator = new RequestValidator(OrderPushRequestType.class)
//        OrderPushRequestType req = new OrderPushRequestType()
//        req.bizType = 1
//        req.noticeId = UUID.randomUUID().toString()
//        when:
//        executor.validate(validator, req)
//        then:
//        notThrown(BizException)
//    }

}