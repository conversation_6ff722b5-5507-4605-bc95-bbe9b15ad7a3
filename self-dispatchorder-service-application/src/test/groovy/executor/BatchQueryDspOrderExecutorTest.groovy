package executor

import com.ctrip.dcs.domain.dsporder.entity.tool.SupplierInfoDO
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.application.provider.executor.BatchQueryDspOrderExecutor
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchQueryDspOrderRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchQueryDspOrderResponseType
import com.ctrip.dcs.self.dispatchorder.interfaces.BatchUpdateSupplierRequestType
import com.ctrip.dcs.self.dispatchorder.interfaces.DspOrderBasic
import com.ctrip.dcs.self.dispatchorder.interfaces.SupplierInfo
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.igt.framework.soa.server.validator.ValidationInterceptor
import com.ctrip.igt.framework.soa.server.validator.ValidatorFactory
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

class BatchQueryDspOrderExecutorTest extends Specification {

    def dspOrderRepository = Mock(DspOrderRepository)
    def validator = Mock(AbstractValidator)
    def executor = new BatchQueryDspOrderExecutor(
            dspOrderRepository: dspOrderRepository
    )

    @Unroll
    def "test execute success"() {
        given: "Mock repository response"
        def request = new BatchQueryDspOrderRequestType(userOrderIdList: ["ORDER1", "ORDER2"])
        def supplierInfo = new SupplierInfoDO(
                userOrderId: "ORDER1",
                spId: 100,
                supplierId: 200,
                orderStatus: 1,
                dspOrderId: "DSP1"
        )
        dspOrderRepository.batchQueryDspOrdersByUserOrderIds(request.getUserOrderIdList())

        when: "Execute request"
        def response = executor.execute(request)

        then: "Verify response"
        response.responseResult.success
        response.dspOrderBasicList.size() == 1
        with(response.dspOrderBasicList[0]) {
            userOrderId == "ORDER1"
            spId == 100
            supplierId == 200
            orderStatus == 1
            dspOrderId == "DSP1"
        }
        1 * dspOrderRepository.batchQueryDspOrdersByUserOrderIds(_) >> [supplierInfo]
    }

    def "test execute with BizException"() {
        given: "Mock repository throws BizException"
        def request = new BatchQueryDspOrderRequestType(userOrderIdList: ["ORDER1"])
        dspOrderRepository.batchQueryDspOrdersByUserOrderIds(_) >> { throw new BizException("ERROR_CODE", "Error message") }

        when: "Execute request"
        def response = executor.execute(request)

        then: "Verify error response"
        !response.responseResult.success
        response.responseResult.returnCode == "ERROR_CODE"
        response.responseResult.returnMessage == "Error message"
    }

    def "test execute with unexpected exception"() {
        given: "Mock repository throws Exception"
        def request = new BatchQueryDspOrderRequestType(userOrderIdList: ["ORDER1"])
        dspOrderRepository.batchQueryDspOrdersByUserOrderIds(_) >> { throw new RuntimeException("Unexpected error") }

        when: "Execute request"
        def response = executor.execute(request)

        then: "Verify error response"
        !response.responseResult.success
    }

    def "test transform with null supplier"() {
        when: "Transform null supplier"
        def result = executor.transform(null)

        then: "Result should be null"
        result == null
    }

    def "test transform with valid supplier"() {
        given: "Valid supplier info"
        def supplier = new SupplierInfoDO(
                userOrderId: "ORDER1",
                spId: 100,
                supplierId: 200,
                orderStatus: 1,
                dspOrderId: "DSP1"
        )

        when: "Transform supplier"
        def result = executor.transform(supplier)

        then: "Verify transformed object"
        result.userOrderId == "ORDER1"
        result.spId == 100
        result.supplierId == 200
        result.orderStatus == 1
        result.dspOrderId == "DSP1"
    }

    def "test validate with invalid request"() {
        given: "Invalid request"
        validator.ruleFor("userOrderIdList") >> { throw new BizException("INVALID_REQUEST") }

        when: "Validate request"
        executor.validate(validator)

        then: "Validation fails"
        thrown(RuntimeException)
    }

    @Unroll
    def "validate should throw BizException for invalid input"() {
        given:
        def request = new BatchQueryDspOrderRequestType(userOrderIdList: userOrderIdList)
        def validatorFactory = Spy(ValidatorFactory) {}
        def map = Whitebox.getInternalState(ValidatorFactory, 'VALIDATOR_MAPS')
        map.putAt(BatchQueryDspOrderRequestType, executor)
        def v = new ValidationInterceptor(validatorFactory)

        when:
        v.handle(executor, request)

        then:
        def ex = thrown(RuntimeException)
        ex.message == expectedMessage

        where:
        userOrderIdList                                                             || expectedMessage
        null                                                                         || "'userOrderIdList' 必须不能为null."
        []                                                                           || "'userOrderIdList' 不能为空字符串."
    }
}
