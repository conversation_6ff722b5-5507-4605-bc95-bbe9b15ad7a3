<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>self-dispatchorder-service</artifactId>
        <groupId>com.ctrip.dcs</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>1.0.0</version>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <artifactId>self-dispatchorder-service-application</artifactId>

    <name>self-dispatchorder-service-application</name>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.dcs.dispatchorder</groupId>
            <artifactId>self-dispatchorder-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.self</groupId>
            <artifactId>self-orderquery-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.di.data</groupId>
            <artifactId>abtestclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs</groupId>
            <artifactId>self-dispatchorder-service-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs</groupId>
            <artifactId>self-dispatchorder-service-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.arch</groupId>
            <artifactId>distlock-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>soa-server</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework</groupId>
            <artifactId>canal-json</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.order.payment</groupId>
            <artifactId>order-payment-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.settlement</groupId>
            <artifactId>rhb-settlement-service-mq</artifactId>
        </dependency>
    </dependencies>
</project>
